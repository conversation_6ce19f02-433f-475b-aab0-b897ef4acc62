from django.db.models import Q, Count, Avg
from blog.models import Post, Comment
from django.contrib.auth import get_user_model
from django.conf import settings
import re

# Import PostgreSQL search functions only if using PostgreSQL
try:
    if 'postgresql' in settings.DATABASES['default']['ENGINE']:
        from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank
        POSTGRES_SEARCH_AVAILABLE = True
    else:
        POSTGRES_SEARCH_AVAILABLE = False
except (ImportError, KeyError):
    POSTGRES_SEARCH_AVAILABLE = False

User = get_user_model()

class AdvancedSearchService:
    """Advanced search functionality with filters and ranking"""
    
    @staticmethod
    def search_posts(query, filters=None, user=None):
        """
        Advanced post search with multiple filters
        
        Args:
            query (str): Search query
            filters (dict): Search filters
            user (User): Current user for personalization
        
        Returns:
            QuerySet: Filtered and ranked posts
        """
        if not query and not filters:
            return Post.objects.none()
        
        # Start with all published posts
        posts = Post.objects.filter(status='published')
        
        # Apply text search if query provided
        if query:
            # Use PostgreSQL full-text search if available
            if POSTGRES_SEARCH_AVAILABLE:
                try:
                    from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank
                    search_vector = SearchVector('title', weight='A') + \
                                   SearchVector('content', weight='B')
                    search_query = SearchQuery(query)

                    posts = posts.annotate(
                        search=search_vector,
                        rank=SearchRank(search_vector, search_query)
                    ).filter(search=search_query).order_by('-rank', '-created_at')

                except Exception:
                    # Fallback to basic search
                    posts = posts.filter(
                        Q(title__icontains=query) |
                        Q(content__icontains=query)
                    ).order_by('-created_at')
            else:
                # Use basic search for SQLite and other databases
                posts = posts.filter(
                    Q(title__icontains=query) |
                    Q(content__icontains=query)
                ).order_by('-created_at')
        
        # Apply filters
        if filters:
            # Category filter
            if filters.get('category'):
                posts = posts.filter(category__name__iexact=filters['category'])
            
            # Author filter
            if filters.get('author'):
                posts = posts.filter(author__username__icontains=filters['author'])
            
            # Date range filter
            if filters.get('date_from'):
                posts = posts.filter(created_at__gte=filters['date_from'])
            if filters.get('date_to'):
                posts = posts.filter(created_at__lte=filters['date_to'])
            
            # Reading time filter
            if filters.get('reading_time_min'):
                posts = posts.filter(reading_time__gte=filters['reading_time_min'])
            if filters.get('reading_time_max'):
                posts = posts.filter(reading_time__lte=filters['reading_time_max'])
            
            # Engagement filters
            if filters.get('min_likes'):
                posts = posts.filter(like_count__gte=filters['min_likes'])
            if filters.get('min_comments'):
                posts = posts.annotate(
                    comment_count=Count('comments')
                ).filter(comment_count__gte=filters['min_comments'])
            
            # Content type filters
            if filters.get('has_media'):
                if filters['has_media']:
                    posts = posts.exclude(featured_image='')
                else:
                    posts = posts.filter(featured_image='')
            
            # Sorting
            sort_by = filters.get('sort_by', 'relevance')
            if sort_by == 'newest':
                posts = posts.order_by('-created_at')
            elif sort_by == 'oldest':
                posts = posts.order_by('created_at')
            elif sort_by == 'most_liked':
                posts = posts.order_by('-like_count')
            elif sort_by == 'most_commented':
                posts = posts.annotate(
                    comment_count=Count('comments')
                ).order_by('-comment_count')
            elif sort_by == 'reading_time':
                posts = posts.order_by('reading_time')
        
        return posts.distinct()
    
    @staticmethod
    def search_users(query, filters=None):
        """Search users with filters"""
        if not query:
            return User.objects.none()
        
        users = User.objects.filter(
            Q(username__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query)
        )
        
        if filters:
            # Verified users only
            if filters.get('verified_only'):
                users = users.filter(profile__is_verified=True)
            
            # Users with posts
            if filters.get('has_posts'):
                users = users.filter(posts__isnull=False).distinct()
        
        return users.order_by('username')
    
    @staticmethod
    def get_search_suggestions(query, limit=5):
        """Get search suggestions based on partial query"""
        if len(query) < 2:
            return []
        
        suggestions = []
        
        # Post title suggestions
        post_titles = Post.objects.filter(
            title__icontains=query,
            status='published'
        ).values_list('title', flat=True)[:limit]
        
        suggestions.extend([
            {'type': 'post', 'text': title, 'query': title}
            for title in post_titles
        ])
        
        # Author suggestions
        authors = User.objects.filter(
            username__icontains=query
        ).values_list('username', flat=True)[:limit]
        
        suggestions.extend([
            {'type': 'author', 'text': f'Posts by {username}', 'query': f'author:{username}'}
            for username in authors
        ])
        
        return suggestions[:limit]
    
    @staticmethod
    def get_trending_searches(limit=10):
        """Get trending search terms (mock implementation)"""
        # In a real implementation, this would analyze search logs
        return [
            'artificial intelligence',
            'web development',
            'machine learning',
            'python programming',
            'react tutorial',
            'data science',
            'blockchain',
            'mobile development',
            'cloud computing',
            'cybersecurity'
        ][:limit]
    
    @staticmethod
    def extract_search_filters(query):
        """Extract filters from search query"""
        filters = {}
        
        # Extract author filter (author:username)
        author_match = re.search(r'author:(\w+)', query)
        if author_match:
            filters['author'] = author_match.group(1)
            query = re.sub(r'author:\w+', '', query).strip()
        
        # Extract category filter (category:name)
        category_match = re.search(r'category:(\w+)', query)
        if category_match:
            filters['category'] = category_match.group(1)
            query = re.sub(r'category:\w+', '', query).strip()
        
        # Extract date filter (date:YYYY-MM-DD)
        date_match = re.search(r'date:(\d{4}-\d{2}-\d{2})', query)
        if date_match:
            filters['date_from'] = date_match.group(1)
            query = re.sub(r'date:\d{4}-\d{2}-\d{2}', '', query).strip()
        
        return query.strip(), filters
    
    @staticmethod
    def get_search_analytics(user=None):
        """Get search analytics for admin or user"""
        # Mock implementation - in real app, this would analyze search logs
        return {
            'total_searches': 1250,
            'unique_searchers': 340,
            'average_results_per_search': 8.5,
            'top_search_terms': [
                {'term': 'python', 'count': 45},
                {'term': 'javascript', 'count': 38},
                {'term': 'react', 'count': 32},
                {'term': 'django', 'count': 28},
                {'term': 'machine learning', 'count': 25},
            ],
            'search_success_rate': 87.3,  # Percentage of searches with clicks
        }

class SmartRecommendationService:
    """AI-powered content recommendation system"""
    
    @staticmethod
    def get_personalized_recommendations(user, limit=10):
        """Get personalized post recommendations for user"""
        if not user or not user.is_authenticated:
            return SmartRecommendationService.get_trending_posts(limit)

        # For now, return trending posts
        # In a real implementation, this would use user's reading history
        return SmartRecommendationService.get_trending_posts(limit)
    
    @staticmethod
    def get_trending_posts(limit=10):
        """Get trending posts based on engagement"""
        return Post.objects.filter(
            status='published'
        ).annotate(
            total_likes=Count('likes')
        ).order_by('-total_likes', '-created_at')[:limit]
    
    @staticmethod
    def get_similar_posts(post, limit=5):
        """Get posts similar to the given post"""
        if not post.category:
            return Post.objects.none()
        
        return Post.objects.filter(
            category=post.category,
            status='published'
        ).exclude(id=post.id).order_by('-created_at')[:limit]
    
    @staticmethod
    def get_author_recommendations(author, limit=5):
        """Get recommended posts from the same author"""
        return Post.objects.filter(
            author=author,
            status='published'
        ).order_by('-created_at')[:limit]
