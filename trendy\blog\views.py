from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
import uuid
from rest_framework import viewsets, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from .models import Category, HeroImage, Post, Comment, PostMedia, Tag, Newsletter
from .serializers import CategorySerializer, PostSerializer, CommentSerializer, UserSerializer
from .forms import UserRegistrationForm, UserProfileForm, PostForm, CommentForm, NewsletterForm
from django.utils.text import slugify
from django.contrib.auth import get_user_model

User = get_user_model()
from django.views.generic import ListView, DetailView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.urls import reverse_lazy
from django.views.generic.edit import CreateView, UpdateView, DeleteView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.db.models import Prefetch

from rest_framework import status

# Regional content filtering
from regional.services import RegionalContentService
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.authtoken.models import Token
from .serializers import UserRegistrationSerializer, LoginSerializer, LikeSerializer
from django.db.models import F
from django.contrib.contenttypes.models import ContentType

from blog import serializers
from rest_framework import viewsets
from rest_framework.pagination import PageNumberPagination


class StandardPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100


class RegistrationAPIView(APIView):
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            token, created = Token.objects.get_or_create(user=user)
            return Response({
                'user': UserSerializer(user).data,
                'token': token.key
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class LoginAPIView(APIView):
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data
            token, created = Token.objects.get_or_create(user=user)
            return Response({
                'user': UserSerializer(user).data,
                'token': token.key
            }, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


def home(request):
    posts = Post.objects.select_related('author').prefetch_related(
            Prefetch('media_items', queryset=PostMedia.objects.order_by('order')),
            'target_countries'
        ).filter(status='published').order_by('-created_at')

    # Apply regional filtering if user is authenticated
    if request.user.is_authenticated:
        posts = RegionalContentService.filter_posts_by_region(posts, request.user, request)

    featured_posts = Post.objects.filter(
            is_featured=True,
            status='published'
        ).prefetch_related('media_items', 'target_countries').order_by('-created_at')

    # Apply regional filtering to featured posts as well
    if request.user.is_authenticated:
        featured_posts = RegionalContentService.filter_posts_by_region(featured_posts, request.user, request)

    featured_posts = featured_posts[:3]
    categories = Category.objects.all()
    tags = Tag.objects.all()
    
    # Search functionality
    query = request.GET.get('q')
    if query:
        posts = posts.filter(
            Q(title__icontains=query) |
            Q(content__icontains=query) |
            Q(tags__name__icontains=query)
        ).distinct()
    
    # Category filter
    category_slug = request.GET.get('category')
    if category_slug:
        posts = posts.filter(category__slug=category_slug)
    
    # Tag filter
    tag_slug = request.GET.get('tag')
    if tag_slug:
        posts = posts.filter(tags__slug=tag_slug)
        
    featured_author = get_user_model().objects.filter(
        is_staff=True
    ).order_by('?').first()
    
    hero_images = HeroImage.objects.filter(is_active=True).order_by('-created_at')

    
    paginator = Paginator(posts, 9)
    page = request.GET.get('page')
    posts = paginator.get_page(page)
    
    return render(request, 'blog/home.html', {
        'posts': posts,
        'featured_posts': featured_posts,
        'categories': categories,
        'tags': tags,
        'query': query,
        'featured_author': featured_author,
        'hero_images': hero_images,
        'category_slug': category_slug,
        'tag_slug': tag_slug,
    })
    
def author_posts(request, username):
    User = get_user_model()
    author = get_object_or_404(User, username=username)
    posts = Post.objects.filter(author=author, status='published').order_by('-created_at')
    return render(request, 'blog/author_posts.html', {
        'author': author,
        'posts': posts
    })

def post_detail(request, slug):
    post = get_object_or_404(
            Post.objects.prefetch_related('media_items', 'tags'),
            slug=slug,
            status='published'
        )
    Post.objects.filter(pk=post.pk).update(views=F('views') + 1)
    post.refresh_from_db()  # Reload to get updated value
    
    comments = post.comments.filter(parent=None).prefetch_related('replies')
    related_posts = Post.objects.filter(
            category=post.category,
            status='published'
        ).exclude(id=post.id).prefetch_related('media_items')[:3]    
    if request.method == 'POST' and request.user.is_authenticated:
        comment_form = CommentForm(request.POST)
        if comment_form.is_valid():
            comment = comment_form.save(commit=False)
            comment.post = post
            comment.author = request.user
            # Handle parent comment
            parent_id = request.POST.get('parent')
            if parent_id:
                comment.parent = Comment.objects.get(id=parent_id)
            comment.save()
            messages.success(request, 'Comment added successfully!')
            return redirect('post-detail', slug=slug)
    else:
        comment_form = CommentForm()
    
    return render(request, 'blog/post_detail.html', {
        'post': post,
        'comments': comments,
        'comment_form': comment_form,
        'related_posts': related_posts,
    })

@login_required
def add_comment(request, slug):
    post = get_object_or_404(Post, slug=slug)
    if request.method == 'POST':
        comment_form = CommentForm(request.POST)
        if comment_form.is_valid():
            comment = comment_form.save(commit=False)
            comment.post = post
            comment.author = request.user
            # Handle parent comment
            parent_id = request.POST.get('parent')
            if parent_id:
                comment.parent = Comment.objects.get(id=parent_id)
            comment.save()
            messages.success(request, 'Comment added successfully!')
    return redirect('post-detail', slug=slug)

def register(request):
    if request.method == 'POST':
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()

            # Different message for admin users
            if user.is_staff or user.is_superuser:
                messages.success(request, 'Admin account created successfully! You can login immediately.')
            else:
                messages.success(request, 'Account created successfully! Please check your email to verify your account before logging in.')

            return redirect('login')
    else:
        form = UserRegistrationForm()
    return render(request, 'blog/register.html', {'form': form})

@login_required
def profile(request):
    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('profile')
    else:
        form = UserProfileForm(instance=request.user)
    
    # Get user's posts
    user_posts = Post.objects.filter(author=request.user, status='published').order_by('-created_at')
    paginator = Paginator(user_posts, 10)
    page = request.GET.get('page')
    posts = paginator.get_page(page)
    return render(request, 'blog/profile.html', {
        'form': form,
        'user_posts': user_posts
    })

@login_required
def edit_profile(request):
    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('profile')
    else:
        form = UserProfileForm(instance=request.user)
    return render(request, 'blog/edit_profile.html', {'form': form})

@login_required
def like_post(request, slug):
    post = get_object_or_404(Post, slug=slug)
    if request.user in post.likes.all():
        post.likes.remove(request.user)
    else:
        post.likes.add(request.user)
    return redirect('post-detail', slug=slug)

@login_required
def like_comment(request, comment_id):
    comment = get_object_or_404(Comment, id=comment_id)
    if request.user in comment.likes.all():
        comment.likes.remove(request.user)
    else:
        comment.likes.add(request.user)
    return redirect('post-detail', slug=comment.post.slug)

def subscribe_newsletter(request):
    if request.method == 'POST':
        form = NewsletterForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            Newsletter.objects.get_or_create(email=email)
            messages.success(request, 'Successfully subscribed to newsletter!')
        else:
            messages.error(request, 'Invalid email address.')
    return redirect('home')



# class PostSerializer(serializers.ModelSerializer):
#     like_count = serializers.SerializerMethodField()
#     is_liked = serializers.SerializerMethodField()
    
#     def get_like_count(self, obj):
#         return obj.likes.count()
    
#     def get_is_liked(self, obj):
#         user = self.context['request'].user
#         return user.is_authenticated and obj.likes.filter(id=user.id).exists()
    
    # ... other methods ...


# Import custom permissions
from .permissions import (
    IsAuthorOrReadOnly,
    CanCreateContent,
    IsContentCreatorOrReadOnly,
    CanModerateContent
)

# API Views
class CategoryViewSet(viewsets.ModelViewSet):
    queryset = Category.objects.all().order_by('-created_at')
    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    pagination_class = StandardPagination

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        Categories should be readable by everyone, but only moderators can create/edit/delete.
        """
        if self.action in ['list', 'retrieve']:
            # Anyone can read categories
            permission_classes = [permissions.AllowAny]
        elif self.action in ['create', 'update', 'partial_update', 'destroy']:
            # Only moderators can modify categories
            permission_classes = [CanModerateContent]
        else:
            permission_classes = [permissions.IsAuthenticatedOrReadOnly]

        return [permission() for permission in permission_classes]

    
    
from django.http import JsonResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticatedOrReadOnly

@api_view(['GET'])
@permission_classes([IsAuthenticatedOrReadOnly])
def posts_list_api(request):
    """Custom API view for posts to avoid serialization issues"""
    try:
        posts = Post.objects.select_related('author', 'category').prefetch_related('tags', 'media_items', 'target_countries').order_by('-created_at')

        # Apply regional filtering if user is authenticated
        if request.user.is_authenticated:
            # Check for query parameters to override user preferences
            country_code = request.GET.get('country')
            show_global = request.GET.get('show_global')

            if country_code or show_global is not None:
                # Apply custom filtering based on query parameters
                from django.db.models import Q
                from regional.models import Country

                filters = Q()

                # Handle show_global parameter
                if show_global and show_global.lower() == 'true':
                    filters |= Q(is_global=True)

                # Handle country parameter
                if country_code:
                    try:
                        country = Country.objects.get(code=country_code.upper(), is_active=True)
                        filters |= Q(target_countries=country)
                    except Country.DoesNotExist:
                        pass

                # If no filters were applied, show global content by default
                if not filters:
                    filters = Q(is_global=True)

                posts = posts.filter(filters).distinct()
            else:
                # Use default regional filtering
                posts = RegionalContentService.filter_posts_by_region(posts, request.user, request)

        posts_data = []
        for post in posts:
            # Manually serialize each post
            post_data = {
                'id': post.id,
                'title': post.title,
                'slug': post.slug,
                'content': post.content,
                'author': {
                    'id': post.author.id,
                    'username': post.author.username,
                    'email': post.author.email,
                    'first_name': post.author.first_name,
                    'last_name': post.author.last_name,
                },
                'category': {
                    'id': post.category.id,
                    'name': post.category.name,
                    'slug': post.category.slug,
                    'description': post.category.description,
                    'created_at': post.category.created_at.isoformat(),
                    'updated_at': post.category.updated_at.isoformat(),
                },
                'tags': [
                    {
                        'id': tag.id,
                        'name': tag.name,
                        'slug': tag.slug,
                    }
                    for tag in post.tags.all()
                ],
                'created_at': post.created_at.isoformat(),
                'updated_at': post.updated_at.isoformat(),
                'views': post.views,
                'is_featured': post.is_featured,
                'status': post.status,
                'reference': post.reference,
                'comment_count': post.comments.count(),
                'like_count': post.likes.count(),
                'is_liked': post.likes.filter(id=request.user.id).exists() if request.user.is_authenticated else False,
                'media_items': [
                    {
                        'id': media.id,
                        'media_type': media.media_type,
                        'image': media.image.url if media.image else None,
                        'image_url': media.image_url,
                        'video': media.video.url if media.video else None,
                        'video_url': media.video_url,
                        'thumbnail': media.thumbnail.url if media.thumbnail else None,
                        'caption': media.caption,
                        'title': media.title,
                        'description': media.description,
                        'order': media.order,
                        'created_at': media.created_at.isoformat(),
                    }
                    for media in post.media_items.all()
                ],
            }
            posts_data.append(post_data)

        return JsonResponse({
            'count': len(posts_data),
            'next': None,
            'previous': None,
            'results': posts_data
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def post_detail_api(request, pk):
    """Custom API view to get individual post by ID or slug"""
    try:
        # Try to get post by ID first, then by slug
        try:
            if pk.isdigit():
                post = get_object_or_404(Post, id=int(pk), status='published')
            else:
                post = get_object_or_404(Post, slug=pk, status='published')
        except:
            return JsonResponse({'error': 'Post not found'}, status=404)

        # Check if user is authenticated for like status
        is_liked = False
        if request.user.is_authenticated:
            is_liked = post.likes.filter(id=request.user.id).exists()

        # Serialize the post data
        post_data = {
            'id': post.id,
            'title': post.title,
            'slug': post.slug,
            'content': post.content,
            'author': {
                'id': post.author.id,
                'username': post.author.username,
                'email': post.author.email,
                'first_name': post.author.first_name,
                'last_name': post.author.last_name,
            },
            'category': {
                'id': post.category.id,
                'name': post.category.name,
                'slug': post.category.slug,
                'description': post.category.description,
                'created_at': post.category.created_at.isoformat(),
                'updated_at': post.category.updated_at.isoformat(),
            },
            'tags': [
                {
                    'id': tag.id,
                    'name': tag.name,
                    'slug': tag.slug,
                }
                for tag in post.tags.all()
            ],
            'created_at': post.created_at.isoformat(),
            'updated_at': post.updated_at.isoformat(),
            'views': post.views,
            'is_featured': post.is_featured,
            'status': post.status,
            'reference': [],
            'comment_count': post.comments.count(),
            'like_count': post.likes.count(),
            'is_liked': is_liked,
            'media_items': [
                {
                    'id': media.id,
                    'media_type': media.media_type,
                    'image': media.image.url if media.image else None,
                    'image_url': media.image_url,
                    'image_url_full': request.build_absolute_uri(media.image.url) if media.image else media.image_url,
                    'video': media.video.url if media.video else None,
                    'video_url': media.video_url,
                    'thumbnail': media.thumbnail.url if media.thumbnail else None,
                    'caption': media.caption,
                    'title': media.title,
                    'description': media.description,
                    'order': media.order,
                    'created_at': media.created_at.isoformat(),
                }
                for media in post.media_items.all().order_by('order', 'created_at')
            ],
        }

        return JsonResponse(post_data)

    except Exception as e:
        print(f"Error in post_detail_api: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def increment_post_view_api(request, post_id):
    """Custom API view to increment post view count"""
    try:
        post = get_object_or_404(Post, id=post_id)
        old_views = post.views
        Post.objects.filter(pk=post.pk).update(views=F('views') + 1)
        post.refresh_from_db()
        print(f"View count updated for post {post.pk}: {old_views} -> {post.views}")

        # Note: Reading points are now awarded through a separate endpoint
        # after user has spent sufficient time reading the post

        return JsonResponse({
            'views': post.views,
            'message': 'View count updated successfully'
        })
    except Exception as e:
        print(f"Error incrementing view count: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def award_reading_points_api(request, post_id):
    """Award reading points after user has spent sufficient time reading"""
    try:
        # Get time spent from request data
        time_spent = request.data.get('time_spent', 0)
        scroll_percentage = request.data.get('scroll_percentage', 0.0)

        print(f"🔍 DEBUG: award_reading_points_api called - user: {request.user.username}, post: {post_id}, time: {time_spent}s")

        # Validate post exists
        post = get_object_or_404(Post, id=post_id)

        # Award gamification points for reading
        from gamification.services import GamificationService
        success, message = GamificationService.update_reading_activity(
            request.user,
            post_id,
            time_spent=time_spent,
            scroll_percentage=scroll_percentage
        )

        if success:
            print(f"✅ Awarded reading points to {request.user.username} for post {post_id}")
            return JsonResponse({
                'success': True,
                'message': 'Reading points awarded successfully',
                'points_awarded': 5
            })
        else:
            print(f"⚠️ Reading points not awarded to {request.user.username} for post {post_id}: {message}")
            return JsonResponse({
                'success': False,
                'message': message
            })

    except Exception as e:
        print(f"❌ Failed to award reading points: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


class PostViewSet(viewsets.ModelViewSet):
    serializer_class = PostSerializer
    permission_classes = [IsContentCreatorOrReadOnly]
    pagination_class = StandardPagination

    def get_queryset(self):
        return Post.objects.select_related('author', 'category').prefetch_related('tags', 'media_items').order_by('-created_at')

    def perform_create(self, serializer):
        # Check if user can create content
        if not self.request.user.can_create_content():
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("You don't have permission to create posts. Contact an admin to become a content creator.")

        serializer.save(
            author=self.request.user,
            slug=slugify(serializer.validated_data['title'])
        )
    
    @action(detail=True, methods=['post'])
    def get_comments(self, obj):
        comments = obj.comments.filter(parent=None).order_by('created_at')
        paginator = Paginator(comments, 10)
        page = self.context['request'].GET.get('comments_page')
        comments_page = paginator.get_page(page)
        return CommentSerializer(comments_page, many=True).data
    
    
    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        post = self.get_object()
        if request.user in post.likes.all():
            post.likes.remove(request.user)
            is_liked = False
        else:
            post.likes.add(request.user)
            is_liked = True
        return Response({
            'like_count': post.likes.count(),
            'is_liked': is_liked
        })
    
    @action(detail=True, methods=['get'])
    def comments(self, request, pk=None):
        post = self.get_object()
        comments = Comment.objects.filter(post=post, parent=None)
        serializer = CommentSerializer(comments, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[permissions.AllowAny])
    def increment_view(self, request, pk=None):
        """Increment view count for a post"""
        post = self.get_object()
        old_views = post.views
        Post.objects.filter(pk=post.pk).update(views=F('views') + 1)
        post.refresh_from_db()
        print(f"View count updated for post {post.pk}: {old_views} -> {post.views}")
        return Response({
            'views': post.views,
            'message': 'View count updated successfully'
        })

class CommentsAPIView(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        try:
            post_id = request.query_params.get('post', None)
            if not post_id:
                return Response({'error': 'post parameter is required'}, status=400)

            # Get all comments for the post (both parent and replies)
            all_comments = Comment.objects.filter(
                post_id=post_id
            ).select_related('author', 'post', 'parent').prefetch_related('likes').order_by('created_at')

            # Build a map of comments by ID for easier lookup
            comment_map = {}
            for comment in all_comments:
                comment_data = {
                    'id': comment.id,
                    'post': comment.post.id,
                    'author': {
                        'id': comment.author.id,
                        'username': comment.author.username,
                        'email': comment.author.email,
                        'first_name': comment.author.first_name,
                        'last_name': comment.author.last_name,
                    },
                    'content': comment.content,
                    'created_at': comment.created_at.isoformat(),
                    'updated_at': comment.updated_at.isoformat(),
                    'parent': comment.parent.id if comment.parent else None,
                    'like_count': comment.likes.count(),
                    'is_liked': comment.likes.filter(id=request.user.id).exists() if request.user.is_authenticated else False,
                    'replies': []
                }
                comment_map[comment.id] = comment_data

            # Build the nested structure
            top_level_comments = []
            for comment in all_comments:
                comment_data = comment_map[comment.id]
                if comment.parent is None:
                    # This is a top-level comment
                    top_level_comments.append(comment_data)
                else:
                    # This is a reply, add it to its parent's replies
                    parent_data = comment_map.get(comment.parent.id)
                    if parent_data:
                        parent_data['replies'].append(comment_data)

            # Sort top-level comments by creation date (newest first)
            top_level_comments.sort(key=lambda x: x['created_at'], reverse=True)

            return Response({
                'count': len(top_level_comments),
                'next': None,
                'previous': None,
                'results': top_level_comments
            })

        except Exception as e:
            return Response({'error': str(e)}, status=500)

    def post(self, request):
        try:
            if not request.user.is_authenticated:
                return Response({'error': 'Authentication required'}, status=401)

            post_id = request.data.get('post')
            content = request.data.get('content')
            parent_id = request.data.get('parent')

            if not post_id or not content:
                return Response({'error': 'post and content are required'}, status=400)

            # Get the post
            try:
                post = Post.objects.get(id=post_id)
            except Post.DoesNotExist:
                return Response({'error': 'Post not found'}, status=404)

            # Get parent comment if specified
            parent = None
            if parent_id:
                try:
                    parent = Comment.objects.get(id=parent_id)
                except Comment.DoesNotExist:
                    return Response({'error': 'Parent comment not found'}, status=404)

            # Create the comment
            comment = Comment.objects.create(
                post=post,
                author=request.user,
                content=content,
                parent=parent
            )

            # Award gamification points for commenting
            try:
                from gamification.services import GamificationService
                success, message = GamificationService.update_engagement_activity(
                    request.user, 'comment', comment.id, 'comment'
                )
                if success:
                    print(f"✅ Awarded comment points to {request.user.username} for comment {comment.id}")
                else:
                    print(f"⚠️ Comment points not awarded: {message}")
            except Exception as e:
                print(f"❌ Failed to award comment points: {e}")

            # Return the created comment
            comment_data = {
                'id': comment.id,
                'post': comment.post.id,
                'author': {
                    'id': comment.author.id,
                    'username': comment.author.username,
                    'email': comment.author.email,
                    'first_name': comment.author.first_name,
                    'last_name': comment.author.last_name,
                },
                'content': comment.content,
                'created_at': comment.created_at.isoformat(),
                'updated_at': comment.updated_at.isoformat(),
                'parent': comment.parent.id if comment.parent else None,
                'like_count': 0,
                'is_liked': False,
                'replies': []
            }

            return Response(comment_data, status=201)

        except Exception as e:
            return Response({'error': str(e)}, status=500)

class CommentLikeAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, comment_id):
        try:
            comment = get_object_or_404(Comment, id=comment_id)

            if request.user in comment.likes.all():
                comment.likes.remove(request.user)
                is_liked = False
            else:
                comment.likes.add(request.user)
                is_liked = True

            return Response({
                'like_count': comment.likes.count(),
                'is_liked': is_liked
            })

        except Exception as e:
            return Response({'error': str(e)}, status=500)

class PostLikeAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, post_id):
        try:
            post = get_object_or_404(Post, id=post_id)

            if request.user in post.likes.all():
                post.likes.remove(request.user)
                is_liked = False
                # Note: We don't remove points when unliking to prevent gaming
            else:
                post.likes.add(request.user)
                is_liked = True

                # Award gamification points for liking
                try:
                    from gamification.services import GamificationService
                    success, message = GamificationService.update_engagement_activity(
                        request.user, 'like', post_id, 'post'
                    )
                    if success:
                        print(f"✅ Awarded like points to {request.user.username} for post {post_id}")
                    else:
                        print(f"⚠️ Like points not awarded: {message}")
                except Exception as e:
                    print(f"❌ Failed to award like points: {e}")

            return Response({
                'like_count': post.likes.count(),
                'is_liked': is_liked
            })

        except Exception as e:
            return Response({'error': str(e)}, status=500)

def is_staff_or_author(user, post):
    return user.is_staff or user == post.author

@login_required
def create_post(request):
    if request.method == 'POST':
        form = PostForm(request.POST, request.FILES)
        if form.is_valid():
            post = form.save(commit=False)
            post.author = request.user
            post.save()
            form.save_m2m()  # Save tags
            messages.success(request, 'Post created successfully!')
            return redirect('post-detail', slug=post.slug)
    else:
        form = PostForm()
    return render(request, 'blog/create_post.html', {'form': form})

@login_required
def edit_post(request, slug):
    post = get_object_or_404(Post, slug=slug)
    if not is_staff_or_author(request.user, post):
        messages.error(request, 'You do not have permission to edit this post.')
        return redirect('post-detail', slug=slug)
    
    if request.method == 'POST':
        form = PostForm(request.POST, request.FILES, instance=post)
        if form.is_valid():
            post = form.save()
            form.save_m2m()  # Save tags
            messages.success(request, 'Post updated successfully!')
            return redirect('post-detail', slug=post.slug)
    else:
        form = PostForm(instance=post)
    return render(request, 'blog/create_post.html', {'form': form, 'post': post})

@login_required
def delete_post(request, slug):
    post = get_object_or_404(Post, slug=slug)
    if not (request.user.is_staff or request.user == post.author):
        messages.error(request, 'You do not have permission to delete this post.')
        return redirect('post-detail', slug=slug)
    
    if request.method == 'POST':
        post.delete()
        messages.success(request, 'Post deleted successfully!')
        return redirect('home')
    return redirect('post-detail', slug=slug)

class LikeAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = LikeSerializer(data=request.data)
        if serializer.is_valid():
            content_type = serializer.validated_data['content_type']
            object_id = serializer.validated_data['object_id']
            is_liked = serializer.validated_data['is_liked']
            
            if content_type == 'post':
                obj = get_object_or_404(Post, id=object_id)
                if is_liked:
                    obj.likes.add(request.user)
                else:
                    obj.likes.remove(request.user)
            elif content_type == 'comment':
                obj = get_object_or_404(Comment, id=object_id)
                if is_liked:
                    obj.likes.add(request.user)
                else:
                    obj.likes.remove(request.user)
            else:
                return Response(
                    {'error': 'Invalid content type'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            return Response({
                'message': 'Like status updated successfully',
                'like_count': obj.likes.count(),
                'is_liked': is_liked
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class CommentRepliesAPIView(APIView):
    permission_classes = [permissions.AllowAny]
    
    def get(self, request, comment_id):
        comment = get_object_or_404(Comment, id=comment_id)
        replies = Comment.objects.filter(parent=comment)
        serializer = CommentSerializer(replies, many=True, context={'request': request})
        return Response(serializer.data)
    
    def post(self, request, comment_id):
        if not request.user.is_authenticated:
            return Response(
                {'error': 'Authentication required'},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        parent_comment = get_object_or_404(Comment, id=comment_id)
        serializer = CommentSerializer(data=request.data)
        
        if serializer.is_valid():
            serializer.save(
                author=request.user,
                post=parent_comment.post,
                parent=parent_comment
            )
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class PostCommentsAPIView(APIView):
    permission_classes = [permissions.AllowAny]
    
    def get(self, request, slug):
        post = get_object_or_404(Post, slug=slug)
        comments = Comment.objects.filter(post=post, parent=None)  # Get only parent comments
        serializer = CommentSerializer(comments, many=True, context={'request': request})
        return Response(serializer.data)

    def post(self, request, slug):
        if not request.user.is_authenticated:
            return Response(
                {'error': 'Authentication required'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        post = get_object_or_404(Post, slug=slug)
        serializer = CommentSerializer(data=request.data, context={'request': request})

        if serializer.is_valid():
            serializer.save(post=post)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Additional Web Views for new pages
def about(request):
    """About page view"""
    return render(request, 'blog/about.html')


def support(request):
    """Support center view with ticket handling"""
    if request.method == 'POST':
        # Handle support ticket submission
        name = request.POST.get('name')
        email = request.POST.get('email')
        category = request.POST.get('category')
        priority = request.POST.get('priority', 'medium')
        subject = request.POST.get('subject')
        message = request.POST.get('message')
        attachment = request.FILES.get('attachment')

        try:
            # Create support ticket
            from django.core.mail import send_mail
            from django.conf import settings
            import uuid

            # Generate ticket ID
            ticket_id = str(uuid.uuid4())[:8].upper()

            # Format email content
            email_subject = f"[TICKET-{ticket_id}] [{priority.upper()}] {subject}"
            email_body = f"""
New support ticket submitted:

Ticket ID: {ticket_id}
Name: {name}
Email: {email}
Category: {category}
Priority: {priority}
Subject: {subject}

Message:
{message}

---
Submitted from Trendy Support Center
Time: {timezone.now()}
User: {request.user if request.user.is_authenticated else 'Anonymous'}
            """

            # Send email to support team
            send_mail(
                subject=email_subject,
                message=email_body,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=['<EMAIL>'],
                fail_silently=False,
            )

            # Send confirmation email to user
            confirmation_subject = f"Support Ticket Created - #{ticket_id}"
            confirmation_body = f"""
Hi {name},

Your support ticket has been created successfully!

Ticket Details:
- Ticket ID: #{ticket_id}
- Subject: {subject}
- Priority: {priority}
- Category: {category}

Our support team will review your ticket and respond within:
- Critical: 2 hours
- High: 8 hours
- Medium: 24 hours
- Low: 48 hours

You can reference this ticket using ID #{ticket_id} in any future communications.

Best regards,
The Trendy Support Team
            """

            send_mail(
                subject=confirmation_subject,
                message=confirmation_body,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
                fail_silently=True,
            )

            messages.success(request, f'Support ticket #{ticket_id} created successfully! Check your email for confirmation.')
            return redirect('support')

        except Exception as e:
            messages.error(request, 'There was an error creating your support ticket. Please try again or contact us directly.')
            print(f"Support ticket error: {e}")

    return render(request, 'blog/support.html')


def privacy_policy(request):
    """Privacy policy view"""
    return render(request, 'blog/privacy_policy.html')


def terms_of_service(request):
    """Terms of service view"""
    return render(request, 'blog/terms_of_service.html')


def contact(request):
    """Contact page view with form handling"""
    if request.method == 'POST':
        # Handle contact form submission
        name = request.POST.get('name')
        email = request.POST.get('email')
        subject = request.POST.get('subject')
        message = request.POST.get('message')
        priority = request.POST.get('priority', 'medium')
        attachment = request.FILES.get('attachment')

        try:
            # Create support ticket
            from django.core.mail import send_mail
            from django.conf import settings

            # Format email content
            email_subject = f"[{priority.upper()}] Contact Form: {subject}"
            email_body = f"""
New contact form submission:

Name: {name}
Email: {email}
Subject: {subject}
Priority: {priority}

Message:
{message}

---
Submitted from Trendy Contact Form
Time: {timezone.now()}
User: {request.user if request.user.is_authenticated else 'Anonymous'}
            """

            # Send email to support team
            send_mail(
                subject=email_subject,
                message=email_body,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=['<EMAIL>'],
                fail_silently=False,
            )

            # Send confirmation email to user
            confirmation_subject = "We received your message - Trendy Support"
            confirmation_body = f"""
Hi {name},

Thank you for contacting Trendy support. We have received your message and will get back to you within 24 hours.

Your message details:
Subject: {subject}
Priority: {priority}

If you have any urgent issues, please don't hesitate to reach out to us directly.

Best regards,
The Trendy Support Team
            """

            send_mail(
                subject=confirmation_subject,
                message=confirmation_body,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
                fail_silently=True,
            )

            messages.success(request, 'Your message has been sent successfully! We\'ll get back to you within 24 hours.')
            return redirect('contact')

        except Exception as e:
            messages.error(request, 'There was an error sending your message. Please try again or contact us <NAME_EMAIL>.')
            print(f"Contact form error: {e}")

    return render(request, 'blog/contact.html')


@login_required
def settings_view(request):
    """User settings page view"""
    if request.method == 'POST':
        # Handle different form submissions based on the form type
        form_type = request.POST.get('form_type')

        if form_type == 'profile':
            # Handle profile update
            form = UserProfileForm(request.POST, request.FILES, instance=request.user)
            if form.is_valid():
                form.save()
                messages.success(request, 'Profile updated successfully!')
            else:
                messages.error(request, 'Please correct the errors below.')

        elif form_type == 'password':
            # Handle password change
            current_password = request.POST.get('current_password')
            new_password = request.POST.get('new_password')
            confirm_password = request.POST.get('confirm_password')

            if not request.user.check_password(current_password):
                messages.error(request, 'Current password is incorrect.')
            elif new_password != confirm_password:
                messages.error(request, 'New passwords do not match.')
            elif len(new_password) < 8:
                messages.error(request, 'Password must be at least 8 characters long.')
            else:
                request.user.set_password(new_password)
                request.user.save()
                messages.success(request, 'Password changed successfully!')
                # Re-authenticate user to keep them logged in
                from django.contrib.auth import update_session_auth_hash
                update_session_auth_hash(request, request.user)

        elif form_type == 'notifications':
            # Handle notification preferences
            # This would update user notification settings
            messages.success(request, 'Notification preferences updated!')

        elif form_type == 'privacy':
            # Handle privacy settings
            # This would update user privacy settings
            messages.success(request, 'Privacy settings updated!')

        return redirect('settings')

    return render(request, 'blog/settings.html')


@login_required
def delete_account(request):
    """Handle account deletion"""
    if request.method == 'POST':
        confirmation = request.POST.get('confirmation')
        password = request.POST.get('password')

        if confirmation != 'DELETE':
            messages.error(request, 'Please type "DELETE" to confirm account deletion.')
            return redirect('settings')

        if not request.user.check_password(password):
            messages.error(request, 'Incorrect password.')
            return redirect('settings')

        # Delete user account
        user = request.user
        from django.contrib.auth import logout
        logout(request)
        user.delete()

        messages.success(request, 'Your account has been permanently deleted.')
        return redirect('home')

    return redirect('settings')


@login_required
def notifications_view(request):
    """User notifications page"""
    # This would show user notifications
    # For now, just render a basic template
    return render(request, 'blog/notifications.html')


# ============================================================================
# MEDIA OPTIMIZATION VIEWS
# ============================================================================

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.views.decorators.cache import cache_control
from django.views.decorators.http import require_http_methods

@api_view(['GET'])
@permission_classes([AllowAny])
@cache_control(max_age=3600)  # Cache for 1 hour
def post_media_optimized(request, post_id):
    """Get optimized media for a specific post"""
    try:
        post = get_object_or_404(Post, id=post_id, status='published')
        media_items = post.media_items.all().order_by('order', 'created_at')

        optimized_media = []
        for media in media_items:
            media_data = {
                'id': media.id,
                'type': media.media_type,
                'title': media.title,
                'caption': media.caption,
                'description': media.description,
                'order': media.order,
                'created_at': media.created_at.isoformat(),
            }

            if media.is_optimized:
                # Use optimized data
                media_data.update({
                    'responsive': media.get_responsive_data(),
                    'optimized_urls': media.optimized_urls,
                    'metadata': media.media_metadata,
                    'is_optimized': True
                })
            else:
                # Fallback to original
                media_data.update({
                    'src': media.media_source,
                    'preview': media.preview_image,
                    'is_optimized': False
                })

            optimized_media.append(media_data)

        return Response({
            'post_id': post.id,
            'post_title': post.title,
            'media_count': len(optimized_media),
            'media_items': optimized_media,
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def media_gallery(request, post_id):
    """Get media gallery data for full-screen viewing"""
    try:
        post = get_object_or_404(Post, id=post_id, status='published')
        media_items = post.media_items.all().order_by('order', 'created_at')

        gallery_items = []
        for index, media in enumerate(media_items):
            item = {
                'id': media.id,
                'index': index,
                'type': media.media_type,
                'title': media.title or f"{media.get_media_type_display()} {index + 1}",
                'caption': media.caption,
                'description': media.description,
            }

            if media.media_type == 'image':
                if media.is_optimized:
                    item.update({
                        'thumbnail': media.get_optimized_url('small'),
                        'medium': media.get_optimized_url('medium'),
                        'large': media.get_optimized_url('large'),
                        'original': media.get_optimized_url('original'),
                        'responsive': media.get_responsive_data(),
                        'metadata': media.media_metadata
                    })
                else:
                    item.update({
                        'src': media.media_source,
                        'thumbnail': media.media_source,
                        'medium': media.media_source,
                        'large': media.media_source,
                        'original': media.media_source
                    })

            elif media.media_type == 'video':
                item.update({
                    'video_url': media.media_source,
                    'thumbnail': media.preview_image,
                    'poster': media.preview_image,
                    'video_metadata': media.media_metadata
                })

            gallery_items.append(item)

        return Response({
            'post_id': post.id,
            'post_title': post.title,
            'total_items': len(gallery_items),
            'gallery_items': gallery_items,
            'navigation': {
                'enable_swipe': True,
                'enable_keyboard': True,
                'enable_zoom': True,
                'auto_play_videos': False,
                'loop_gallery': True
            }
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def optimize_media(request, media_id):
    """Manually trigger media optimization"""
    try:
        media = get_object_or_404(PostMedia, id=media_id)

        # Check if user can edit this post
        if media.post.author != request.user:
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Optimize the media
        if media.media_type == 'image' and media.image:
            media.optimize_media()

            return Response({
                'success': True,
                'message': 'Media optimized successfully',
                'optimized_urls': media.optimized_urls,
                'metadata': media.media_metadata,
                'is_optimized': media.is_optimized
            })
        else:
            return Response({
                'error': 'Media type not supported for optimization or no file available'
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def media_progressive_data(request, media_id):
    """Get progressive loading data for a specific media item"""
    try:
        media = get_object_or_404(PostMedia, id=media_id)

        if media.media_type != 'image':
            return Response({
                'error': 'Progressive loading only available for images'
            }, status=status.HTTP_400_BAD_REQUEST)

        if media.is_optimized:
            progressive_data = {
                'placeholder': media.get_optimized_url('thumbnail'),
                'low_quality': media.get_optimized_url('small'),
                'medium_quality': media.get_optimized_url('medium'),
                'high_quality': media.get_optimized_url('large'),
                'loading_strategy': 'progressive'
            }
        else:
            progressive_data = {
                'placeholder': media.media_source,
                'low_quality': media.media_source,
                'medium_quality': media.media_source,
                'high_quality': media.media_source,
                'loading_strategy': 'standard'
            }

        return Response({
            'media_id': media.id,
            'progressive_data': progressive_data,
            'responsive_data': media.get_responsive_data() if hasattr(media, 'get_responsive_data') else {},
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def media_batch_optimize_status(request):
    """Get status of media optimization for multiple items"""
    try:
        media_ids = request.GET.get('ids', '').split(',')
        media_ids = [int(id.strip()) for id in media_ids if id.strip().isdigit()]

        if not media_ids:
            return Response({
                'error': 'No valid media IDs provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        media_items = PostMedia.objects.filter(id__in=media_ids)

        status_data = []
        for media in media_items:
            item_status = {
                'id': media.id,
                'type': media.media_type,
                'is_optimized': media.is_optimized,
                'has_variants': bool(media.optimized_urls),
                'file_size': getattr(media.image, 'size', 0) if media.image else 0,
                'optimization_date': media.updated_at.isoformat() if media.is_optimized else None
            }

            if media.is_optimized and media.media_metadata:
                item_status['metadata'] = media.media_metadata

            status_data.append(item_status)

        return Response({
            'total_items': len(status_data),
            'optimized_count': sum(1 for item in status_data if item['is_optimized']),
            'items': status_data
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
@cache_control(max_age=86400)  # Cache for 24 hours
def media_optimization_stats(request):
    """Get overall media optimization statistics"""
    try:
        total_media = PostMedia.objects.count()
        optimized_media = PostMedia.objects.filter(is_optimized=True).count()
        image_media = PostMedia.objects.filter(media_type='image').count()
        video_media = PostMedia.objects.filter(media_type='video').count()

        optimization_rate = (optimized_media / total_media * 100) if total_media > 0 else 0

        return Response({
            'total_media_items': total_media,
            'optimized_items': optimized_media,
            'optimization_rate': round(optimization_rate, 2),
            'media_breakdown': {
                'images': image_media,
                'videos': video_media
            },
            'optimization_benefits': {
                'faster_loading': True,
                'bandwidth_savings': '30-70%',
                'better_seo': True,
                'mobile_optimized': True
            }
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
