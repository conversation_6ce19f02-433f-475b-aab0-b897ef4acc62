from django.contrib import admin
from .models import (
    AdNetwork, AdPlacement, AdImpression, RewardedAd, 
    SponsoredPost, AdSettings, AdNetworkPlacement
)


@admin.register(AdNetwork)
class AdNetworkAdmin(admin.ModelAdmin):
    list_display = ['name', 'network_type', 'is_active', 'priority', 'created_at']
    list_filter = ['network_type', 'is_active', 'priority']
    search_fields = ['name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(AdPlacement)
class AdPlacementAdmin(admin.ModelAdmin):
    list_display = ['name', 'location', 'placement_type', 'is_active', 'created_at']
    list_filter = ['placement_type', 'location', 'is_active']
    search_fields = ['name', 'location']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(AdNetworkPlacement)
class AdNetworkPlacementAdmin(admin.ModelAdmin):
    list_display = ['ad_network', 'ad_placement', 'is_active', 'weight']
    list_filter = ['is_active', 'ad_network__name', 'ad_placement__name']
    search_fields = ['ad_network__name', 'ad_placement__name']


@admin.register(RewardedAd)
class RewardedAdAdmin(admin.ModelAdmin):
    list_display = ['name', 'points_reward', 'is_active', 'created_at']
    list_filter = ['is_active', 'reward_type', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(AdImpression)
class AdImpressionAdmin(admin.ModelAdmin):
    list_display = ['user', 'ad_placement', 'impression_type', 'created_at']
    list_filter = ['impression_type', 'created_at', 'ad_placement__placement_type']
    search_fields = ['user__username', 'session_id']
    readonly_fields = ['created_at']
    date_hierarchy = 'created_at'


@admin.register(SponsoredPost)
class SponsoredPostAdmin(admin.ModelAdmin):
    list_display = ['title', 'sponsor_name', 'sponsor_type', 'status', 'start_date', 'end_date']
    list_filter = ['sponsor_type', 'status', 'start_date', 'end_date']
    search_fields = ['title', 'sponsor_name']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'start_date'


@admin.register(AdSettings)
class AdSettingsAdmin(admin.ModelAdmin):
    list_display = ['ads_enabled', 'show_ads_to_premium', 'max_ads_per_session', 'rewarded_ads_enabled']
    readonly_fields = ['created_at', 'updated_at']
    
    def has_add_permission(self, request):
        # Only allow one settings instance
        return not AdSettings.objects.exists()
    
    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of settings
        return False
