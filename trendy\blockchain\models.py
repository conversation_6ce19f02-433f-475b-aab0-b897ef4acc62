"""
Blockchain models for Trendy app
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal

User = get_user_model()

class BlockchainNetwork(models.Model):
    """Supported blockchain networks"""
    NETWORK_CHOICES = [
        ('polygon', 'Polygon (MATIC)'),
        ('polygon_testnet', 'Polygon Mumbai Testnet'),
        ('bsc', 'Binance Smart Chain'),
        ('bsc_testnet', 'BSC Testnet'),
        ('ethereum', 'Ethereum'),
        ('ethereum_testnet', 'Ethereum Goerli Testnet'),
        ('solana', 'Solana'),
        ('solana_testnet', 'Solana Devnet'),
    ]
    
    name = models.CharField(max_length=50, choices=NETWORK_CHOICES, unique=True)
    chain_id = models.IntegerField()
    rpc_url = models.URLField()
    explorer_url = models.URLField()
    native_token = models.CharField(max_length=10)  # MATIC, BNB, ETH, SOL
    is_active = models.BooleanField(default=True)
    gas_price_gwei = models.DecimalField(max_digits=10, decimal_places=2, default=20)
    
    def __str__(self):
        return self.get_name_display()

class SmartContract(models.Model):
    """Smart contracts deployed on blockchain"""
    CONTRACT_TYPES = [
        ('token', 'ERC-20 Token'),
        ('nft', 'ERC-721 NFT'),
        ('escrow', 'Escrow Contract'),
        ('staking', 'Staking Contract'),
        ('marketplace', 'Marketplace Contract'),
    ]
    
    network = models.ForeignKey(BlockchainNetwork, on_delete=models.CASCADE)
    contract_type = models.CharField(max_length=20, choices=CONTRACT_TYPES)
    name = models.CharField(max_length=100)
    address = models.CharField(max_length=42)  # Ethereum address format
    abi = models.JSONField()  # Contract ABI
    deployed_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ['network', 'contract_type', 'name']
    
    def __str__(self):
        return f"{self.name} on {self.network.name}"

class UserWalletAddress(models.Model):
    """User's blockchain wallet addresses"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='wallet_addresses')
    network = models.ForeignKey(BlockchainNetwork, on_delete=models.CASCADE)
    address = models.CharField(max_length=42)
    private_key_encrypted = models.TextField()  # Encrypted private key
    is_primary = models.BooleanField(default=False)

    # Activation system
    is_active = models.BooleanField(default=False)
    activation_code = models.CharField(max_length=6, blank=True, null=True)
    activation_code_expires = models.DateTimeField(blank=True, null=True)
    activated_at = models.DateTimeField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['user', 'network']
        indexes = [
            models.Index(fields=['user', 'network']),
            models.Index(fields=['address']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.network.name} - {self.address[:10]}..."

    def generate_activation_code(self):
        """Generate a 6-digit activation code"""
        import random
        from django.utils import timezone
        from datetime import timedelta

        self.activation_code = f"{random.randint(100000, 999999)}"
        self.activation_code_expires = timezone.now() + timedelta(hours=24)  # Code expires in 24 hours
        self.save()
        return self.activation_code

    def is_activation_code_valid(self, code):
        """Check if the provided activation code is valid"""
        from django.utils import timezone

        if not self.activation_code or not self.activation_code_expires:
            return False

        if timezone.now() > self.activation_code_expires:
            return False

        return self.activation_code == code

    def activate_wallet(self, code):
        """Activate the wallet with the provided code"""
        from django.utils import timezone

        if self.is_activation_code_valid(code):
            self.is_active = True
            self.activated_at = timezone.now()
            self.activation_code = None  # Clear the code after use
            self.activation_code_expires = None
            self.save()
            return True
        return False

class BlockchainTransaction(models.Model):
    """Blockchain transaction records"""
    TRANSACTION_TYPES = [
        ('token_reward', 'Token Reward'),
        ('nft_mint', 'NFT Mint'),
        ('token_transfer', 'Token Transfer'),
        ('escrow_create', 'Escrow Create'),
        ('escrow_release', 'Escrow Release'),
        ('staking_deposit', 'Staking Deposit'),
        ('staking_withdraw', 'Staking Withdraw'),
        ('marketplace_buy', 'Marketplace Purchase'),
        ('marketplace_sell', 'Marketplace Sale'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='blockchain_transactions')
    network = models.ForeignKey(BlockchainNetwork, on_delete=models.CASCADE)
    contract = models.ForeignKey(SmartContract, on_delete=models.CASCADE, null=True, blank=True)
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    
    # Transaction details
    blockchain_hash = models.CharField(max_length=66, unique=True)
    from_address = models.CharField(max_length=42)
    to_address = models.CharField(max_length=42)
    amount = models.DecimalField(max_digits=30, decimal_places=18, null=True, blank=True)
    token_id = models.BigIntegerField(null=True, blank=True)  # For NFTs
    
    # Gas and fees
    gas_limit = models.BigIntegerField()
    gas_used = models.BigIntegerField(null=True, blank=True)
    gas_price = models.BigIntegerField()  # In wei
    transaction_fee = models.DecimalField(max_digits=20, decimal_places=18)
    
    # Status and timing
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    block_number = models.BigIntegerField(null=True, blank=True)
    block_hash = models.CharField(max_length=66, null=True, blank=True)
    confirmations = models.IntegerField(default=0)
    
    # Metadata
    description = models.TextField(blank=True)
    metadata = models.JSONField(default=dict, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    confirmed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'transaction_type']),
            models.Index(fields=['blockchain_hash']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['network', 'block_number']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.username} - {self.transaction_type} - {self.blockchain_hash[:10]}..."
    
    @property
    def is_confirmed(self):
        return self.status == 'confirmed' and self.confirmations >= 12

class TokenBalance(models.Model):
    """User token balances on blockchain"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='token_balances')
    network = models.ForeignKey(BlockchainNetwork, on_delete=models.CASCADE)
    contract = models.ForeignKey(SmartContract, on_delete=models.CASCADE)
    balance = models.DecimalField(max_digits=30, decimal_places=18, default=0)
    staked_balance = models.DecimalField(max_digits=30, decimal_places=18, default=0)
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['user', 'network', 'contract']
        indexes = [
            models.Index(fields=['user', 'network']),
            models.Index(fields=['contract', 'balance']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.contract.name} - {self.balance}"
    
    @property
    def total_balance(self):
        return self.balance + self.staked_balance

class NFTAsset(models.Model):
    """NFT assets owned by users"""
    RARITY_CHOICES = [
        (1, 'Common'),
        (2, 'Uncommon'),
        (3, 'Rare'),
        (4, 'Epic'),
        (5, 'Legendary'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='nft_assets')
    network = models.ForeignKey(BlockchainNetwork, on_delete=models.CASCADE)
    contract = models.ForeignKey(SmartContract, on_delete=models.CASCADE)
    
    # NFT details
    token_id = models.BigIntegerField()
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    image_url = models.URLField()
    animation_url = models.URLField(blank=True)
    external_url = models.URLField(blank=True)
    
    # Attributes
    rarity = models.IntegerField(choices=RARITY_CHOICES, default=1)
    attributes = models.JSONField(default=dict, blank=True)
    
    # Blockchain info
    mint_transaction = models.ForeignKey(
        BlockchainTransaction, 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='minted_nfts'
    )
    
    # Timestamps
    minted_at = models.DateTimeField(auto_now_add=True)
    last_transferred = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ['network', 'contract', 'token_id']
        indexes = [
            models.Index(fields=['user', 'rarity']),
            models.Index(fields=['contract', 'token_id']),
            models.Index(fields=['rarity', 'minted_at']),
        ]
        ordering = ['-minted_at']
    
    def __str__(self):
        return f"{self.name} (#{self.token_id}) - {self.user.username}"
    
    @property
    def rarity_display(self):
        return self.get_rarity_display()

class StakingPool(models.Model):
    """Token staking pools"""
    network = models.ForeignKey(BlockchainNetwork, on_delete=models.CASCADE)
    contract = models.ForeignKey(SmartContract, on_delete=models.CASCADE)
    token_contract = models.ForeignKey(
        SmartContract, 
        on_delete=models.CASCADE, 
        related_name='staking_pools'
    )
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    apy_percentage = models.DecimalField(max_digits=5, decimal_places=2)  # Annual Percentage Yield
    minimum_stake = models.DecimalField(max_digits=10, decimal_places=2)
    maximum_stake = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    # Pool stats
    total_staked = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_rewards_distributed = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    active_stakers = models.IntegerField(default=0)
    
    # Status
    is_active = models.BooleanField(default=True)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.name} - {self.apy_percentage}% APY"

class UserStake(models.Model):
    """User staking positions"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='stakes')
    pool = models.ForeignKey(StakingPool, on_delete=models.CASCADE, related_name='user_stakes')
    
    amount_staked = models.DecimalField(max_digits=15, decimal_places=2)
    rewards_earned = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    last_reward_calculation = models.DateTimeField(auto_now_add=True)
    
    # Transactions
    stake_transaction = models.ForeignKey(
        BlockchainTransaction,
        on_delete=models.SET_NULL,
        null=True,
        related_name='stake_deposits'
    )
    
    # Status
    is_active = models.BooleanField(default=True)
    staked_at = models.DateTimeField(auto_now_add=True)
    unstaked_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'pool']),
            models.Index(fields=['pool', 'is_active']),
            models.Index(fields=['staked_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.pool.name} - {self.amount_staked}"
    
    def calculate_pending_rewards(self):
        """Calculate pending rewards based on time staked"""
        if not self.is_active:
            return Decimal('0')
        
        time_staked = timezone.now() - self.last_reward_calculation
        days_staked = Decimal(str(time_staked.total_seconds() / 86400))  # Convert to days
        
        daily_rate = self.pool.apy_percentage / Decimal('365') / Decimal('100')
        pending_rewards = self.amount_staked * daily_rate * days_staked
        
        return pending_rewards


class MarketplaceListing(models.Model):
    """NFT marketplace listings"""
    LISTING_TYPES = [
        ('fixed_price', 'Fixed Price'),
        ('auction', 'Auction'),
        ('offer', 'Best Offer'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('sold', 'Sold'),
        ('cancelled', 'Cancelled'),
        ('expired', 'Expired'),
    ]

    seller = models.ForeignKey(User, on_delete=models.CASCADE, related_name='marketplace_listings')
    nft_asset = models.ForeignKey(NFTAsset, on_delete=models.CASCADE, related_name='listings')

    listing_type = models.CharField(max_length=20, choices=LISTING_TYPES)
    price = models.DecimalField(max_digits=20, decimal_places=18)
    currency_contract = models.ForeignKey(SmartContract, on_delete=models.CASCADE)

    # Auction specific
    starting_price = models.DecimalField(max_digits=20, decimal_places=18, null=True, blank=True)
    reserve_price = models.DecimalField(max_digits=20, decimal_places=18, null=True, blank=True)
    auction_end = models.DateTimeField(null=True, blank=True)

    # Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    buyer = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='marketplace_purchases')
    sale_transaction = models.ForeignKey(BlockchainTransaction, on_delete=models.SET_NULL, null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    sold_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['status', 'listing_type']),
            models.Index(fields=['seller', 'status']),
            models.Index(fields=['price', 'currency_contract']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.nft_asset.name} - {self.price} - {self.status}"
