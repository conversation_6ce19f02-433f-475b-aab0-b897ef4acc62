# Generated by Django 5.1.7 on 2025-06-25 11:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('monetization', '0002_userreferralcode_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='uservirtualitem',
            name='payment_method',
            field=models.CharField(choices=[('store_points', 'Store Points'), ('real_money', 'Real Money'), ('free', 'Free')], default='store_points', max_length=20),
        ),
        migrations.AddField(
            model_name='uservirtualitem',
            name='purchase_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name='uservirtualitem',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded')], default='pending', max_length=20),
        ),
        migrations.AlterField(
            model_name='uservirtualitem',
            name='purchase_transaction',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='monetization.purchasetransaction'),
        ),
        migrations.AddIndex(
            model_name='uservirtualitem',
            index=models.Index(fields=['payment_method', 'status'], name='monetizatio_payment_fedbdc_idx'),
        ),
    ]
