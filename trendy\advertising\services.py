from django.db import transaction
from django.utils import timezone
from django.db.models import Sum, Count, Avg, Q
from decimal import Decimal
import uuid
import random

from .models import (
    AdNetwork, AdPlacement, AdImpression, RewardedAd, 
    SponsoredPost, AdSettings
)


class AdService:
    """Simplified service for handling ad serving and tracking"""
    
    @staticmethod
    def get_ad_for_placement(user, placement_location, context=None):
        """Get appropriate ad for specific placement and user"""
        try:
            # Get placement
            placement = AdPlacement.objects.filter(
                location=placement_location,
                is_active=True
            ).first()
            
            if not placement:
                return None
            
            # Get active ad networks
            networks = AdNetwork.objects.filter(is_active=True).order_by('priority')
            
            if not networks.exists():
                return None
            
            # Select network (simple round-robin for now)
            network = networks.first()
            
            return {
                'placement_id': placement.id,
                'placement_name': placement.name,
                'placement_type': placement.placement_type,
                'ad_network_id': network.id,
                'ad_network_name': network.name,
                'points_reward': getattr(placement, 'points_reward', 0),
                'ad_id': f"ad_{uuid.uuid4().hex[:8]}",
            }
            
        except Exception as e:
            print(f"Error getting ad for placement: {e}")
            return None
    
    @staticmethod
    def record_ad_impression(user, placement_id, ad_network_id, ad_id=None, context=None):
        """Record ad impression"""
        try:
            placement = AdPlacement.objects.get(id=placement_id)
            ad_network = AdNetwork.objects.get(id=ad_network_id)
            
            # Create impression record
            impression = AdImpression.objects.create(
                user=user,
                ad_placement=placement,
                impression_type='shown',
                session_id=context.get('session_id') if context else None,
                points_awarded=0,  # Points awarded on completion
            )
            
            return {
                'success': True,
                'impression_id': impression.id,
                'revenue': 0.0
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def record_ad_click(impression_id, context=None):
        """Record ad click"""
        try:
            impression = AdImpression.objects.get(id=impression_id)
            impression.impression_type = 'clicked'
            impression.save()
            
            return {
                'success': True,
                'click_revenue': 0.0,
                'total_revenue': 0.0
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def start_rewarded_ad_session(user, placement_id, ad_network_id):
        """Start rewarded ad session"""
        try:
            placement = AdPlacement.objects.get(id=placement_id)
            ad_network = AdNetwork.objects.get(id=ad_network_id)
            
            # Generate session ID
            session_id = str(uuid.uuid4())
            
            # Get points reward
            points_offered = getattr(placement, 'points_reward', 10)
            
            return {
                'success': True,
                'session_id': session_id,
                'points_offered': points_offered
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def complete_rewarded_ad_session(session_id, completed=True):
        """Complete rewarded ad session and award points"""
        try:
            if completed:
                # Award points through gamification system
                try:
                    from gamification.services import GamificationService
                    # For now, award default points
                    points_awarded = 10
                    
                    return {
                        'success': True,
                        'points_awarded': points_awarded,
                        'message': 'Points awarded successfully'
                    }
                except ImportError:
                    return {
                        'success': True,
                        'points_awarded': 0,
                        'message': 'Ad completed but points system unavailable'
                    }
            else:
                return {
                    'success': True,
                    'points_awarded': 0,
                    'message': 'Ad session abandoned'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def get_user_ad_stats(user):
        """Get user's ad interaction statistics"""
        try:
            # Today's stats
            today = timezone.now().replace(hour=0, minute=0, second=0)
            
            today_impressions = AdImpression.objects.filter(
                user=user,
                created_at__gte=today
            ).count()
            
            # All-time stats
            total_impressions = AdImpression.objects.filter(user=user).count()
            total_clicks = AdImpression.objects.filter(
                user=user, 
                impression_type='clicked'
            ).count()
            
            return {
                'today_impressions': today_impressions,
                'total_impressions': total_impressions,
                'total_clicks': total_clicks,
                'click_through_rate': (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
            }
            
        except Exception as e:
            return {
                'today_impressions': 0,
                'total_impressions': 0,
                'total_clicks': 0,
                'click_through_rate': 0
            }
    
    @staticmethod
    def get_sponsored_content(user, placement=None):
        """Get relevant sponsored content for user"""
        try:
            # Get active sponsored content
            sponsored_content = SponsoredPost.objects.filter(
                status='active',
                start_date__lte=timezone.now(),
                end_date__gte=timezone.now(),
            ).order_by('-created_at')[:5]
            
            content_data = []
            for content in sponsored_content:
                content_data.append({
                    'id': content.id,
                    'title': content.title,
                    'sponsor_name': content.sponsor_name,
                    'sponsor_type': content.sponsor_type,
                    'content_type': 'sponsored_post',
                })
            
            return content_data
            
        except Exception as e:
            print(f"Error getting sponsored content: {e}")
            return []
    
    @staticmethod
    def check_ad_availability(user):
        """Check if user can watch ads"""
        try:
            settings = AdSettings.get_settings()
            
            if not settings.ads_enabled:
                return {
                    'can_watch_ads': False,
                    'reason': 'Ads are disabled'
                }
            
            # Check daily limits
            today = timezone.now().replace(hour=0, minute=0, second=0)
            today_impressions = AdImpression.objects.filter(
                user=user,
                created_at__gte=today
            ).count()
            
            max_daily = getattr(settings, 'max_rewarded_ads_per_day', 10)
            
            if today_impressions >= max_daily:
                return {
                    'can_watch_ads': False,
                    'reason': 'Daily limit reached'
                }
            
            return {
                'can_watch_ads': True,
                'remaining_today': max_daily - today_impressions
            }
            
        except Exception as e:
            return {
                'can_watch_ads': False,
                'reason': f'Error checking availability: {e}'
            }
