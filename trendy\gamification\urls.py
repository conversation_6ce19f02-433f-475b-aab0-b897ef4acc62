from django.urls import path
from . import views

app_name = 'gamification'

urlpatterns = [
    # Badges
    path('badges/', views.badges_list, name='badges-list'),
    path('user/badges/', views.user_badges, name='user-badges'),

    # Challenges
    path('challenges/', views.challenges_list, name='challenges-list'),
    path('challenges/<int:challenge_id>/join/', views.join_challenge, name='join-challenge'),
    path('challenges/<int:challenge_id>/progress/', views.update_challenge_progress, name='update-challenge-progress'),

    # User profile and level
    path('user/profile/', views.user_profile, name='user-profile'),
    path('user/level/', views.user_level, name='user-level'),
    path('user/transactions/', views.user_transactions, name='user-transactions'),

    # PayPal Rewards
    path('paypal-rewards/', views.paypal_rewards_list, name='paypal-rewards-list'),
    path('user-paypal-rewards/', views.user_paypal_rewards, name='user-paypal-rewards'),
    path('paypal-rewards/<str:reward_id>/claim/', views.claim_paypal_reward, name='claim-paypal-reward'),

    # Tier Unlocks
    path('unlocked-tiers/', views.user_unlocked_tiers, name='user-unlocked-tiers'),
    path('unlock-tier/', views.unlock_tier, name='unlock-tier'),

    # Leaderboard
    path('leaderboard/', views.leaderboard, name='leaderboard'),

    # Statistics (admin)
    path('stats/challenges/', views.challenge_stats, name='challenge-stats'),
    path('stats/gamification/', views.gamification_stats, name='gamification-stats'),

    # Admin actions
    path('admin/award-points/', views.award_points_manual, name='award-points-manual'),
    path('admin/pending-rewards/', views.admin_pending_rewards, name='admin-pending-rewards'),
    path('admin/approve-reward/<int:claim_id>/', views.admin_approve_reward, name='admin-approve-reward'),
    path('admin/process-payment/<int:claim_id>/', views.admin_process_payment, name='admin-process-payment'),
    path('admin/reward-stats/', views.admin_reward_stats, name='admin-reward-stats'),

    # Engagement Rules Endpoints
    path('engagement/status/', views.engagement_status, name='engagement-status'),
    path('engagement/validate-reading/', views.validate_reading_activity, name='validate-reading'),
    path('engagement/validate-engagement/', views.validate_engagement_activity, name='validate-engagement'),

    # Point Conversion Endpoints
    path('conversion/settings/', views.conversion_settings, name='conversion-settings'),
    path('conversion/preview/', views.preview_conversion, name='preview-conversion'),
    path('conversion/convert/', views.convert_points, name='convert-points'),
    path('conversion/history/', views.conversion_history, name='conversion-history'),
    path('store-points/balance/', views.store_points_balance, name='store-points-balance'),

    # Unified Points Endpoint (Performance Optimized)
    path('points/unified/', views.unified_user_points, name='unified-user-points'),

    # Point Spending Endpoint
    path('spend-points/', views.spend_points, name='spend-points'),

    # Debug endpoint
    path('debug/headers/', views.debug_headers, name='debug-headers'),
]
