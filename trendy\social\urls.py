from django.urls import path
from . import views

app_name = 'social'

urlpatterns = [
    # User profiles
    path('profile/', views.user_profile, name='user-profile'),
    path('profile/<str:username>/', views.user_profile, name='user-profile-detail'),
    
    # Following system
    path('follow/<str:username>/', views.follow_user, name='follow-user'),
    path('users/<str:username>/followers/', views.user_followers, name='user-followers'),
    path('users/<str:username>/following/', views.user_following, name='user-following'),
    
    # Bookmarks
    path('bookmarks/', views.bookmarks, name='bookmarks'),
    path('bookmarks/<int:post_id>/remove/', views.remove_bookmark, name='remove-bookmark'),
    
    # Notifications
    path('notifications/', views.notifications, name='notifications'),
    path('notifications/<int:notification_id>/read/', views.mark_notification_read, name='mark-notification-read'),
    
    # Search and recommendations
    path('search/', views.advanced_search, name='advanced-search'),
    path('search/suggestions/', views.search_suggestions, name='search-suggestions'),
    path('recommendations/', views.recommendations, name='recommendations'),

    # Social discovery
    path('discover/', views.discover_users, name='discover-users'),
    path('trending/', views.trending_users, name='trending-users'),
    path('following/', views.user_following_list, name='user-following-list'),
    path('followers/', views.user_followers_list, name='user-followers-list'),
    path('search/users/', views.search_users, name='search-users'),

    # Report functionality
    path('reports/', views.create_report, name='create-report'),
    path('reports/user/', views.user_reports, name='user-reports'),
    path('reports/admin/', views.admin_reports, name='admin-reports'),
    path('reports/<int:report_id>/status/', views.update_report_status, name='update-report-status'),
]
