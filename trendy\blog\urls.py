from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from . import views



router = DefaultRouter()
router.register(r'categories', views.CategoryViewSet, basename='category')
# router.register(r'posts', views.PostViewSet, basename='post')  # Temporarily disabled due to serialization issue
# router.register(r'comments', views.CommentViewSet, basename='comment')  # Temporarily disabled due to DRF introspection issue

api_patterns = [
    # Authentication is now handled by the accounts app at /api/v1/accounts/
    path('posts/', views.posts_list_api, name='api-posts-list'),  # Custom posts API
    path('posts/<str:pk>/', views.post_detail_api, name='api-post-detail'),  # Custom post detail API (supports both ID and slug)
    path('posts/<int:post_id>/increment_view/', views.increment_post_view_api, name='api-increment-view'),  # Custom view increment API
    path('posts/<int:post_id>/award_reading_points/', views.award_reading_points_api, name='api-award-reading-points'),  # Award reading points after engagement
    path('posts/<slug:slug>/comments/', views.PostCommentsAPIView.as_view(), name='api-post-comments'),
    path('comments/', views.CommentsAPIView.as_view(), name='api-comments'),  # New custom comments API
    path('comments/<int:comment_id>/like/', views.CommentLikeAPIView.as_view(), name='api-comment-like'),  # Comment like API
    path('comments/<int:comment_id>/replies/', views.CommentRepliesAPIView.as_view(), name='api-comment-replies'),
    path('likes/', views.LikeAPIView.as_view(), name='api-like'),
    path('likes/posts/<int:post_id>/', views.PostLikeAPIView.as_view(), name='api-post-like'),  # Post like API
    # Note: These APIs are now included in the main trendyblog/urls.py to avoid namespace conflicts
    # path('analytics/', include('analytics.urls')),  # Analytics API - moved to main URLs
    # path('interactive/', include('interactive.urls')),  # Interactive content API - moved to main URLs
    # path('voice/', include('voice_features.urls')),  # Voice features API - moved to main URLs
    # path('gamification/', include('gamification.urls')),  # Gamification API - moved to main URLs
    # path('monetization/', include('monetization.urls')),  # Monetization API - moved to main URLs
    # path('payments/', include('payments.urls')),  # Payments API - moved to main URLs
    # path('social/', include('social.urls')),  # Social features API - moved to main URLs

    # Media optimization API
    path('posts/<int:post_id>/media/optimized/', views.post_media_optimized, name='api-post-media-optimized'),
    path('posts/<int:post_id>/media/gallery/', views.media_gallery, name='api-media-gallery'),
    path('media/<int:media_id>/optimize/', views.optimize_media, name='api-optimize-media'),
    path('media/<int:media_id>/progressive/', views.media_progressive_data, name='api-media-progressive'),
    path('media/batch-status/', views.media_batch_optimize_status, name='api-media-batch-status'),
    path('media/stats/', views.media_optimization_stats, name='api-media-stats'),

    path('', include(router.urls)),
]

urlpatterns = [
    
    # path('health/', include('health_check.urls')),

    
    # Web interface URLs
    path('', views.home, name='home'),
    
    # Author posts URLs
    path('authors/<str:username>/posts/', views.author_posts, name='author-posts'),
    # path('authors/<str:username>/posts/create/', views.create_post_as_author, name='author-post-create'),
    
    # Category URLs
    path('categories/', views.Category, name='categories'),
    # path('categories/<int:category_id>/posts/', views.category_posts, name='category-posts'),
    # path('categories/<int:category_id>/posts/create/', views.create_post_in_category, name='category-post-create'),
    
    # Tag URLs
    path('tags/', views.Tag, name='tags'),
    # path('tags/<str:tag_name>/posts/', views.posts_with_tag, name='tag-posts'),
    
    # Post URLs
    path('posts/create/', views.create_post, name='post-create'),
    path('posts/<slug:slug>/', views.post_detail, name='post-detail'),
    path('posts/<slug:slug>/edit/', views.edit_post, name='post-edit'),
    path('posts/<slug:slug>/delete/', views.delete_post, name='post-delete'),
    path('posts/<slug:slug>/like/', views.like_post, name='post-like'),
    path('posts/<slug:slug>/comment/', views.add_comment, name='post-comment'),
    
    # Comment URLs
    path('comments/<int:comment_id>/like/', views.like_comment, name='comment-like'),
    
    # User URLs
    path('users/register/', views.register, name='user-register'),
    path('users/<str:username>/', views.author_posts, name='author-posts'),
    path('profile/', views.profile, name='profile'),
    path('profile/edit/', views.edit_profile, name='profile-edit'),
    
    # Newsletter
    path('newsletter/subscribe/', views.subscribe_newsletter, name='newsletter-subscribe'),

    # Additional pages
    path('about/', views.about, name='about'),
    path('support/', views.support, name='support'),
    path('contact/', views.contact, name='contact'),
    path('privacy/', views.privacy_policy, name='privacy-policy'),
    path('terms/', views.terms_of_service, name='terms-of-service'),
    path('settings/', views.settings_view, name='settings'),
    path('notifications/', views.notifications_view, name='notifications'),
    path('delete-account/', views.delete_account, name='delete-account'),

        # API Versioning

    path('api/v1/', include(api_patterns)),

]