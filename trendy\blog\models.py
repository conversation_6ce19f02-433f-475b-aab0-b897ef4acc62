import uuid
import json
from django.db import models
from django.contrib.auth import get_user_model
from django.conf import settings
from django.dispatch import receiver
from django.utils import timezone
from django.utils.text import slugify
from django.urls import reverse
from django.core.validators import FileExtensionValidator

User = get_user_model()
from django.core.exceptions import ValidationError
from django.db import models
from django.core.validators import FileExtensionValidator
from django.core.validators import URLValidator
from django.core.exceptions import ValidationError



# Create your models here.

def validate_file_size(value):
    filesize = value.size
    # Different limits for different file types
    if value.name.endswith(('.jpg', '.png', '.jpeg')):
        max_size = 10 * 1024 * 1024  # 10MB
    else:  # Videos
        max_size = 100 * 1024 * 1024  # 100MB
        
    if filesize > max_size:
        raise ValidationError(f"Maximum file size is {max_size/1024/1024}MB")
    
    
class Tag(models.Model):
    name = models.CharField(max_length=50, unique=True)
    slug = models.SlugField(unique=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            slug = slugify(self.name)
            while Tag.objects.filter(slug=slug).exists():
                slug = f"{slug}-{uuid.uuid4().hex[:4]}"
            self.slug = slug
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name
    
class HeroImage(models.Model):
    title = models.CharField(max_length=200)
    image = models.ImageField(upload_to='hero_images/', blank=True, null=True)
    image_url = models.URLField(blank=True, null=True)
    display_duration = models.PositiveIntegerField(
        default=5000,
        help_text="Display duration in milliseconds (default: 5000 = 5 seconds)"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title

    @property
    def image_source(self):
        return self.image.url if self.image else self.image_url
    
    def clean(self):
        if self.image and self.image_url:
            raise ValidationError("Cannot have both image file and URL")
        if not self.image and not self.image_url:
            raise ValidationError("Must have either image file or URL")

class Category(models.Model):
    name = models.CharField(max_length=100)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    class Meta:
        verbose_name_plural = 'categories'
        ordering = ['-created_at']  # Add this if missing

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            slug = slugify(self.name)
            while Category.objects.filter(slug=slug).exists():
                slug = f"{slug}-{uuid.uuid4().hex[:4]}"
            self.slug = slug
        super().save(*args, **kwargs)

class Post(models.Model):
    title = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    content = models.TextField()
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='posts')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='posts')
    tags = models.ManyToManyField(Tag, related_name='posts')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    views = models.PositiveIntegerField(default=0)
    likes = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name='liked_posts', blank=True)
    is_featured = models.BooleanField(default=False)
    sanitized_content = models.TextField(editable=False)
    is_sanitized = models.BooleanField(default=False)
    status = models.CharField(
        max_length=10,
        choices=[
            ('draft', 'Draft'),
            ('published', 'Published'),
            ('archived', 'Archived')
        ],
        default='draft'
    )
    reference = models.JSONField(
        default=list,
        help_text="Structured references in JSON format",
        blank=True
    )

    # Regional targeting fields
    is_global = models.BooleanField(
        default=False,
        help_text="If True, this post is visible to all users regardless of location"
    )
    target_countries = models.ManyToManyField(
        'regional.Country',
        blank=True,
        related_name='targeted_posts',
        help_text="Countries where this post should be visible. Leave empty for global posts."
    )
    regional_priority = models.PositiveIntegerField(
        default=0,
        help_text="Priority for regional content (higher = more prominent in region)"
    )
    class Meta:
        indexes = [
            models.Index(fields=['-created_at', 'status']),
            models.Index(fields=['slug']),
            models.Index(fields=['author', 'is_featured']),
            models.Index(fields=['is_global', 'status']),
            models.Index(fields=['regional_priority', '-created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            slug = slugify(self.title)
            while Post.objects.filter(slug=slug).exists():
                slug = f"{slug}-{uuid.uuid4().hex[:4]}"
            self.slug = slug
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('post_detail', kwargs={'slug': self.slug})

    @property
    def like_count(self):
        return self.likes.count()

    def can_edit(self, user):
        return user.is_staff or user == self.author

    def can_delete(self, user):
        return user.is_staff or user == self.author
    
    @property
    def images(self):
        return self.media_items.filter(media_type='image')
    
    @property
    def videos(self):
        return self.media_items.filter(media_type='video')

# class PostImage(models.Model):
#     post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='images')
#     image = models.ImageField(upload_to='blog_images/')
#     image_url = models.URLField(blank=True, null=True)
#     caption = models.CharField(max_length=200, blank=True)
#     order = models.PositiveIntegerField(default=0)
#     created_at = models.DateTimeField(auto_now_add=True)

#     class Meta:
#         ordering = ['order', 'created_at']

#     def __str__(self):
#         return f"Image for {self.post.title}"
    
#     @property
#     def image_source(self):
#         return self.image.url if self.image else self.image_url

# class PostVideo(models.Model):
#     post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='videos')
#     video = models.FileField(
#         upload_to='blog_videos/',
#         validators=[
#             FileExtensionValidator(allowed_extensions=['mp4', 'webm', 'ogg', 'mkv']),
#             validate_file_size
#         ]
#     )
#     thumbnail = models.ImageField(upload_to='video_thumbnails/', null=True, blank=True)
#     title = models.CharField(max_length=200, blank=True)
#     description = models.TextField(blank=True)
#     order = models.PositiveIntegerField(default=0)
#     created_at = models.DateTimeField(auto_now_add=True)

#     class Meta:
#         ordering = ['order', 'created_at']

#     def __str__(self):
#         return f"Video for {self.post.title}"

class PostMedia(models.Model):
    MEDIA_TYPE_CHOICES = [
        ('image', 'Image'),
        ('video', 'Video'),
    ]

    post = models.ForeignKey(
        Post, 
        on_delete=models.CASCADE,
        related_name='media_items'
    )
    media_type = models.CharField(
        max_length=5,
        choices=MEDIA_TYPE_CHOICES,
        default='image'
    )
    
    # Common fields
    caption = models.CharField(max_length=200, blank=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    
    # Image-specific fields
    image = models.ImageField(
        upload_to='blog_media/images/',
        blank=True,
        null=True
    )
    image_url = models.URLField(blank=True, null=True)
    video_url = models.URLField(
        blank=True,
        null=True,
        validators=[URLValidator(schemes=['http', 'https', 'ftp', 'ftps'])]
    )
    
    # Video-specific fields
    video = models.FileField(
        upload_to='blog_media/videos/',
        validators=[
            FileExtensionValidator(allowed_extensions=['mp4', 'webm', 'ogg', 'mkv']),
            validate_file_size
        ],
        blank=True,
        null=True
    )
    thumbnail = models.ImageField(
        upload_to='blog_media/thumbnails/',
        blank=True,
        null=True
    )
    title = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)

    # Media optimization fields
    optimized_urls = models.JSONField(
        default=dict,
        blank=True,
        help_text="Optimized image URLs for different sizes"
    )
    media_metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="Media metadata including dimensions, format, etc."
    )
    is_optimized = models.BooleanField(default=False)
    file_hash = models.CharField(max_length=32, blank=True, null=True)

    # Progressive loading data
    placeholder_url = models.URLField(blank=True, null=True)
    srcset = models.TextField(blank=True, help_text="Responsive image srcset")

    class Meta:
        ordering = ['order', 'created_at']
        verbose_name_plural = 'Post Media'

    def __str__(self):
        return f"{self.get_media_type_display()} for {self.post.title}"

    @property
    def media_source(self):
        if self.media_type == 'image':
            return self.image.url if self.image else self.image_url
        return self.video.url if self.video else None

    @property
    def preview_image(self):
        if self.media_type == 'image':
            return self.image.url if self.image else self.image_url
        return self.thumbnail.url if self.thumbnail else None

    def get_optimized_url(self, size='medium'):
        """Get optimized URL for specific size"""
        if self.is_optimized and self.optimized_urls:
            return self.optimized_urls.get(size, self.media_source)
        return self.media_source

    def get_responsive_data(self):
        """Get data for responsive image loading"""
        if not self.is_optimized:
            return {
                'src': self.media_source,
                'placeholder': self.placeholder_url or self.media_source
            }

        return {
            'src': self.get_optimized_url('large'),
            'srcset': self.srcset,
            'placeholder': self.placeholder_url or self.get_optimized_url('thumbnail'),
            'sizes': '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
            'loading': 'lazy'
        }

    def optimize_media(self):
        """Optimize media file and generate variants"""
        if self.media_type == 'image' and self.image:
            from .services.media_optimization import MediaOptimizationService

            optimization_result = MediaOptimizationService.optimize_image(
                self.image,
                sizes=['thumbnail', 'small', 'medium', 'large']
            )

            if optimization_result:
                self.optimized_urls = optimization_result.get('urls', {})
                self.media_metadata = optimization_result.get('metadata', {})
                self.file_hash = optimization_result.get('hash')
                self.is_optimized = True

                # Generate srcset
                self.srcset = MediaOptimizationService.create_image_srcset(self.optimized_urls)

                # Set placeholder
                self.placeholder_url = self.optimized_urls.get('thumbnail')

                self.save(update_fields=[
                    'optimized_urls', 'media_metadata', 'file_hash',
                    'is_optimized', 'srcset', 'placeholder_url'
                ])

    def clean(self):
        super().clean()
        
        # Validate image/video exclusivity
        if self.media_type == 'video' and not self.video and not self.video_url:
            raise ValidationError("Video media requires either a file or URL")
        
        if self.media_type == 'image' and self.video:
            raise ValidationError("Image media cannot have a video file")
            
        if self.media_type == 'video' and (self.image or self.image_url):
            raise ValidationError("Video media cannot have image fields populated")

        # Validate URL formats
        if self.image_url and not self.image_url.startswith(('http://', 'https://')):
            raise ValidationError("Image URL must be a valid HTTP/HTTPS URL")         
        # raise ValidationError("Video media requires a video file")

class Comment(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='comments')
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    like_count = models.PositiveIntegerField(default=0)
    content = models.TextField()
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='replies')
    likes = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name='liked_comments', blank=True)
    sanitized_content = models.TextField(editable=False)
    is_sanitized = models.BooleanField(default=False)
    class Meta:
        indexes = [
            models.Index(fields=['-created_at', 'post']),
            models.Index(fields=['parent'])
        ]
        ordering = ['-created_at']
        

    def __str__(self):
        return f'Comment by {self.author.username} on {self.post.title}'

    def get_like_count(self):
        return self.likes.count()
    
    # @receiver(m2m_changed, sender=Comment.likes.through)
    # def update_comment_like_count(sender, instance, action, **kwargs):
    #     if action in ['post_add', 'post_remove', 'post_clear']:
    #         instance.like_count = instance.likes.count()
    #         instance.save(update_fields=['like_count'])

class Newsletter(models.Model):
    email = models.EmailField(unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.email
