from django.urls import path
from . import views

app_name = 'messaging'

urlpatterns = [
    # Conversation endpoints
    path('conversations/', views.ConversationListCreateView.as_view(), name='conversation-list-create'),
    path('conversations/<int:pk>/', views.ConversationDetailView.as_view(), name='conversation-detail'),
    path('conversations/<int:conversation_id>/messages/', views.MessageListCreateView.as_view(), name='message-list-create'),
    path('conversations/<int:conversation_id>/mark-read/', views.mark_conversation_read, name='mark-conversation-read'),
    
    # Message endpoints
    path('messages/<int:pk>/', views.MessageDetailView.as_view(), name='message-detail'),
    
    # Utility endpoints
    path('start-conversation/', views.start_conversation, name='start-conversation'),
    path('unread-count/', views.get_unread_count, name='unread-count'),
]
