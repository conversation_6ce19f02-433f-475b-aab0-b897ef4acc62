"""
Django admin interface for system maintenance and feature management
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from django.urls import reverse
from django.http import HttpResponseRedirect
from django.contrib import messages
from django.db import models
from django.forms import Textarea
from .models import SystemMaintenance, FeatureToggle, MaintenanceLog, PREDEFINED_FEATURES


@admin.register(SystemMaintenance)
class SystemMaintenanceAdmin(admin.ModelAdmin):
    list_display = [
        'title', 
        'maintenance_type', 
        'status_badge', 
        'scheduled_start', 
        'scheduled_end',
        'is_active_badge',
        'created_by'
    ]
    list_filter = [
        'maintenance_type', 
        'status', 
        'is_active', 
        'allow_admin_access',
        'created_at'
    ]
    search_fields = ['title', 'description']
    readonly_fields = ['actual_start', 'actual_end', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'maintenance_type', 'status')
        }),
        ('Scheduling', {
            'fields': ('scheduled_start', 'scheduled_end', 'actual_start', 'actual_end')
        }),
        ('Access Control', {
            'fields': ('is_active', 'allow_admin_access', 'allow_staff_access')
        }),
        ('Messages', {
            'fields': ('public_message', 'admin_message')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['activate_maintenance', 'deactivate_maintenance', 'mark_completed']
    
    def status_badge(self, obj):
        colors = {
            'scheduled': '#ffc107',  # yellow
            'active': '#dc3545',     # red
            'completed': '#28a745',  # green
            'cancelled': '#6c757d',  # gray
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 12px;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'
    
    def is_active_badge(self, obj):
        if obj.is_active:
            return format_html(
                '<span style="background-color: #dc3545; color: white; padding: 3px 8px; '
                'border-radius: 3px; font-size: 12px;">🔴 ACTIVE</span>'
            )
        return format_html(
            '<span style="background-color: #28a745; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 12px;">🟢 INACTIVE</span>'
        )
    is_active_badge.short_description = 'Active Status'
    
    def save_model(self, request, obj, form, change):
        if not change:  # New object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
        
        # Log the action
        MaintenanceLog.objects.create(
            maintenance=obj,
            log_type='update' if change else 'start',
            message=f'Maintenance {"updated" if change else "created"} by {request.user.username}',
            user=request.user,
            details={'action': 'admin_save', 'changes': form.changed_data if change else []}
        )
    
    def activate_maintenance(self, request, queryset):
        count = 0
        for maintenance in queryset:
            maintenance.is_active = True
            maintenance.status = 'active'
            maintenance.save()
            count += 1
            
            MaintenanceLog.objects.create(
                maintenance=maintenance,
                log_type='start',
                message=f'Maintenance activated by {request.user.username}',
                user=request.user
            )
        
        messages.success(request, f'Activated {count} maintenance(s)')
    activate_maintenance.short_description = 'Activate selected maintenance'
    
    def deactivate_maintenance(self, request, queryset):
        count = 0
        for maintenance in queryset:
            maintenance.is_active = False
            maintenance.save()
            count += 1
            
            MaintenanceLog.objects.create(
                maintenance=maintenance,
                log_type='end',
                message=f'Maintenance deactivated by {request.user.username}',
                user=request.user
            )
        
        messages.success(request, f'Deactivated {count} maintenance(s)')
    deactivate_maintenance.short_description = 'Deactivate selected maintenance'
    
    def mark_completed(self, request, queryset):
        count = 0
        for maintenance in queryset:
            maintenance.status = 'completed'
            maintenance.is_active = False
            if not maintenance.actual_end:
                maintenance.actual_end = timezone.now()
            maintenance.save()
            count += 1
            
            MaintenanceLog.objects.create(
                maintenance=maintenance,
                log_type='end',
                message=f'Maintenance marked completed by {request.user.username}',
                user=request.user
            )
        
        messages.success(request, f'Marked {count} maintenance(s) as completed')
    mark_completed.short_description = 'Mark selected as completed'


@admin.register(FeatureToggle)
class FeatureToggleAdmin(admin.ModelAdmin):
    list_display = [
        'display_name',
        'name',
        'feature_type',
        'enabled_badge',
        'user_access_summary',
        'timing_info'
    ]
    list_filter = [
        'feature_type',
        'is_enabled',
        'is_global',
        'enabled_for_admins',
        'enabled_for_staff',
        'enabled_for_users'
    ]
    search_fields = ['name', 'display_name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'description', 'feature_type')
        }),
        ('Toggle Settings', {
            'fields': ('is_enabled', 'is_global')
        }),
        ('User Access', {
            'fields': ('enabled_for_admins', 'enabled_for_staff', 'enabled_for_users')
        }),
        ('Timing', {
            'fields': ('disable_start', 'disable_end'),
            'description': 'Optional: Set specific times to disable/enable the feature'
        }),
        ('Messages', {
            'fields': ('disabled_message',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['enable_features', 'disable_features', 'reset_timing']
    
    def enabled_badge(self, obj):
        if obj.is_currently_enabled:
            return format_html(
                '<span style="background-color: #28a745; color: white; padding: 3px 8px; '
                'border-radius: 3px; font-size: 12px;">✅ ENABLED</span>'
            )
        return format_html(
            '<span style="background-color: #dc3545; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 12px;">❌ DISABLED</span>'
        )
    enabled_badge.short_description = 'Status'
    
    def user_access_summary(self, obj):
        access = []
        if obj.enabled_for_admins:
            access.append('👑 Admins')
        if obj.enabled_for_staff:
            access.append('👤 Staff')
        if obj.enabled_for_users:
            access.append('👥 Users')
        
        if not access:
            return format_html('<span style="color: #dc3545;">❌ No Access</span>')
        
        return format_html('<span style="color: #28a745;">{}</span>', ' | '.join(access))
    user_access_summary.short_description = 'User Access'
    
    def timing_info(self, obj):
        if obj.disable_start or obj.disable_end:
            now = timezone.now()
            if obj.disable_start and obj.disable_end:
                if obj.disable_start <= now <= obj.disable_end:
                    return format_html('<span style="color: #dc3545;">🕐 Currently Disabled</span>')
                elif now < obj.disable_start:
                    return format_html('<span style="color: #ffc107;">⏰ Scheduled Disable</span>')
                else:
                    return format_html('<span style="color: #28a745;">✅ Re-enabled</span>')
            elif obj.disable_start and now >= obj.disable_start:
                return format_html('<span style="color: #dc3545;">🕐 Disabled Since Start</span>')
        
        return format_html('<span style="color: #6c757d;">⏱️ No Timing</span>')
    timing_info.short_description = 'Timing'
    
    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        
        # Log the action
        MaintenanceLog.objects.create(
            feature_toggle=obj,
            log_type='feature_toggle',
            message=f'Feature "{obj.display_name}" {"updated" if change else "created"} by {request.user.username}',
            user=request.user,
            details={
                'action': 'admin_save',
                'enabled': obj.is_enabled,
                'changes': form.changed_data if change else []
            }
        )
    
    def enable_features(self, request, queryset):
        count = queryset.update(is_enabled=True)
        for feature in queryset:
            MaintenanceLog.objects.create(
                feature_toggle=feature,
                log_type='feature_toggle',
                message=f'Feature "{feature.display_name}" enabled by {request.user.username}',
                user=request.user
            )
        messages.success(request, f'Enabled {count} feature(s)')
    enable_features.short_description = 'Enable selected features'
    
    def disable_features(self, request, queryset):
        count = queryset.update(is_enabled=False)
        for feature in queryset:
            MaintenanceLog.objects.create(
                feature_toggle=feature,
                log_type='feature_toggle',
                message=f'Feature "{feature.display_name}" disabled by {request.user.username}',
                user=request.user
            )
        messages.success(request, f'Disabled {count} feature(s)')
    disable_features.short_description = 'Disable selected features'
    
    def reset_timing(self, request, queryset):
        count = queryset.update(disable_start=None, disable_end=None)
        messages.success(request, f'Reset timing for {count} feature(s)')
    reset_timing.short_description = 'Reset timing for selected features'


@admin.register(MaintenanceLog)
class MaintenanceLogAdmin(admin.ModelAdmin):
    list_display = ['timestamp', 'log_type_badge', 'message', 'user', 'related_object']
    list_filter = ['log_type', 'timestamp']
    search_fields = ['message', 'user__username']
    readonly_fields = ['timestamp', 'details_formatted']
    
    fieldsets = (
        ('Log Information', {
            'fields': ('log_type', 'message', 'user', 'timestamp')
        }),
        ('Related Objects', {
            'fields': ('maintenance', 'feature_toggle')
        }),
        ('Details', {
            'fields': ('details_formatted',),
            'classes': ('collapse',)
        })
    )
    
    def log_type_badge(self, obj):
        colors = {
            'start': '#28a745',      # green
            'end': '#17a2b8',        # blue
            'update': '#ffc107',     # yellow
            'feature_toggle': '#6f42c1',  # purple
            'error': '#dc3545',      # red
            'info': '#6c757d',       # gray
        }
        color = colors.get(obj.log_type, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 12px;">{}</span>',
            color, obj.get_log_type_display()
        )
    log_type_badge.short_description = 'Type'
    
    def related_object(self, obj):
        if obj.maintenance:
            return format_html('🔧 {}', obj.maintenance.title)
        elif obj.feature_toggle:
            return format_html('🎛️ {}', obj.feature_toggle.display_name)
        return '-'
    related_object.short_description = 'Related To'
    
    def details_formatted(self, obj):
        if obj.details:
            import json
            return format_html('<pre>{}</pre>', json.dumps(obj.details, indent=2))
        return 'No details'
    details_formatted.short_description = 'Details (JSON)'
    
    def has_add_permission(self, request):
        return False  # Logs are created automatically
    
    def has_change_permission(self, request, obj=None):
        return False  # Logs should not be modified


# Custom admin actions
def setup_predefined_features():
    """Setup predefined feature toggles"""
    created_count = 0
    for feature_data in PREDEFINED_FEATURES:
        feature, created = FeatureToggle.objects.get_or_create(
            name=feature_data['name'],
            defaults=feature_data
        )
        if created:
            created_count += 1
    
    return created_count


# Admin site customization
admin.site.site_header = "Trendy System Administration"
admin.site.site_title = "Trendy Admin"
admin.site.index_title = "System Management Dashboard"
