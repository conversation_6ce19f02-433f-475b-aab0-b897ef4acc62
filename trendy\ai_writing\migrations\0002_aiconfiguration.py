# Generated by Django 5.0.4 on 2025-08-03 19:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("ai_writing", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="AIConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Configuration name (e.g., 'Production AI', 'Development AI')",
                        max_length=100,
                        unique=True,
                    ),
                ),
                (
                    "provider",
                    models.Char<PERSON>ield(
                        choices=[
                            ("openai", "OpenAI (GPT)"),
                            ("anthropic", "Anthropic (<PERSON>)"),
                            ("google", "Google (Gemini)"),
                            ("huggingface", "Hugging Face"),
                            ("ollama", "Ollama (Local)"),
                            ("custom", "Custom API"),
                        ],
                        default="openai",
                        max_length=20,
                    ),
                ),
                (
                    "model_name",
                    models.<PERSON><PERSON><PERSON><PERSON>(
                        choices=[
                            ("gpt-4", "GPT-4"),
                            ("gpt-4-turbo", "GPT-4 Turbo"),
                            ("gpt-3.5-turbo", "GPT-3.5 Turbo"),
                            ("claude-3-opus", "Claude 3 Opus"),
                            ("claude-3-sonnet", "Claude 3 Sonnet"),
                            ("claude-3-haiku", "Claude 3 Haiku"),
                            ("gemini-pro", "Gemini Pro"),
                            ("gemini-pro-vision", "Gemini Pro Vision"),
                            ("custom-model", "Custom Model"),
                        ],
                        default="gpt-3.5-turbo",
                        max_length=50,
                    ),
                ),
                (
                    "api_key",
                    models.CharField(
                        help_text="API key for the AI provider", max_length=500
                    ),
                ),
                (
                    "api_base_url",
                    models.URLField(
                        blank=True,
                        help_text="Custom API base URL (for custom providers)",
                    ),
                ),
                (
                    "api_version",
                    models.CharField(
                        blank=True, help_text="API version (if required)", max_length=20
                    ),
                ),
                (
                    "max_tokens",
                    models.PositiveIntegerField(
                        default=1000, help_text="Maximum tokens per request"
                    ),
                ),
                (
                    "temperature",
                    models.FloatField(
                        default=0.7,
                        help_text="Temperature for response creativity (0.0-2.0)",
                    ),
                ),
                (
                    "top_p",
                    models.FloatField(
                        default=1.0, help_text="Top-p sampling parameter (0.0-1.0)"
                    ),
                ),
                (
                    "frequency_penalty",
                    models.FloatField(
                        default=0.0, help_text="Frequency penalty (-2.0 to 2.0)"
                    ),
                ),
                (
                    "presence_penalty",
                    models.FloatField(
                        default=0.0, help_text="Presence penalty (-2.0 to 2.0)"
                    ),
                ),
                (
                    "requests_per_minute",
                    models.PositiveIntegerField(
                        default=60, help_text="Maximum requests per minute"
                    ),
                ),
                (
                    "requests_per_day",
                    models.PositiveIntegerField(
                        default=1000, help_text="Maximum requests per day"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Enable/disable this AI configuration"
                    ),
                ),
                (
                    "is_default",
                    models.BooleanField(
                        default=False, help_text="Use as default AI configuration"
                    ),
                ),
                ("enable_content_generation", models.BooleanField(default=True)),
                ("enable_grammar_improvement", models.BooleanField(default=True)),
                ("enable_seo_suggestions", models.BooleanField(default=True)),
                ("enable_text_completion", models.BooleanField(default=True)),
                ("enable_readability_analysis", models.BooleanField(default=True)),
                (
                    "custom_headers",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional headers for API requests (JSON format)",
                    ),
                ),
                (
                    "total_requests",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
                (
                    "total_tokens_used",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
                (
                    "last_used",
                    models.DateTimeField(blank=True, editable=False, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "AI Configuration",
                "verbose_name_plural": "AI Configurations",
                "ordering": ["-is_default", "-is_active", "name"],
            },
        ),
    ]
