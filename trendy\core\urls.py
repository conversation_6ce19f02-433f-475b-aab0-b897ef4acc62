"""
URL configuration for core maintenance system
"""
from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    # System status endpoints
    path('api/v1/system/status/', views.system_status, name='system_status'),
    path('api/v1/system/health/', views.health_check, name='health_check'),
    path('api/v1/system/features/', views.features_list, name='features_list'),
    path('api/v1/system/feature/<str:feature_name>/', views.feature_status, name='feature_status'),
    
    # Simple maintenance status (for non-authenticated requests)
    path('maintenance/status/', views.MaintenanceStatusView.as_view(), name='maintenance_status'),
]
