from rest_framework import status, viewsets, permissions
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone

from .models import (
    AIConfiguration, AIWritingPreferences, ContentPrompt, AIWritingSession,
    ContentSuggestion, AIUsageAnalytics, ContentTemplate
)
from .serializers import (
    AIConfigurationSerializer, AIWritingPreferencesSerializer, ContentPromptSerializer,
    AIWritingSessionSerializer, ContentSuggestionSerializer,
    ContentTemplateSerializer, AIUsageAnalyticsSerializer,
    ContentIdeaRequestSerializer, ContentIdeaResponseSerializer,
    ContentOutlineRequestSerializer, ContentOutlineResponseSerializer,
    GrammarImprovementRequestSerializer, GrammarImprovementResponseSerializer,
    SEOSuggestionsRequestSerializer, SEOSuggestionsResponseSerializer,
    TextCompletionRequestSerializer, TextCompletionResponseSerializer,
    ReadabilityAnalysisRequestSerializer, ReadabilityAnalysisResponseSerializer
)
from .services import AIWritingService
from blog.permissions import IsContentCreatorOrReadOnly


class AIWritingPreferencesViewSet(viewsets.ModelViewSet):
    """ViewSet for managing AI writing preferences"""
    serializer_class = AIWritingPreferencesSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return AIWritingPreferences.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['get', 'post'])
    def user_preferences(self, request):
        """Get or update user's AI writing preferences"""
        try:
            preferences = AIWritingPreferences.objects.get(user=request.user)
        except AIWritingPreferences.DoesNotExist:
            # Create default preferences if they don't exist
            preferences = AIWritingPreferences.objects.create(user=request.user)

        if request.method == 'GET':
            serializer = self.get_serializer(preferences)
            return Response(serializer.data)

        elif request.method == 'POST':
            serializer = self.get_serializer(preferences, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ContentPromptViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for content prompts (read-only for users)"""
    serializer_class = ContentPromptSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return ContentPrompt.objects.filter(is_active=True)


class AIWritingSessionViewSet(viewsets.ModelViewSet):
    """ViewSet for AI writing sessions"""
    serializer_class = AIWritingSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return AIWritingSession.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class ContentSuggestionViewSet(viewsets.ModelViewSet):
    """ViewSet for content suggestions"""
    serializer_class = ContentSuggestionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return ContentSuggestion.objects.filter(session__user=self.request.user)


class ContentTemplateViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for content templates"""
    serializer_class = ContentTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return ContentTemplate.objects.filter(is_public=True)


# AI Writing Assistant API Views
class GenerateContentIdeasView(APIView):
    """Generate content ideas based on topic"""
    permission_classes = [IsContentCreatorOrReadOnly]
    
    def post(self, request):
        serializer = ContentIdeaRequestSerializer(data=request.data)
        if serializer.is_valid():
            topic = serializer.validated_data['topic']
            count = serializer.validated_data['count']
            
            ai_service = AIWritingService()
            ideas = ai_service.generate_content_ideas(topic, request.user, count)
            
            # Track usage
            ai_service.track_usage(request.user, 'content_ideas', time_saved=2)
            
            response_serializer = ContentIdeaResponseSerializer({'ideas': ideas})
            return Response(response_serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GenerateContentOutlineView(APIView):
    """Generate content outline for a title"""
    permission_classes = [IsContentCreatorOrReadOnly]
    
    def post(self, request):
        serializer = ContentOutlineRequestSerializer(data=request.data)
        if serializer.is_valid():
            title = serializer.validated_data['title']
            
            ai_service = AIWritingService()
            outline = ai_service.generate_content_outline(title, request.user)
            
            # Track usage
            ai_service.track_usage(request.user, 'content_outline', time_saved=5)
            
            response_serializer = ContentOutlineResponseSerializer(outline)
            return Response(response_serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ImproveGrammarView(APIView):
    """Improve grammar and style of text"""
    permission_classes = [IsContentCreatorOrReadOnly]
    
    def post(self, request):
        serializer = GrammarImprovementRequestSerializer(data=request.data)
        if serializer.is_valid():
            text = serializer.validated_data['text']
            
            ai_service = AIWritingService()
            improvement = ai_service.improve_grammar_and_style(text, request.user)
            
            # Track usage
            words_generated = len(improvement.get('improved_text', '').split())
            ai_service.track_usage(request.user, 'grammar_improvement', words_generated=words_generated, time_saved=3)
            
            response_serializer = GrammarImprovementResponseSerializer(improvement)
            return Response(response_serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GenerateSEOSuggestionsView(APIView):
    """Generate SEO optimization suggestions"""
    permission_classes = [IsContentCreatorOrReadOnly]
    
    def post(self, request):
        serializer = SEOSuggestionsRequestSerializer(data=request.data)
        if serializer.is_valid():
            content = serializer.validated_data['content']
            title = serializer.validated_data.get('title', '')
            
            ai_service = AIWritingService()
            suggestions = ai_service.generate_seo_suggestions(content, title)
            
            # Track usage
            ai_service.track_usage(request.user, 'seo_suggestions', time_saved=4)
            
            response_serializer = SEOSuggestionsResponseSerializer(suggestions)
            return Response(response_serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CompleteTextView(APIView):
    """Complete partial text using AI"""
    permission_classes = [IsContentCreatorOrReadOnly]
    
    def post(self, request):
        serializer = TextCompletionRequestSerializer(data=request.data)
        if serializer.is_valid():
            partial_text = serializer.validated_data['partial_text']
            context = serializer.validated_data.get('context', '')
            
            ai_service = AIWritingService()
            completed_text = ai_service.complete_text(partial_text, request.user, context)
            
            # Track usage
            words_generated = len(completed_text.split())
            ai_service.track_usage(request.user, 'text_completion', words_generated=words_generated, time_saved=1)
            
            response_serializer = TextCompletionResponseSerializer({'completed_text': completed_text})
            return Response(response_serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AnalyzeReadabilityView(APIView):
    """Analyze content readability"""
    permission_classes = [IsContentCreatorOrReadOnly]
    
    def post(self, request):
        serializer = ReadabilityAnalysisRequestSerializer(data=request.data)
        if serializer.is_valid():
            content = serializer.validated_data['content']
            
            ai_service = AIWritingService()
            analysis = ai_service.analyze_readability(content)
            
            # Track usage
            ai_service.track_usage(request.user, 'readability_analysis', time_saved=1)
            
            response_serializer = ReadabilityAnalysisResponseSerializer(analysis)
            return Response(response_serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_user_preferences(request):
    """Get or create user AI writing preferences"""
    ai_service = AIWritingService()
    preferences = ai_service.get_user_preferences(request.user)
    serializer = AIWritingPreferencesSerializer(preferences)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([IsContentCreatorOrReadOnly])
def create_writing_session(request):
    """Create a new AI writing session"""
    post_id = request.data.get('post_id')
    post = None
    
    if post_id:
        from blog.models import Post
        try:
            post = Post.objects.get(id=post_id, author=request.user)
        except Post.DoesNotExist:
            return Response({'error': 'Post not found'}, status=status.HTTP_404_NOT_FOUND)
    
    ai_service = AIWritingService()
    session = ai_service.create_writing_session(request.user, post)
    serializer = AIWritingSessionSerializer(session)
    return Response(serializer.data, status=status.HTTP_201_CREATED)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_ai_config_info(request):
    """Get current AI configuration information"""
    config = AIConfiguration.get_default_config()
    if config:
        serializer = AIConfigurationSerializer(config)
        return Response(serializer.data)
    else:
        return Response({
            'error': 'No AI configuration available',
            'message': 'Please contact administrator to set up AI configuration'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_usage_analytics(request):
    """Get user's AI usage analytics"""
    analytics = AIUsageAnalytics.objects.filter(user=request.user).order_by('-year', '-month')
    serializer = AIUsageAnalyticsSerializer(analytics, many=True)
    return Response(serializer.data)
