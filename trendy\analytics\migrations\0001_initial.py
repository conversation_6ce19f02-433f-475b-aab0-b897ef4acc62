# Generated by Django 5.2.3 on 2025-06-23 13:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('blog', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ContentAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('estimated_reading_time', models.IntegerField()),
                ('complexity_score', models.FloatField(default=0.0)),
                ('word_count', models.IntegerField(default=0)),
                ('sentence_count', models.IntegerField(default=0)),
                ('paragraph_count', models.IntegerField(default=0)),
                ('readability_score', models.FloatField(default=0.0)),
                ('reading_level', models.CharField(default='Unknown', max_length=50)),
                ('average_words_per_sentence', models.FloatField(default=0.0)),
                ('average_syllables_per_word', models.FloatField(default=0.0)),
                ('total_reading_time', models.IntegerField(default=0)),
                ('completion_rate', models.FloatField(default=0.0)),
                ('average_session_duration', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('post', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='content_analytics', to='blog.post')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReadingSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_time', models.DateTimeField(auto_now_add=True)),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('progress_percentage', models.FloatField(default=0.0)),
                ('reading_speed_wpm', models.IntegerField(blank=True, null=True)),
                ('session_duration', models.IntegerField(default=0)),
                ('scroll_depth', models.FloatField(default=0.0)),
                ('is_completed', models.BooleanField(default=False)),
                ('device_type', models.CharField(default='unknown', max_length=20)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reading_sessions', to='blog.post')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reading_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-start_time'],
                'indexes': [models.Index(fields=['user', 'post'], name='analytics_r_user_id_fe54ed_idx'), models.Index(fields=['start_time'], name='analytics_r_start_t_e0147d_idx')],
            },
        ),
    ]
