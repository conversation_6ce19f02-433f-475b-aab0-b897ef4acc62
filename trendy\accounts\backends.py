from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.db.models import Q

User = get_user_model()


class EmailOrUsernameModelBackend(ModelBackend):
    """
    Custom authentication backend that allows users to log in using either
    their username or email address.
    """
    
    def authenticate(self, request, username=None, password=None, email=None, **kwargs):
        if username is None and email is None:
            return None
            
        if password is None:
            return None
        
        try:
            # If email is provided, use it directly
            if email:
                user = User.objects.get(email=email)
            # If username contains @, treat it as email
            elif username and '@' in username:
                user = User.objects.get(email=username)
            # Otherwise, treat it as username
            else:
                user = User.objects.get(username=username)
                
        except User.DoesNotExist:
            # Run the default password hasher once to reduce the timing
            # difference between an existing and a nonexistent user
            User().set_password(password)
            return None
        
        # Check password and user status
        if user.check_password(password) and self.user_can_authenticate(user):
            return user
        
        return None
