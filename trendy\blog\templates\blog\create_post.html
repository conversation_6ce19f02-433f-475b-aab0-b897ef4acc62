{% extends 'blog/base.html' %}
{% load static %}

{% block title %}Create New Post - Trendy Blog{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h1 class="card-title mb-4">{% if post %}Edit Post{% else %}Create New Post{% endif %}</h1>
                    
                    <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        {% if form.errors %}
                        <div class="alert alert-danger">
                            <h5>Please correct the errors below:</h5>
                            <ul>
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <!-- AI Assistance Toggle -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    {{ form.use_ai_assistance }}
                                    <label class="form-check-label" for="{{ form.use_ai_assistance.id_for_label }}">
                                        <i class="fas fa-robot"></i> Enable AI Writing Assistant
                                    </label>
                                </div>
                                <small class="form-text text-muted">{{ form.use_ai_assistance.help_text }}</small>
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.ai_tone.id_for_label }}" class="form-label">AI Tone</label>
                                {{ form.ai_tone }}
                                <small class="form-text text-muted">{{ form.ai_tone.help_text }}</small>
                            </div>
                        </div>

                        <!-- Title with AI assistance -->
                        <div class="mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">
                                Title
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2 ai-suggest-btn" data-target="title" style="display: none;">
                                    <i class="fas fa-lightbulb"></i> AI Suggestions
                                </button>
                            </label>
                            {{ form.title }}
                            <div id="title-suggestions" class="ai-suggestions mt-2" style="display: none;"></div>
                            {% if form.title.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.title.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Category -->
                        <div class="mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                            {{ form.category }}
                            {% if form.category.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.category.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Content with AI assistance -->
                        <div class="mb-3">
                            <label for="{{ form.content.id_for_label }}" class="form-label">
                                Content
                                <div class="btn-group ms-2 ai-content-tools" style="display: none;">
                                    <button type="button" class="btn btn-sm btn-outline-primary" id="generate-outline-btn">
                                        <i class="fas fa-list"></i> Generate Outline
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" id="improve-grammar-btn">
                                        <i class="fas fa-spell-check"></i> Improve Grammar
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-info" id="seo-suggestions-btn">
                                        <i class="fas fa-search"></i> SEO Suggestions
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-warning" id="readability-check-btn">
                                        <i class="fas fa-eye"></i> Readability
                                    </button>
                                </div>
                            </label>
                            {{ form.content }}

                            <!-- AI Suggestions Panel -->
                            <div id="ai-suggestions-panel" class="mt-3" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-robot"></i> AI Suggestions
                                            <button type="button" class="btn-close float-end" id="close-suggestions"></button>
                                        </h6>
                                    </div>
                                    <div class="card-body" id="suggestions-content">
                                        <!-- AI suggestions will be loaded here -->
                                    </div>
                                </div>
                            </div>

                            {% if form.content.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.content.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Unified Media Section -->
                        <div class="mb-4">
                            <label class="form-label">Media Files</label>
                            {{ form.media_files }}
                            <div id="media-preview-container" class="mt-3">
                                {% if post and post.media_items.exists %}
                                    {% for media in post.media_items.all %}
                                    <div class="media-preview-item mb-3" data-order="{{ media.order }}" data-type="{{ media.media_type }}">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span>{{ media.media_type|title }} #{{ media.order|add:"1" }}</span>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-secondary move-up" {% if forloop.first %}disabled{% endif %}>↑</button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary move-down" {% if forloop.last %}disabled{% endif %}>↓</button>
                                                </div>
                                            </div>
                                            
                                            {% if media.media_type == 'image' %}
                                            <img src="{{ media.media_source }}" class="card-img-top" alt="Preview">
                                            {% else %}
                                            <video class="card-img-top" controls>
                                                <source src="{{ media.media_source }}" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                            {% endif %}
                                            
                                            <div class="card-body">
                                                {% if media.media_type == 'image' %}
                                                <input type="text" name="media_captions[]" class="form-control mb-2" 
                                                    placeholder="Caption" value="{{ media.caption }}">
                                                {% else %}
                                                <input type="text" name="media_titles[]" class="form-control mb-2" 
                                                    placeholder="Title" value="{{ media.title }}">
                                                <textarea name="media_descriptions[]" class="form-control mb-2" 
                                                        placeholder="Description">{{ media.description }}</textarea>
                                                {% endif %}
                                                
                                                <input type="hidden" name="media_orders[]" value="{{ media.order }}">
                                                <input type="hidden" name="media_types[]" value="{{ media.media_type }}">
                                                <button type="button" class="btn btn-danger btn-sm remove-media">Remove</button>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>

                        <!-- Tags -->
                        <div class="mb-3">
                            <label for="{{ form.tags_input.id_for_label }}" class="form-label">Tags</label>
                            {{ form.tags_input }}
                            {% if form.tags_input.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.tags_input.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Status -->
                        <div class="mb-3">
                            <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                            {{ form.status }}
                            {% if form.status.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.status.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Featured -->
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_featured }}
                                <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                                    Mark as Featured
                                </label>
                            </div>
                        </div>

                        <!-- Reference -->
                        <div class="mb-3">
                            <label for="{{ form.reference.id_for_label }}" class="form-label">Reference</label>
                            {{ form.reference }}
                            {% if form.reference.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.reference.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">{% if post %}Update Post{% else %}Create Post{% endif %}</button>
                            <a href="{% url 'home' %}" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const mediaInput = document.querySelector('input[name="media_files"]');
        const mediaPreviewContainer = document.getElementById('media-preview-container');
    
        mediaInput.addEventListener('change', function(e) {
            const files = e.target.files;
            const currentOrder = mediaPreviewContainer.children.length;
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const isImage = file.type.startsWith('image/');
                const div = document.createElement('div');
                
                div.className = 'media-preview-item mb-3';
                div.dataset.order = currentOrder + i;
                div.dataset.type = isImage ? 'image' : 'video';
    
                if (isImage) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        createPreviewCard(div, e.target.result, isImage, currentOrder + i);
                    };
                    reader.readAsDataURL(file);
                } else {
                    const videoUrl = URL.createObjectURL(file);
                    createPreviewCard(div, videoUrl, isImage, currentOrder + i);
                }
            }
        });
    
        function createPreviewCard(container, url, isImage, order) {
            container.innerHTML = `
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>${isImage ? 'Image' : 'Video'} #${order + 1}</span>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-secondary move-up" disabled>↑</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary move-down">↓</button>
                        </div>
                    </div>
                    ${isImage ? 
                        `<img src="${url}" class="card-img-top" alt="Preview">` :
                        `<video class="card-img-top" controls>
                            <source src="${url}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>`
                    }
                    <div class="card-body">
                        ${isImage ? 
                            `<input type="text" name="media_captions[]" class="form-control mb-2" placeholder="Caption">` :
                            `<input type="text" name="media_titles[]" class="form-control mb-2" placeholder="Title">
                             <textarea name="media_descriptions[]" class="form-control mb-2" placeholder="Description"></textarea>`
                        }
                        <input type="hidden" name="media_orders[]" value="${order}">
                        <input type="hidden" name="media_types[]" value="${isImage ? 'image' : 'video'}">
                        <button type="button" class="btn btn-danger btn-sm remove-media">Remove</button>
                    </div>
                </div>
            `;
            mediaPreviewContainer.appendChild(container);
            updateOrderNumbers();
        }
    
        // Event delegation for dynamic elements
        document.addEventListener('click', function(e) {
            // Remove media
            if (e.target.classList.contains('remove-media')) {
                e.target.closest('.media-preview-item').remove();
                updateOrderNumbers();
            }
            if (item.dataset.type === 'video') {
                const videoUrl = item.querySelector('source')?.src;
                if (videoUrl) URL.revokeObjectURL(videoUrl);
            }
            
            // Move items
            if (e.target.classList.contains('move-up') || e.target.classList.contains('move-down')) {
                const item = e.target.closest('.media-preview-item');
                const parent = item.parentElement;
                
                if (e.target.classList.contains('move-up')) {
                    const prev = item.previousElementSibling;
                    if (prev) parent.insertBefore(item, prev);
                } else {
                    const next = item.nextElementSibling;
                    if (next) parent.insertBefore(next, item);
                }
                updateOrderNumbers();
            }
        });
    
        function updateOrderNumbers() {
            const items = mediaPreviewContainer.children;
            Array.from(items).forEach((item, index) => {
                item.dataset.order = index;
                const orderInput = item.querySelector('input[name="media_orders[]"]');
                if (orderInput) orderInput.value = index;
                
                // Update displayed number
                const numberSpan = item.querySelector('.card-header span');
                if (numberSpan) {
                    numberSpan.textContent = `${item.dataset.type === 'image' ? 'Image' : 'Video'} #${index + 1}`;
                }
                
                // Update button states
                const moveUp = item.querySelector('.move-up');
                const moveDown = item.querySelector('.move-down');
                moveUp.disabled = index === 0;
                moveDown.disabled = index === items.length - 1;
            });
        }
    
        // Form validation
        const forms = document.querySelectorAll('.needs-validation');
        forms.forEach(form => {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    });
    </script>
{% endblock %}

{% block extra_css %}
<style>
.image-preview-item img,
.video-preview-item video {
    max-height: 200px;
    object-fit: cover;
}

.card {
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--light-bg);
    border-bottom: 1px solid var(--border-color);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-secondary {
    color: var(--text-color);
    border-color: var(--border-color);
}

.btn-outline-secondary:hover {
    background-color: var(--light-bg);
    border-color: var(--border-color);
}

.btn-outline-secondary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.img-thumbnail {
    max-width: 100%;
    height: auto;
}

/* AI Assistance Styles */
.ai-suggestions {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}

.ai-suggestion-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
}

.ai-suggestion-item:hover {
    background: #e9ecef;
    border-color: #007bff;
}

.ai-loading {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.ai-enhanced-textarea, .ai-enhanced-input {
    position: relative;
}

.ai-content-tools {
    display: inline-block;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const useAICheckbox = document.getElementById('use-ai-assistance');
    const aiToneSelect = document.getElementById('ai-tone-select');
    const titleInput = document.getElementById('title-input');
    const contentTextarea = document.getElementById('content-textarea');
    const aiSuggestBtns = document.querySelectorAll('.ai-suggest-btn');
    const aiContentTools = document.querySelector('.ai-content-tools');
    const suggestionsPanel = document.getElementById('ai-suggestions-panel');

    // Toggle AI features
    function toggleAIFeatures() {
        const enabled = useAICheckbox.checked;
        aiSuggestBtns.forEach(btn => {
            btn.style.display = enabled ? 'inline-block' : 'none';
        });
        if (aiContentTools) {
            aiContentTools.style.display = enabled ? 'inline-block' : 'none';
        }
    }

    useAICheckbox.addEventListener('change', toggleAIFeatures);
    toggleAIFeatures(); // Initial state

    // AI API helper function
    async function callAIAPI(endpoint, data) {
        try {
            const response = await fetch(`/api/v1/ai-writing/${endpoint}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Token {{ request.user.auth_token.key }}',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('AI API Error:', error);
            showError('AI service temporarily unavailable. Please try again.');
            return null;
        }
    }

    // Show error message
    function showError(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-dismissible fade show';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.row'));
    }

    // Generate content ideas for title
    document.addEventListener('click', async function(e) {
        if (e.target.closest('.ai-suggest-btn[data-target="title"]')) {
            const topic = titleInput.value || 'blog post';
            const suggestionsDiv = document.getElementById('title-suggestions');

            suggestionsDiv.style.display = 'block';
            suggestionsDiv.innerHTML = '<div class="ai-loading"><i class="fas fa-spinner fa-spin"></i> Generating title suggestions...</div>';

            const result = await callAIAPI('generate/ideas', { topic, count: 5 });
            if (result && result.ideas) {
                suggestionsDiv.innerHTML = result.ideas.map(idea =>
                    `<div class="ai-suggestion-item" onclick="selectTitleSuggestion('${idea.replace(/'/g, "\\'")}')">
                        ${idea}
                    </div>`
                ).join('');
            }
        }
    });

    // Select title suggestion
    window.selectTitleSuggestion = function(title) {
        titleInput.value = title;
        document.getElementById('title-suggestions').style.display = 'none';
    };

    // Generate outline
    document.getElementById('generate-outline-btn')?.addEventListener('click', async function() {
        const title = titleInput.value;
        if (!title) {
            showError('Please enter a title first to generate an outline.');
            return;
        }

        showSuggestionsPanel('Generating content outline...');
        const result = await callAIAPI('generate/outline', { title });

        if (result) {
            const outlineHTML = `
                <h6>Content Outline for: "${title}"</h6>
                <div class="mb-3">
                    <strong>Introduction:</strong>
                    <ul>${result.introduction.map(point => `<li>${point}</li>`).join('')}</ul>
                </div>
                <div class="mb-3">
                    <strong>Main Sections:</strong>
                    ${result.main_sections.map(section => `
                        <div class="mb-2">
                            <strong>${section.title}</strong>
                            <ul>${section.points.map(point => `<li>${point}</li>`).join('')}</ul>
                        </div>
                    `).join('')}
                </div>
                <div class="mb-3">
                    <strong>Conclusion:</strong>
                    <ul>${result.conclusion.map(point => `<li>${point}</li>`).join('')}</ul>
                </div>
                <p><small>Estimated word count: ${result.estimated_word_count} words</small></p>
                <button class="btn btn-primary btn-sm" onclick="useOutline()">Use This Outline</button>
            `;
            showSuggestionsPanel(outlineHTML);
        }
    });

    // Use outline
    window.useOutline = function() {
        // This would insert the outline structure into the content textarea
        showError('Outline structure would be inserted into content area.');
        hideSuggestionsPanel();
    };

    // Improve grammar
    document.getElementById('improve-grammar-btn')?.addEventListener('click', async function() {
        const content = contentTextarea.value;
        if (!content) {
            showError('Please enter some content first.');
            return;
        }

        showSuggestionsPanel('Analyzing and improving grammar...');
        const result = await callAIAPI('improve/grammar', { text: content });

        if (result) {
            const changesHTML = result.changes.length > 0 ?
                result.changes.map(change => `
                    <div class="mb-2 p-2 border rounded">
                        <strong>Original:</strong> ${change.original}<br>
                        <strong>Improved:</strong> ${change.improved}<br>
                        <small class="text-muted">${change.reason}</small>
                    </div>
                `).join('') : '<p>No grammar improvements needed!</p>';

            const improvementHTML = `
                <h6>Grammar & Style Improvements</h6>
                <p><strong>Readability Score:</strong> ${result.readability_score}/10</p>
                <div class="mb-3">
                    <strong>Improved Text:</strong>
                    <div class="p-2 bg-light border rounded">${result.improved_text}</div>
                </div>
                <div class="mb-3">
                    <strong>Changes Made:</strong>
                    ${changesHTML}
                </div>
                <button class="btn btn-success btn-sm" onclick="applyImprovedText('${result.improved_text.replace(/'/g, "\\'")}')">Apply Improvements</button>
            `;
            showSuggestionsPanel(improvementHTML);
        }
    });

    // Apply improved text
    window.applyImprovedText = function(improvedText) {
        contentTextarea.value = improvedText;
        hideSuggestionsPanel();
    };

    // SEO suggestions
    document.getElementById('seo-suggestions-btn')?.addEventListener('click', async function() {
        const content = contentTextarea.value;
        const title = titleInput.value;

        if (!content) {
            showError('Please enter some content first.');
            return;
        }

        showSuggestionsPanel('Generating SEO suggestions...');
        const result = await callAIAPI('generate/seo', { content, title });

        if (result) {
            const seoHTML = `
                <h6>SEO Optimization Suggestions</h6>
                <div class="mb-3">
                    <strong>Alternative Titles:</strong>
                    <ul>${result.title_suggestions.map(title => `<li>${title}</li>`).join('')}</ul>
                </div>
                <div class="mb-3">
                    <strong>Meta Description:</strong>
                    <div class="p-2 bg-light border rounded">${result.meta_description}</div>
                </div>
                <div class="mb-3">
                    <strong>Recommended Keywords:</strong>
                    <div>${result.keywords.map(keyword => `<span class="badge bg-primary me-1">${keyword}</span>`).join('')}</div>
                </div>
                <div class="mb-3">
                    <strong>Content Suggestions:</strong>
                    <ul>${result.content_suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}</ul>
                </div>
            `;
            showSuggestionsPanel(seoHTML);
        }
    });

    // Readability check
    document.getElementById('readability-check-btn')?.addEventListener('click', async function() {
        const content = contentTextarea.value;
        if (!content) {
            showError('Please enter some content first.');
            return;
        }

        showSuggestionsPanel('Analyzing readability...');
        const result = await callAIAPI('analyze/readability', { content });

        if (result) {
            const readabilityHTML = `
                <h6>Readability Analysis</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Word Count:</strong> ${result.word_count}</p>
                        <p><strong>Sentences:</strong> ${result.sentence_count}</p>
                        <p><strong>Paragraphs:</strong> ${result.paragraph_count}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Avg Words/Sentence:</strong> ${result.avg_words_per_sentence}</p>
                        <p><strong>Reading Level:</strong> ${result.reading_level}</p>
                        <p><strong>Reading Time:</strong> ${result.estimated_reading_time} min</p>
                    </div>
                </div>
                <div class="mb-3">
                    <strong>Readability Score:</strong>
                    <div class="progress">
                        <div class="progress-bar" style="width: ${result.readability_score}%">${result.readability_score}/100</div>
                    </div>
                </div>
                ${result.suggestions.length > 0 ? `
                    <div class="mb-3">
                        <strong>Suggestions:</strong>
                        <ul>${result.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}</ul>
                    </div>
                ` : ''}
            `;
            showSuggestionsPanel(readabilityHTML);
        }
    });

    // Show suggestions panel
    function showSuggestionsPanel(content) {
        document.getElementById('suggestions-content').innerHTML = content;
        suggestionsPanel.style.display = 'block';
        suggestionsPanel.scrollIntoView({ behavior: 'smooth' });
    }

    // Hide suggestions panel
    function hideSuggestionsPanel() {
        suggestionsPanel.style.display = 'none';
    }

    // Close suggestions panel
    document.getElementById('close-suggestions')?.addEventListener('click', hideSuggestionsPanel);
});
</script>
{% endblock %}
{% endblock %}