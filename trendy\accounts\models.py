from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
import uuid
import os


def user_avatar_path(instance, filename):
    """Generate file path for user avatar"""
    ext = filename.split('.')[-1]
    filename = f'{uuid.uuid4()}.{ext}'
    return os.path.join('avatars', filename)


class CustomUser(AbstractUser):
    """Extended User model with additional fields"""
    email = models.EmailField(unique=True)
    bio = models.TextField(max_length=500, blank=True)
    avatar = models.ImageField(upload_to=user_avatar_path, blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True)
    date_of_birth = models.DateField(blank=True, null=True)
    location = models.CharField(max_length=100, blank=True)
    website = models.URLField(blank=True)
    
    # Email verification
    is_email_verified = models.BooleanField(default=False)
    email_verification_token = models.UUIDField(default=uuid.uuid4, editable=False)
    email_verification_sent_at = models.DateTimeField(blank=True, null=True)
    
    # Account settings
    receive_email_notifications = models.BooleanField(default=True)
    receive_push_notifications = models.BooleanField(default=True)
    is_profile_public = models.BooleanField(default=True)

    # Login tracking
    first_login_at = models.DateTimeField(null=True, blank=True)
    
    # Social links
    twitter_url = models.URLField(blank=True)
    linkedin_url = models.URLField(blank=True)
    github_url = models.URLField(blank=True)
    
    # Regional preferences
    preferred_country = models.ForeignKey(
        'regional.Country',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='preferred_users',
        help_text="User's preferred country for content filtering"
    )
    detected_country = models.ForeignKey(
        'regional.Country',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='detected_users',
        help_text="Country detected from user's IP address"
    )
    show_global_content = models.BooleanField(
        default=True,
        help_text="Whether to show global content alongside regional content"
    )
    auto_detect_location = models.BooleanField(
        default=True,
        help_text="Whether to automatically detect user's location"
    )

    # Timestamps
    updated_at = models.DateTimeField(auto_now=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']
    
    class Meta:
        db_table = 'auth_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
    
    def __str__(self):
        return self.email
    
    @property
    def full_name(self):
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username
    
    @property
    def avatar_url(self):
        if self.avatar:
            return self.avatar.url
        return None
    
    def save(self, *args, **kwargs):
        """Override save to auto-verify admin users"""
        # Auto-verify email for admin users (staff or superuser)
        if (self.is_staff or self.is_superuser) and not self.is_email_verified:
            self.is_email_verified = True

        super().save(*args, **kwargs)

    @property
    def requires_email_verification(self):
        """Check if user requires email verification"""
        # Admin users (staff or superuser) are exempt from email verification
        if self.is_staff or self.is_superuser:
            return False
        return not self.is_email_verified

    @property
    def effective_country(self):
        """Get the effective country for content filtering"""
        # If user has explicitly set a preferred country, use it
        if self.preferred_country:
            return self.preferred_country

        # If user has explicitly cleared preferred country (manual_clear in history),
        # respect their choice to use global content
        from regional.models import UserLocationHistory
        recent_clear = UserLocationHistory.objects.filter(
            user=self,
            detection_method='manual_clear',
            created_at__gte=timezone.now() - timezone.timedelta(days=30)  # Within 30 days
        ).first()

        if recent_clear:
            return None  # User wants global content

        # Otherwise, fall back to detected country if auto-detection is enabled
        if self.auto_detect_location and self.detected_country:
            return self.detected_country

    # Role management methods
    @property
    def is_content_creator(self):
        """Check if user is a content creator"""
        if self.is_staff or self.is_superuser:
            return True
        return self.groups.filter(name='Content Creators').exists()

    @property
    def is_regular_user(self):
        """Check if user is a regular user (not content creator or admin)"""
        if self.is_staff or self.is_superuser:
            return False
        return self.groups.filter(name='Regular Users').exists()

    @property
    def user_role(self):
        """Get user's primary role as a string"""
        if self.is_superuser:
            return 'superuser'
        elif self.is_staff:
            return 'admin'
        elif self.is_content_creator:
            return 'content_creator'
        elif self.is_regular_user:
            return 'regular_user'
        else:
            return 'unassigned'

    def can_create_content(self):
        """Check if user can create blog posts and interactive content"""
        if self.is_staff or self.is_superuser:
            return True
        return self.has_perm('blog.can_create_post')

    def can_moderate_content(self):
        """Check if user can moderate content"""
        if self.is_staff or self.is_superuser:
            return True
        return self.has_perm('blog.can_moderate_posts')

    def promote_to_content_creator(self):
        """Promote user to content creator role"""
        from django.contrib.auth.models import Group

        # Remove from regular users if present
        regular_users = Group.objects.filter(name='Regular Users').first()
        if regular_users:
            self.groups.remove(regular_users)

        # Add to content creators
        content_creators = Group.objects.filter(name='Content Creators').first()
        if content_creators:
            self.groups.add(content_creators)
            return True
        return False

    def demote_to_regular_user(self):
        """Demote user to regular user role"""
        from django.contrib.auth.models import Group

        # Remove from content creators if present
        content_creators = Group.objects.filter(name='Content Creators').first()
        if content_creators:
            self.groups.remove(content_creators)

        # Add to regular users
        regular_users = Group.objects.filter(name='Regular Users').first()
        if regular_users:
            self.groups.add(regular_users)
            return True
        return False

        return None

    def send_verification_email(self):
        """Send email verification email"""
        # Don't send verification email to admin users
        if self.is_staff or self.is_superuser:
            return

        if not self.is_email_verified:
            subject = 'Verify your Trendy account'
            message = f'''
            Hi {self.username},

            Please verify your email address by clicking the link below:

            {settings.FRONTEND_URL}/verify-email/{self.email_verification_token}

            This link will expire in 24 hours.

            Best regards,
            The Trendy Team
            '''

            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [self.email],
                fail_silently=False,
            )
            self.email_verification_sent_at = timezone.now()
            self.save(update_fields=['email_verification_sent_at'])


class UserSettings(models.Model):
    """User preferences and settings"""
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name='settings')
    
    # Notification preferences
    email_notifications = models.BooleanField(default=True)
    push_notifications = models.BooleanField(default=True)
    comment_notifications = models.BooleanField(default=True)
    like_notifications = models.BooleanField(default=True)
    follow_notifications = models.BooleanField(default=True)
    
    # Privacy settings
    profile_visibility = models.CharField(
        max_length=20,
        choices=[
            ('public', 'Public'),
            ('private', 'Private'),
            ('friends', 'Friends Only'),
        ],
        default='public'
    )
    show_email = models.BooleanField(default=False)
    show_phone = models.BooleanField(default=False)
    
    # Content preferences
    content_language = models.CharField(max_length=10, default='en')
    posts_per_page = models.IntegerField(default=10)
    auto_play_videos = models.BooleanField(default=True)
    
    # Theme preferences
    theme = models.CharField(
        max_length=20,
        choices=[
            ('light', 'Light'),
            ('dark', 'Dark'),
            ('auto', 'Auto'),
        ],
        default='auto'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.user.username}'s Settings"


class Notification(models.Model):
    """User notifications"""
    NOTIFICATION_TYPES = [
        ('like', 'Like'),
        ('comment', 'Comment'),
        ('follow', 'Follow'),
        ('mention', 'Mention'),
        ('post', 'New Post'),
        ('system', 'System'),
        ('reward', 'Reward'),
        ('achievement', 'Achievement'),
        ('subscription', 'Subscription'),
        ('points', 'Points'),
    ]
    
    recipient = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='notifications')
    sender = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='sent_notifications', null=True, blank=True)
    
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=255)
    message = models.TextField()
    
    # Optional related objects
    post_id = models.IntegerField(null=True, blank=True)
    comment_id = models.IntegerField(null=True, blank=True)

    # Additional metadata as JSON
    metadata = models.JSONField(default=dict, blank=True)
    
    # Status
    is_read = models.BooleanField(default=False)
    is_sent = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', '-created_at']),
            models.Index(fields=['is_read']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.recipient.username}"
    
    def mark_as_read(self):
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])


class PasswordResetToken(models.Model):
    """Password reset tokens"""
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    token = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    used_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Password reset for {self.user.email}"
    
    @property
    def is_expired(self):
        """Check if token is expired (24 hours)"""
        return timezone.now() > self.created_at + timezone.timedelta(hours=24)
    
    @property
    def is_used(self):
        """Check if token has been used"""
        return self.used_at is not None
    
    def mark_as_used(self):
        """Mark token as used"""
        self.used_at = timezone.now()
        self.save(update_fields=['used_at'])


