from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth import get_user_model
from .models import UserSettings, Notification, PasswordResetToken

User = get_user_model()


class CustomUserAdmin(UserAdmin):
    """Custom user admin with role management"""
    list_display = ('email', 'username', 'first_name', 'last_name', 'get_user_role', 'is_email_verified', 'is_staff', 'date_joined')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'is_email_verified', 'groups', 'date_joined')
    search_fields = ('email', 'username', 'first_name', 'last_name')
    ordering = ('-date_joined',)
    actions = ['promote_to_content_creator', 'demote_to_regular_user']
    
    fieldsets = UserAdmin.fieldsets + (
        ('Additional Info', {
            'fields': ('bio', 'avatar', 'phone_number', 'date_of_birth', 'location', 'website')
        }),
        ('Social Links', {
            'fields': ('twitter_url', 'linkedin_url', 'github_url')
        }),
        ('Email Verification', {
            'fields': ('is_email_verified', 'email_verification_token', 'email_verification_sent_at')
        }),
        ('Preferences', {
            'fields': ('receive_email_notifications', 'receive_push_notifications', 'is_profile_public')
        }),
        ('Regional Settings', {
            'fields': ('preferred_country', 'detected_country', 'show_global_content', 'auto_detect_location')
        }),
    )

    readonly_fields = ('email_verification_token', 'email_verification_sent_at', 'updated_at')

    def get_user_role(self, obj):
        """Display user's role in admin list"""
        return obj.user_role.replace('_', ' ').title()
    get_user_role.short_description = 'Role'
    get_user_role.admin_order_field = 'is_staff'

    def promote_to_content_creator(self, request, queryset):
        """Admin action to promote users to content creator"""
        promoted_count = 0
        for user in queryset:
            if not (user.is_staff or user.is_superuser):
                if user.promote_to_content_creator():
                    promoted_count += 1

        self.message_user(
            request,
            f'Successfully promoted {promoted_count} user(s) to Content Creator.'
        )
    promote_to_content_creator.short_description = "Promote selected users to Content Creator"

    def demote_to_regular_user(self, request, queryset):
        """Admin action to demote users to regular user"""
        demoted_count = 0
        for user in queryset:
            if not (user.is_staff or user.is_superuser):
                if user.demote_to_regular_user():
                    demoted_count += 1

        self.message_user(
            request,
            f'Successfully demoted {demoted_count} user(s) to Regular User.'
        )
    demote_to_regular_user.short_description = "Demote selected users to Regular User"


# Register the custom user admin
admin.site.register(User, CustomUserAdmin)


@admin.register(UserSettings)
class UserSettingsAdmin(admin.ModelAdmin):
    """User settings admin"""
    list_display = ('user', 'email_notifications', 'push_notifications', 'profile_visibility', 'theme')
    list_filter = ('email_notifications', 'push_notifications', 'profile_visibility', 'theme')
    search_fields = ('user__email', 'user__username')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Notification admin"""
    list_display = ('title', 'recipient', 'notification_type', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'created_at')
    search_fields = ('title', 'message', 'recipient__email', 'recipient__username')
    readonly_fields = ('created_at', 'read_at')
    ordering = ('-created_at',)


@admin.register(PasswordResetToken)
class PasswordResetTokenAdmin(admin.ModelAdmin):
    """Password reset token admin"""
    list_display = ('user', 'token', 'created_at', 'used_at', 'is_expired')
    list_filter = ('created_at', 'used_at')
    search_fields = ('user__email', 'user__username')
    readonly_fields = ('token', 'created_at', 'used_at')
    ordering = ('-created_at',)
