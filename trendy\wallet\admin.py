from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import UserWallet, WalletTransaction, WalletDepositRequest, WalletWithdrawalRequest, WalletSettings


@admin.register(UserWallet)
class UserWalletAdmin(admin.ModelAdmin):
    list_display = ['user', 'formatted_balance', 'is_active', 'is_verified', 'created_at']
    list_filter = ['is_active', 'is_verified', 'created_at']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'balance', 'is_active', 'is_verified', 'verification_date')
        }),
        ('Limits', {
            'fields': ('daily_spend_limit', 'monthly_spend_limit')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def formatted_balance(self, obj):
        return obj.formatted_balance
    formatted_balance.short_description = 'Balance'


@admin.register(WalletTransaction)
class WalletTransactionAdmin(admin.ModelAdmin):
    list_display = ['wallet_user', 'transaction_type', 'purpose', 'formatted_amount', 'status', 'created_at']
    list_filter = ['transaction_type', 'purpose', 'status', 'created_at']
    search_fields = ['wallet__user__username', 'description', 'reference_id']
    readonly_fields = ['id', 'created_at', 'processed_at', 'completed_at']
    
    fieldsets = (
        ('Transaction Details', {
            'fields': ('id', 'wallet', 'transaction_type', 'purpose', 'amount', 'status')
        }),
        ('Balances', {
            'fields': ('balance_before', 'balance_after')
        }),
        ('Payment Information', {
            'fields': ('payment_method', 'external_transaction_id', 'reference_id')
        }),
        ('Description', {
            'fields': ('description',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'processed_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )
    
    def wallet_user(self, obj):
        return obj.wallet.user.username
    wallet_user.short_description = 'User'
    
    def formatted_amount(self, obj):
        color = 'green' if obj.transaction_type == 'credit' else 'red'
        sign = '+' if obj.transaction_type == 'credit' else '-'
        return format_html(
            '<span style="color: {};">{}{}</span>',
            color, sign, obj.formatted_amount
        )
    formatted_amount.short_description = 'Amount'


@admin.register(WalletDepositRequest)
class WalletDepositRequestAdmin(admin.ModelAdmin):
    list_display = ['wallet_user', 'formatted_amount', 'payment_method', 'status', 'created_at']
    list_filter = ['payment_method', 'status', 'created_at']
    search_fields = ['wallet__user__username', 'payment_order_id', 'external_transaction_id']
    readonly_fields = ['id', 'created_at', 'processed_at', 'completed_at']
    
    fieldsets = (
        ('Deposit Information', {
            'fields': ('id', 'wallet', 'amount', 'payment_method', 'status')
        }),
        ('Payment Details', {
            'fields': ('payment_order_id', 'external_transaction_id')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'processed_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )
    
    def wallet_user(self, obj):
        return obj.wallet.user.username
    wallet_user.short_description = 'User'
    
    def formatted_amount(self, obj):
        return f"${obj.amount:.2f}"
    formatted_amount.short_description = 'Amount'


@admin.register(WalletWithdrawalRequest)
class WalletWithdrawalRequestAdmin(admin.ModelAdmin):
    list_display = ['wallet_user', 'formatted_amount', 'paypal_email', 'status', 'created_at']
    list_filter = ['withdrawal_method', 'status', 'created_at']
    search_fields = ['wallet__user__username', 'paypal_email', 'external_transaction_id']
    readonly_fields = ['id', 'created_at', 'reviewed_at', 'processed_at', 'completed_at']
    
    fieldsets = (
        ('Withdrawal Information', {
            'fields': ('id', 'wallet', 'amount', 'withdrawal_method', 'paypal_email', 'status')
        }),
        ('Admin Review', {
            'fields': ('reviewed_by', 'admin_notes', 'rejection_reason')
        }),
        ('Payment Details', {
            'fields': ('external_transaction_id',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'reviewed_at', 'processed_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['approve_withdrawals', 'reject_withdrawals']
    
    def wallet_user(self, obj):
        return obj.wallet.user.username
    wallet_user.short_description = 'User'
    
    def formatted_amount(self, obj):
        return f"${obj.amount:.2f}"
    formatted_amount.short_description = 'Amount'
    
    def approve_withdrawals(self, request, queryset):
        updated = queryset.filter(status='pending').update(
            status='approved',
            reviewed_by=request.user,
            reviewed_at=timezone.now()
        )
        self.message_user(request, f'{updated} withdrawal requests approved.')
    approve_withdrawals.short_description = 'Approve selected withdrawal requests'
    
    def reject_withdrawals(self, request, queryset):
        updated = queryset.filter(status='pending').update(
            status='rejected',
            reviewed_by=request.user,
            reviewed_at=timezone.now(),
            rejection_reason='Rejected by admin'
        )
        self.message_user(request, f'{updated} withdrawal requests rejected.')
    reject_withdrawals.short_description = 'Reject selected withdrawal requests'


@admin.register(WalletSettings)
class WalletSettingsAdmin(admin.ModelAdmin):
    fieldsets = (
        ('Deposit Settings', {
            'fields': ('minimum_deposit', 'maximum_deposit', 'deposit_fee_percentage', 'deposits_enabled')
        }),
        ('Withdrawal Settings', {
            'fields': ('minimum_withdrawal', 'maximum_withdrawal', 'withdrawal_fee_percentage', 'withdrawal_fee_fixed', 'withdrawals_enabled')
        }),
        ('System Controls', {
            'fields': ('wallets_enabled', 'require_verification')
        }),
        ('Limits', {
            'fields': ('daily_withdrawal_limit', 'monthly_withdrawal_limit')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ['created_at', 'updated_at']
    
    def has_add_permission(self, request):
        # Only allow one settings instance
        return not WalletSettings.objects.exists()
    
    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of settings
        return False
