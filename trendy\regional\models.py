"""
Regional content filtering models for Trendy app
"""

from django.db import models
from django.utils import timezone
from django.core.validators import RegexValidator


class Region(models.Model):
    """Geographical regions for content organization"""
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(
        max_length=10, 
        unique=True,
        help_text="Short code for the region (e.g., 'AF' for Africa, 'EU' for Europe)"
    )
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.name


class Country(models.Model):
    """Countries for regional content targeting"""
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(
        max_length=2,
        unique=True,
        validators=[RegexValidator(r'^[A-Z]{2}$', 'Country code must be 2 uppercase letters')],
        help_text="ISO 3166-1 alpha-2 country code (e.g., 'UG' for Uganda)"
    )
    code_3 = models.CharField(
        max_length=3,
        unique=True,
        validators=[RegexValidator(r'^[A-Z]{3}$', 'Country code must be 3 uppercase letters')],
        help_text="ISO 3166-1 alpha-3 country code (e.g., 'UGA' for Uganda)"
    )
    region = models.ForeignKey(
        Region, 
        on_delete=models.CASCADE, 
        related_name='countries'
    )
    
    # Additional country metadata
    currency_code = models.CharField(max_length=3, blank=True)
    currency_name = models.CharField(max_length=50, blank=True)
    phone_code = models.CharField(max_length=10, blank=True)
    flag_emoji = models.CharField(max_length=10, blank=True)
    
    # Content settings
    is_active = models.BooleanField(default=True)
    allow_global_content = models.BooleanField(
        default=True,
        help_text="Whether users from this country can see global content"
    )
    priority = models.PositiveIntegerField(
        default=0,
        help_text="Display priority in country lists (higher = first)"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-priority', 'name']
        verbose_name_plural = 'Countries'
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['code_3']),
            models.Index(fields=['region', 'is_active']),
            models.Index(fields=['-priority', 'name']),
        ]

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def display_name(self):
        """Display name with flag emoji if available"""
        if self.flag_emoji:
            return f"{self.flag_emoji} {self.name}"
        return self.name


class CountryIPRange(models.Model):
    """IP ranges for country detection (optional - can use external service)"""
    country = models.ForeignKey(Country, on_delete=models.CASCADE, related_name='ip_ranges')
    ip_start = models.GenericIPAddressField()
    ip_end = models.GenericIPAddressField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['ip_start', 'ip_end']),
            models.Index(fields=['country', 'is_active']),
        ]
        unique_together = ['ip_start', 'ip_end']

    def __str__(self):
        return f"{self.country.name}: {self.ip_start} - {self.ip_end}"


class UserLocationHistory(models.Model):
    """Track user location detection history"""
    user = models.ForeignKey(
        'accounts.CustomUser', 
        on_delete=models.CASCADE, 
        related_name='location_history'
    )
    detected_country = models.ForeignKey(
        Country, 
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    ip_address = models.GenericIPAddressField()
    detection_method = models.CharField(
        max_length=20,
        choices=[
            ('ip_geolocation', 'IP Geolocation'),
            ('user_profile', 'User Profile'),
            ('manual_selection', 'Manual Selection'),
            ('browser_locale', 'Browser Locale'),
        ],
        default='ip_geolocation'
    )
    confidence_score = models.DecimalField(
        max_digits=3, 
        decimal_places=2, 
        default=0.0,
        help_text="Confidence in location detection (0.0 - 1.0)"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['detected_country']),
            models.Index(fields=['ip_address']),
        ]

    def __str__(self):
        country_name = self.detected_country.name if self.detected_country else 'Unknown'
        return f"{self.user.username} - {country_name} ({self.detection_method})"
