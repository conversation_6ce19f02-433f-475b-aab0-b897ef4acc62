# Generated by Django 5.1.7 on 2025-06-24 07:27

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BlockchainNetwork',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('polygon', 'Polygon (MATIC)'), ('bsc', 'Binance Smart Chain'), ('ethereum', 'Ethereum'), ('solana', 'Solana')], max_length=50, unique=True)),
                ('chain_id', models.IntegerField()),
                ('rpc_url', models.URLField()),
                ('explorer_url', models.URLField()),
                ('native_token', models.Char<PERSON>ield(max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('gas_price_gwei', models.DecimalField(decimal_places=2, default=20, max_digits=10)),
            ],
        ),
        migrations.CreateModel(
            name='BlockchainTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('token_reward', 'Token Reward'), ('nft_mint', 'NFT Mint'), ('token_transfer', 'Token Transfer'), ('escrow_create', 'Escrow Create'), ('escrow_release', 'Escrow Release'), ('staking_deposit', 'Staking Deposit'), ('staking_withdraw', 'Staking Withdraw'), ('marketplace_buy', 'Marketplace Purchase'), ('marketplace_sell', 'Marketplace Sale')], max_length=20)),
                ('blockchain_hash', models.CharField(max_length=66, unique=True)),
                ('from_address', models.CharField(max_length=42)),
                ('to_address', models.CharField(max_length=42)),
                ('amount', models.DecimalField(blank=True, decimal_places=18, max_digits=30, null=True)),
                ('token_id', models.BigIntegerField(blank=True, null=True)),
                ('gas_limit', models.BigIntegerField()),
                ('gas_used', models.BigIntegerField(blank=True, null=True)),
                ('gas_price', models.BigIntegerField()),
                ('transaction_fee', models.DecimalField(decimal_places=18, max_digits=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('block_number', models.BigIntegerField(blank=True, null=True)),
                ('block_hash', models.CharField(blank=True, max_length=66, null=True)),
                ('confirmations', models.IntegerField(default=0)),
                ('description', models.TextField(blank=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('confirmed_at', models.DateTimeField(blank=True, null=True)),
                ('network', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blockchain.blockchainnetwork')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blockchain_transactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SmartContract',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contract_type', models.CharField(choices=[('token', 'ERC-20 Token'), ('nft', 'ERC-721 NFT'), ('escrow', 'Escrow Contract'), ('staking', 'Staking Contract'), ('marketplace', 'Marketplace Contract')], max_length=20)),
                ('name', models.CharField(max_length=100)),
                ('address', models.CharField(max_length=42)),
                ('abi', models.JSONField()),
                ('deployed_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('network', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blockchain.blockchainnetwork')),
            ],
            options={
                'unique_together': {('network', 'contract_type', 'name')},
            },
        ),
        migrations.CreateModel(
            name='NFTAsset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token_id', models.BigIntegerField()),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('image_url', models.URLField()),
                ('animation_url', models.URLField(blank=True)),
                ('external_url', models.URLField(blank=True)),
                ('rarity', models.IntegerField(choices=[(1, 'Common'), (2, 'Uncommon'), (3, 'Rare'), (4, 'Epic'), (5, 'Legendary')], default=1)),
                ('attributes', models.JSONField(blank=True, default=dict)),
                ('minted_at', models.DateTimeField(auto_now_add=True)),
                ('last_transferred', models.DateTimeField(blank=True, null=True)),
                ('mint_transaction', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='minted_nfts', to='blockchain.blockchaintransaction')),
                ('network', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blockchain.blockchainnetwork')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='nft_assets', to=settings.AUTH_USER_MODEL)),
                ('contract', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blockchain.smartcontract')),
            ],
            options={
                'ordering': ['-minted_at'],
            },
        ),
        migrations.CreateModel(
            name='MarketplaceListing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('listing_type', models.CharField(choices=[('fixed_price', 'Fixed Price'), ('auction', 'Auction'), ('offer', 'Best Offer')], max_length=20)),
                ('price', models.DecimalField(decimal_places=18, max_digits=20)),
                ('starting_price', models.DecimalField(blank=True, decimal_places=18, max_digits=20, null=True)),
                ('reserve_price', models.DecimalField(blank=True, decimal_places=18, max_digits=20, null=True)),
                ('auction_end', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('sold', 'Sold'), ('cancelled', 'Cancelled'), ('expired', 'Expired')], default='active', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('sold_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('buyer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='marketplace_purchases', to=settings.AUTH_USER_MODEL)),
                ('sale_transaction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='blockchain.blockchaintransaction')),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='marketplace_listings', to=settings.AUTH_USER_MODEL)),
                ('nft_asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='listings', to='blockchain.nftasset')),
                ('currency_contract', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blockchain.smartcontract')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='blockchaintransaction',
            name='contract',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='blockchain.smartcontract'),
        ),
        migrations.CreateModel(
            name='StakingPool',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('apy_percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('minimum_stake', models.DecimalField(decimal_places=18, max_digits=20)),
                ('maximum_stake', models.DecimalField(blank=True, decimal_places=18, max_digits=20, null=True)),
                ('total_staked', models.DecimalField(decimal_places=18, default=0, max_digits=30)),
                ('total_rewards_distributed', models.DecimalField(decimal_places=18, default=0, max_digits=30)),
                ('active_stakers', models.IntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('contract', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blockchain.smartcontract')),
                ('network', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blockchain.blockchainnetwork')),
                ('token_contract', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staking_pools', to='blockchain.smartcontract')),
            ],
        ),
        migrations.CreateModel(
            name='TokenBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('balance', models.DecimalField(decimal_places=18, default=0, max_digits=30)),
                ('staked_balance', models.DecimalField(decimal_places=18, default=0, max_digits=30)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('contract', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blockchain.smartcontract')),
                ('network', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blockchain.blockchainnetwork')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='token_balances', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserStake',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_staked', models.DecimalField(decimal_places=18, max_digits=30)),
                ('rewards_earned', models.DecimalField(decimal_places=18, default=0, max_digits=30)),
                ('last_reward_calculation', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('staked_at', models.DateTimeField(auto_now_add=True)),
                ('unstaked_at', models.DateTimeField(blank=True, null=True)),
                ('pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_stakes', to='blockchain.stakingpool')),
                ('stake_transaction', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stake_deposits', to='blockchain.blockchaintransaction')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stakes', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserWalletAddress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(max_length=42)),
                ('private_key_encrypted', models.TextField()),
                ('is_primary', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('network', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blockchain.blockchainnetwork')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wallet_addresses', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddIndex(
            model_name='nftasset',
            index=models.Index(fields=['user', 'rarity'], name='blockchain__user_id_e0e695_idx'),
        ),
        migrations.AddIndex(
            model_name='nftasset',
            index=models.Index(fields=['contract', 'token_id'], name='blockchain__contrac_2a45bf_idx'),
        ),
        migrations.AddIndex(
            model_name='nftasset',
            index=models.Index(fields=['rarity', 'minted_at'], name='blockchain__rarity_28fc50_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='nftasset',
            unique_together={('network', 'contract', 'token_id')},
        ),
        migrations.AddIndex(
            model_name='marketplacelisting',
            index=models.Index(fields=['status', 'listing_type'], name='blockchain__status_1d829c_idx'),
        ),
        migrations.AddIndex(
            model_name='marketplacelisting',
            index=models.Index(fields=['seller', 'status'], name='blockchain__seller__cdf3cf_idx'),
        ),
        migrations.AddIndex(
            model_name='marketplacelisting',
            index=models.Index(fields=['price', 'currency_contract'], name='blockchain__price_aaad95_idx'),
        ),
        migrations.AddIndex(
            model_name='marketplacelisting',
            index=models.Index(fields=['created_at'], name='blockchain__created_2b2ea8_idx'),
        ),
        migrations.AddIndex(
            model_name='blockchaintransaction',
            index=models.Index(fields=['user', 'transaction_type'], name='blockchain__user_id_2a074c_idx'),
        ),
        migrations.AddIndex(
            model_name='blockchaintransaction',
            index=models.Index(fields=['blockchain_hash'], name='blockchain__blockch_f5efad_idx'),
        ),
        migrations.AddIndex(
            model_name='blockchaintransaction',
            index=models.Index(fields=['status', 'created_at'], name='blockchain__status_f353af_idx'),
        ),
        migrations.AddIndex(
            model_name='blockchaintransaction',
            index=models.Index(fields=['network', 'block_number'], name='blockchain__network_fd0d13_idx'),
        ),
        migrations.AddIndex(
            model_name='tokenbalance',
            index=models.Index(fields=['user', 'network'], name='blockchain__user_id_7c4635_idx'),
        ),
        migrations.AddIndex(
            model_name='tokenbalance',
            index=models.Index(fields=['contract', 'balance'], name='blockchain__contrac_11a708_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='tokenbalance',
            unique_together={('user', 'network', 'contract')},
        ),
        migrations.AddIndex(
            model_name='userstake',
            index=models.Index(fields=['user', 'pool'], name='blockchain__user_id_156e94_idx'),
        ),
        migrations.AddIndex(
            model_name='userstake',
            index=models.Index(fields=['pool', 'is_active'], name='blockchain__pool_id_cd17f5_idx'),
        ),
        migrations.AddIndex(
            model_name='userstake',
            index=models.Index(fields=['staked_at'], name='blockchain__staked__92912c_idx'),
        ),
        migrations.AddIndex(
            model_name='userwalletaddress',
            index=models.Index(fields=['user', 'network'], name='blockchain__user_id_476128_idx'),
        ),
        migrations.AddIndex(
            model_name='userwalletaddress',
            index=models.Index(fields=['address'], name='blockchain__address_9a0ed2_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userwalletaddress',
            unique_together={('user', 'network')},
        ),
    ]
