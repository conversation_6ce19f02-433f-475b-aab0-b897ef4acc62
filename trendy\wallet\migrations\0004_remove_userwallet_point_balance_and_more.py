# Generated by Django 4.2.23 on 2025-06-25 01:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("wallet", "0003_userwallet_point_balance_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="userwallet",
            name="point_balance",
        ),
        migrations.RemoveField(
            model_name="wallettransaction",
            name="balance_after_points",
        ),
        migrations.RemoveField(
            model_name="wallettransaction",
            name="points",
        ),
        migrations.AlterField(
            model_name="wallettransaction",
            name="purpose",
            field=models.CharField(
                choices=[
                    ("deposit", "Wallet Deposit"),
                    ("withdrawal", "Wallet Withdrawal"),
                    ("purchase_subscription", "Premium Subscription"),
                    ("purchase_points", "Point Boost Purchase"),
                    ("purchase_virtual_item", "Virtual Item Purchase"),
                    ("refund", "Refund"),
                    ("admin_adjustment", "Admin Adjustment"),
                    ("reward_payout", "Reward Payout"),
                ],
                max_length=30,
            ),
        ),
    ]
