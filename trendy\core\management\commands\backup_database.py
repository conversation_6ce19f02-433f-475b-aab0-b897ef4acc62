"""
Django management command for creating comprehensive database backups
Usage: python manage.py backup_database [--output-dir path] [--compress] [--include-media]
"""
import os
import json
import gzip
import shutil
from datetime import datetime
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command
from django.conf import settings
from django.db import connection
from django.apps import apps
import subprocess


class Command(BaseCommand):
    help = 'Create a comprehensive database backup with metadata'

    def add_arguments(self, parser):
        parser.add_argument(
            '--output-dir',
            type=str,
            default='backups',
            help='Directory to store backup files (default: backups)'
        )
        parser.add_argument(
            '--compress',
            action='store_true',
            help='Compress backup files with gzip'
        )
        parser.add_argument(
            '--include-media',
            action='store_true',
            help='Include media files in backup'
        )
        parser.add_argument(
            '--name',
            type=str,
            help='Custom name for backup (default: auto-generated)'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🗄️  Starting Database Backup Process'))
        self.stdout.write('=' * 60)

        # Setup backup directory
        output_dir = Path(options['output_dir'])
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = options.get('name') or f'trendy_backup_{timestamp}'
        backup_dir = output_dir / backup_name
        
        try:
            backup_dir.mkdir(parents=True, exist_ok=True)
            self.stdout.write(f'📁 Backup directory: {backup_dir}')

            # Create backup metadata
            metadata = self._create_metadata()
            
            # 1. Database dump
            self.stdout.write('\n1️⃣  Creating database dump...')
            db_file = self._create_database_dump(backup_dir, options['compress'])
            
            # 2. Django fixtures
            self.stdout.write('\n2️⃣  Creating Django fixtures...')
            fixtures_dir = self._create_fixtures(backup_dir, options['compress'])
            
            # 3. Media files (if requested)
            media_dir = None
            if options['include_media']:
                self.stdout.write('\n3️⃣  Backing up media files...')
                media_dir = self._backup_media_files(backup_dir, options['compress'])
            
            # 4. Environment and settings backup
            self.stdout.write('\n4️⃣  Backing up configuration...')
            config_file = self._backup_configuration(backup_dir)
            
            # 5. Save metadata
            metadata.update({
                'database_file': str(db_file.name) if db_file else None,
                'fixtures_directory': str(fixtures_dir.name) if fixtures_dir else None,
                'media_directory': str(media_dir.name) if media_dir else None,
                'config_file': str(config_file.name) if config_file else None,
                'compressed': options['compress'],
                'include_media': options['include_media'],
            })
            
            metadata_file = backup_dir / 'backup_metadata.json'
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)
            
            # 6. Create backup summary
            self._create_backup_summary(backup_dir, metadata)
            
            # 7. Create restore script
            self._create_restore_script(backup_dir, metadata)
            
            self.stdout.write(f'\n✅ Backup completed successfully!')
            self.stdout.write(f'📦 Backup location: {backup_dir}')
            self.stdout.write(f'📊 Backup size: {self._get_directory_size(backup_dir)}')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Backup failed: {str(e)}')
            )
            # Cleanup on failure
            if backup_dir.exists():
                shutil.rmtree(backup_dir)
            raise CommandError(f'Backup process failed: {str(e)}')

    def _create_metadata(self):
        """Create backup metadata"""
        return {
            'backup_created': datetime.now().isoformat(),
            'django_version': getattr(settings, 'DJANGO_VERSION', 'unknown'),
            'database_engine': settings.DATABASES['default']['ENGINE'],
            'database_name': settings.DATABASES['default']['NAME'],
            'installed_apps': list(settings.INSTALLED_APPS),
            'python_version': subprocess.check_output(['python', '--version']).decode().strip(),
            'backup_type': 'full_system_backup',
        }

    def _create_database_dump(self, backup_dir, compress):
        """Create raw database dump"""
        db_config = settings.DATABASES['default']
        engine = db_config['ENGINE']
        
        if 'sqlite' in engine:
            return self._backup_sqlite(backup_dir, db_config, compress)
        elif 'postgresql' in engine:
            return self._backup_postgresql(backup_dir, db_config, compress)
        elif 'mysql' in engine:
            return self._backup_mysql(backup_dir, db_config, compress)
        else:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Unsupported database engine: {engine}')
            )
            return None

    def _backup_sqlite(self, backup_dir, db_config, compress):
        """Backup SQLite database"""
        db_path = Path(db_config['NAME'])
        if not db_path.exists():
            raise CommandError(f'SQLite database not found: {db_path}')
        
        backup_file = backup_dir / 'database.sqlite3'
        if compress:
            backup_file = backup_dir / 'database.sqlite3.gz'
            with open(db_path, 'rb') as f_in:
                with gzip.open(backup_file, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
        else:
            shutil.copy2(db_path, backup_file)
        
        self.stdout.write(f'  ✅ SQLite backup: {backup_file.name}')
        return backup_file

    def _backup_postgresql(self, backup_dir, db_config, compress):
        """Backup PostgreSQL database"""
        backup_file = backup_dir / 'database.sql'
        if compress:
            backup_file = backup_dir / 'database.sql.gz'
        
        # Build pg_dump command
        cmd = [
            'pg_dump',
            '--host', db_config.get('HOST', 'localhost'),
            '--port', str(db_config.get('PORT', 5432)),
            '--username', db_config['USER'],
            '--dbname', db_config['NAME'],
            '--verbose',
            '--clean',
            '--no-owner',
            '--no-privileges',
        ]
        
        # Set password environment variable
        env = os.environ.copy()
        env['PGPASSWORD'] = db_config['PASSWORD']
        
        try:
            if compress:
                with gzip.open(backup_file, 'wt') as f:
                    subprocess.run(cmd, stdout=f, env=env, check=True)
            else:
                with open(backup_file, 'w') as f:
                    subprocess.run(cmd, stdout=f, env=env, check=True)
            
            self.stdout.write(f'  ✅ PostgreSQL backup: {backup_file.name}')
            return backup_file
        except subprocess.CalledProcessError as e:
            raise CommandError(f'PostgreSQL backup failed: {e}')

    def _backup_mysql(self, backup_dir, db_config, compress):
        """Backup MySQL database"""
        backup_file = backup_dir / 'database.sql'
        if compress:
            backup_file = backup_dir / 'database.sql.gz'
        
        # Build mysqldump command
        cmd = [
            'mysqldump',
            '--host', db_config.get('HOST', 'localhost'),
            '--port', str(db_config.get('PORT', 3306)),
            '--user', db_config['USER'],
            f'--password={db_config["PASSWORD"]}',
            '--single-transaction',
            '--routines',
            '--triggers',
            db_config['NAME'],
        ]
        
        try:
            if compress:
                with gzip.open(backup_file, 'wt') as f:
                    subprocess.run(cmd, stdout=f, check=True)
            else:
                with open(backup_file, 'w') as f:
                    subprocess.run(cmd, stdout=f, check=True)
            
            self.stdout.write(f'  ✅ MySQL backup: {backup_file.name}')
            return backup_file
        except subprocess.CalledProcessError as e:
            raise CommandError(f'MySQL backup failed: {e}')

    def _create_fixtures(self, backup_dir, compress):
        """Create Django fixtures for all apps"""
        fixtures_dir = backup_dir / 'fixtures'
        fixtures_dir.mkdir(exist_ok=True)
        
        # Get all models
        all_models = []
        for app_config in apps.get_app_configs():
            for model in app_config.get_models():
                all_models.append(f'{app_config.label}.{model._meta.model_name}')
        
        if all_models:
            fixture_file = fixtures_dir / 'all_data.json'
            if compress:
                fixture_file = fixtures_dir / 'all_data.json.gz'
            
            try:
                if compress:
                    with gzip.open(fixture_file, 'wt') as f:
                        call_command('dumpdata', *all_models, stdout=f, indent=2)
                else:
                    with open(fixture_file, 'w') as f:
                        call_command('dumpdata', *all_models, stdout=f, indent=2)
                
                self.stdout.write(f'  ✅ Django fixtures: {fixture_file.name}')
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'  ⚠️  Fixtures backup failed: {e}')
                )
        
        return fixtures_dir

    def _backup_media_files(self, backup_dir, compress):
        """Backup media files"""
        media_root = Path(settings.MEDIA_ROOT)
        if not media_root.exists():
            self.stdout.write('  ⚠️  Media directory not found')
            return None
        
        media_backup_dir = backup_dir / 'media'
        
        if compress:
            # Create compressed archive
            archive_path = backup_dir / 'media.tar.gz'
            shutil.make_archive(
                str(archive_path).replace('.tar.gz', ''),
                'gztar',
                str(media_root.parent),
                str(media_root.name)
            )
            self.stdout.write(f'  ✅ Media archive: {archive_path.name}')
            return archive_path
        else:
            # Copy directory
            shutil.copytree(media_root, media_backup_dir)
            self.stdout.write(f'  ✅ Media directory: {media_backup_dir.name}')
            return media_backup_dir

    def _backup_configuration(self, backup_dir):
        """Backup configuration files"""
        config_dir = backup_dir / 'config'
        config_dir.mkdir(exist_ok=True)
        
        # Backup .env file (without sensitive data)
        env_file = Path('.env')
        if env_file.exists():
            backup_env = config_dir / 'env_template.txt'
            with open(env_file, 'r') as f_in, open(backup_env, 'w') as f_out:
                for line in f_in:
                    if '=' in line and not line.strip().startswith('#'):
                        key = line.split('=')[0]
                        f_out.write(f'{key}=<REDACTED>\n')
                    else:
                        f_out.write(line)
        
        # Backup requirements
        req_file = Path('requirements.txt')
        if req_file.exists():
            shutil.copy2(req_file, config_dir / 'requirements.txt')
        
        self.stdout.write(f'  ✅ Configuration backup: {config_dir.name}')
        return config_dir

    def _create_backup_summary(self, backup_dir, metadata):
        """Create human-readable backup summary"""
        summary_file = backup_dir / 'BACKUP_SUMMARY.txt'
        with open(summary_file, 'w') as f:
            f.write('TRENDY DATABASE BACKUP SUMMARY\n')
            f.write('=' * 50 + '\n\n')
            f.write(f'Backup Created: {metadata["backup_created"]}\n')
            f.write(f'Database Engine: {metadata["database_engine"]}\n')
            f.write(f'Database Name: {metadata["database_name"]}\n')
            f.write(f'Compressed: {metadata["compressed"]}\n')
            f.write(f'Media Included: {metadata["include_media"]}\n\n')
            
            f.write('FILES INCLUDED:\n')
            f.write('-' * 20 + '\n')
            if metadata.get('database_file'):
                f.write(f'• Database: {metadata["database_file"]}\n')
            if metadata.get('fixtures_directory'):
                f.write(f'• Fixtures: {metadata["fixtures_directory"]}\n')
            if metadata.get('media_directory'):
                f.write(f'• Media: {metadata["media_directory"]}\n')
            if metadata.get('config_file'):
                f.write(f'• Config: {metadata["config_file"]}\n')
            
            f.write(f'\nTo restore this backup, run:\n')
            f.write(f'python manage.py restore_database {backup_dir.name}\n')

    def _create_restore_script(self, backup_dir, metadata):
        """Create restore script"""
        script_file = backup_dir / 'restore.py'
        with open(script_file, 'w') as f:
            f.write('#!/usr/bin/env python\n')
            f.write('"""\nQuick restore script for this backup\n"""\n')
            f.write('import subprocess\nimport sys\n\n')
            f.write('def main():\n')
            f.write('    print("🔄 Restoring Trendy database backup...")\n')
            f.write(f'    cmd = ["python", "manage.py", "restore_database", "{backup_dir.name}"]\n')
            f.write('    result = subprocess.run(cmd)\n')
            f.write('    sys.exit(result.returncode)\n\n')
            f.write('if __name__ == "__main__":\n')
            f.write('    main()\n')
        
        # Make executable on Unix systems
        try:
            os.chmod(script_file, 0o755)
        except:
            pass

    def _get_directory_size(self, path):
        """Get human-readable directory size"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                total_size += os.path.getsize(filepath)
        
        # Convert to human readable
        for unit in ['B', 'KB', 'MB', 'GB']:
            if total_size < 1024.0:
                return f"{total_size:.1f} {unit}"
            total_size /= 1024.0
        return f"{total_size:.1f} TB"
