from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from blog.models import Post
from interactive.models import InteractiveBlock, Poll, PollOption

class Command(BaseCommand):
    help = 'Create sample interactive content for testing'

    def handle(self, *args, **options):
        # Get the first post
        try:
            post = Post.objects.first()
            if not post:
                self.stdout.write(self.style.ERROR('No posts found. Please create a post first.'))
                return
            
            # Create a sample poll
            block = InteractiveBlock.objects.create(
                post=post,
                block_type='poll',
                title='What\'s your favorite programming language?',
                description='Help us understand the community preferences',
                position=0
            )
            
            poll = Poll.objects.create(
                interactive_block=block,
                question='What\'s your favorite programming language for web development?',
                allow_multiple_choices=False,
                show_results_immediately=True,
                expires_at=timezone.now() + timedelta(days=30),
                is_anonymous=False
            )
            
            # Create poll options
            options = [
                'Python (Django/Flask)',
                'JavaScript (Node.js/React)',
                'Java (Spring)',
                'PHP (<PERSON><PERSON>/Symfony)',
                'Ruby (Rails)',
                'Go',
                'Other'
            ]
            
            for i, option_text in enumerate(options):
                PollOption.objects.create(
                    poll=poll,
                    text=option_text,
                    position=i
                )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created sample poll for post "{post.title}"'
                )
            )
            
            # Create another poll about blog features
            block2 = InteractiveBlock.objects.create(
                post=post,
                block_type='poll',
                title='Which blog features do you find most useful?',
                description='Multiple selections allowed',
                position=1
            )
            
            poll2 = Poll.objects.create(
                interactive_block=block2,
                question='Which features make a blog more engaging for you?',
                allow_multiple_choices=True,
                show_results_immediately=True,
                is_anonymous=False
            )
            
            features = [
                'Interactive polls and quizzes',
                'Code examples and playgrounds',
                'Video content',
                'Comment discussions',
                'Reading progress tracking',
                'Social sharing',
                'Dark mode'
            ]
            
            for i, feature_text in enumerate(features):
                PollOption.objects.create(
                    poll=poll2,
                    text=feature_text,
                    position=i
                )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created second sample poll for post "{post.title}"'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating sample interactive content: {e}')
            )
