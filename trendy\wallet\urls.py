from django.urls import path
from . import views
from .views.automated_views import (
    automated_deposit, verify_transaction, automated_withdrawal,
    automated_purchase, transaction_status, pending_verifications
)

urlpatterns = [
    # User wallet endpoints
    path('overview/', views.wallet_overview, name='wallet-overview'),
    path('settings/', views.wallet_settings, name='wallet-settings'),
    path('transactions/', views.transaction_history, name='wallet-transactions'),
    
    # Deposit endpoints
    path('deposit/create/', views.create_deposit, name='create-deposit'),
    path('deposit/confirm/', views.confirm_deposit, name='confirm-deposit'),
    
    # Withdrawal endpoints
    path('withdrawal/create/', views.create_withdrawal, name='create-withdrawal'),
    
    # Spending endpoint
    path('spend/', views.spend_from_wallet, name='spend-from-wallet'),
    
    # Admin endpoints
    path('admin/pending-withdrawals/', views.admin_pending_withdrawals, name='admin-pending-withdrawals'),
    path('admin/approve-withdrawal/<uuid:withdrawal_id>/', views.admin_approve_withdrawal, name='admin-approve-withdrawal'),

    # Automated transaction endpoints
    path('automated/deposit/', automated_deposit, name='automated-deposit'),
    path('automated/withdrawal/', automated_withdrawal, name='automated-withdrawal'),
    path('automated/purchase/', automated_purchase, name='automated-purchase'),
    path('automated/verify/', verify_transaction, name='verify-transaction'),
    path('automated/status/<uuid:transaction_id>/', transaction_status, name='transaction-status'),
    path('automated/pending/', pending_verifications, name='pending-verifications'),
]
