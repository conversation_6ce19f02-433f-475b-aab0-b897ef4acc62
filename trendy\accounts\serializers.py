from rest_framework import serializers
from django.contrib.auth import authenticate, get_user_model
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import UserSettings, Notification, PasswordResetToken
import uuid

User = get_user_model()


class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration"""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    referral_code = serializers.CharField(write_only=True, required=False, allow_blank=True)

    class Meta:
        model = User
        fields = ('username', 'email', 'password', 'password_confirm', 'first_name', 'last_name', 'referral_code')
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
    
    def create(self, validated_data):
        referral_code = validated_data.pop('referral_code', None)
        validated_data.pop('password_confirm')
        user = User.objects.create_user(**validated_data)

        # Handle referral code if provided
        if referral_code:
            self._process_referral_code(user, referral_code)
        # Note: Welcome bonus is now handled by gamification signals automatically

        # Only send verification email if user is not admin
        if not (user.is_staff or user.is_superuser):
            user.send_verification_email()

        return user

    def _process_referral_code(self, new_user, referral_code):
        """Process referral code and create referral relationship"""
        try:
            from monetization.models import ReferralProgram
            from gamification.models import UserLevel, PointTransaction
            from monetization.models import MonetizationSettings

            # Validate referral code using new system
            try:
                from monetization.models import UserReferralCode
                referral_code_obj = UserReferralCode.objects.get(
                    code=referral_code.upper(),
                    is_active=True
                )
                referrer = referral_code_obj.user

                # Create referral relationship
                referral = ReferralProgram.objects.create(
                    referrer=referrer,
                    referee=new_user,
                    referral_code_used=referral_code_obj.code
                )

                # Increment usage count
                referral_code_obj.increment_usage()

                # Award join bonus points to both users
                settings = MonetizationSettings.get_settings()

                # Award points to referrer
                PointTransaction.objects.create(
                    user=referrer,
                    points=settings.referral_join_points,
                    transaction_type='referral',
                    description=f'Referral bonus: {new_user.username} joined'
                )

                # Award welcome points to new user
                PointTransaction.objects.create(
                    user=new_user,
                    points=50,  # Welcome bonus for using referral code
                    transaction_type='welcome',
                    description='Welcome bonus for using referral code'
                )

                # Update user levels
                referrer_level, _ = UserLevel.objects.get_or_create(user=referrer)
                referrer_level.total_points += settings.referral_join_points
                referrer_level.save()

                new_user_level, _ = UserLevel.objects.get_or_create(user=new_user)
                new_user_level.total_points += 50
                new_user_level.save()

                # Mark join reward as given
                referral.join_reward_given = True
                referral.save()

            except UserReferralCode.DoesNotExist:
                pass  # Invalid referral code
            except Exception:
                pass  # Don't fail registration if referral processing fails
        except Exception:
            pass  # Don't fail registration if referral processing fails

    def _award_welcome_bonus(self, new_user):
        """Award welcome bonus to new users"""
        try:
            from gamification.models import PointTransaction, UserLevel

            # Award standard welcome bonus
            PointTransaction.objects.create(
                user=new_user,
                points=100,  # Standard welcome bonus
                transaction_type='welcome',
                description='Welcome bonus for new user registration'
            )

            # Update user level
            new_user_level, _ = UserLevel.objects.get_or_create(user=new_user)
            new_user_level.total_points += 100
            new_user_level.save()

        except Exception:
            pass  # Don't fail registration if welcome bonus fails


class LoginSerializer(serializers.Serializer):
    """Serializer for user login"""
    email_or_username = serializers.CharField()
    password = serializers.CharField(style={'input_type': 'password'})
    
    def validate(self, attrs):
        email_or_username = attrs.get('email_or_username')
        password = attrs.get('password')
        
        if email_or_username and password:
            # Try to authenticate with email first
            if '@' in email_or_username:
                user = authenticate(request=self.context.get('request'),
                                  email=email_or_username, password=password)
            else:
                user = authenticate(request=self.context.get('request'),
                                  username=email_or_username, password=password)
            
            if not user:
                raise serializers.ValidationError('Invalid credentials')
            
            if not user.is_active:
                raise serializers.ValidationError('Account is disabled')

            # Check email verification for non-admin users only
            if not (user.is_staff or user.is_superuser) and not user.is_email_verified:
                raise serializers.ValidationError('Please verify your email address before logging in.')

            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError('Must include email/username and password')


class UserSerializer(serializers.ModelSerializer):
    """Serializer for user data"""
    full_name = serializers.ReadOnlyField()
    avatar_url = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = (
            'id', 'username', 'email', 'first_name', 'last_name', 'full_name',
            'bio', 'avatar_url', 'phone_number', 'date_of_birth', 'location',
            'website', 'twitter_url', 'linkedin_url', 'github_url',
            'is_email_verified', 'is_staff', 'is_superuser',
            'receive_email_notifications', 'receive_push_notifications',
            'is_profile_public', 'date_joined', 'updated_at'
        )
        read_only_fields = ('id', 'date_joined', 'is_email_verified', 'is_staff', 'is_superuser')


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user profile"""
    
    class Meta:
        model = User
        fields = (
            'first_name', 'last_name', 'bio', 'phone_number', 'date_of_birth',
            'location', 'website', 'twitter_url', 'linkedin_url', 'github_url',
            'receive_email_notifications', 'receive_push_notifications',
            'is_profile_public'
        )


class ChangePasswordSerializer(serializers.Serializer):
    """Serializer for changing password"""
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(required=True)
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match")
        return attrs
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect")
        return value


class PasswordResetRequestSerializer(serializers.Serializer):
    """Serializer for password reset request"""
    email = serializers.EmailField()
    
    def validate_email(self, value):
        try:
            user = User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("No user found with this email address")
        return value


class PasswordResetConfirmSerializer(serializers.Serializer):
    """Serializer for password reset confirmation"""
    token = serializers.UUIDField()
    new_password = serializers.CharField(validators=[validate_password])
    new_password_confirm = serializers.CharField()
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        
        try:
            reset_token = PasswordResetToken.objects.get(token=attrs['token'])
            if reset_token.is_expired or reset_token.is_used:
                raise serializers.ValidationError("Token is invalid or expired")
            attrs['reset_token'] = reset_token
        except PasswordResetToken.DoesNotExist:
            raise serializers.ValidationError("Invalid token")
        
        return attrs


class UserSettingsSerializer(serializers.ModelSerializer):
    """Serializer for user settings"""
    
    class Meta:
        model = UserSettings
        fields = (
            'email_notifications', 'push_notifications', 'comment_notifications',
            'like_notifications', 'follow_notifications', 'profile_visibility',
            'show_email', 'show_phone', 'content_language', 'posts_per_page',
            'auto_play_videos', 'theme'
        )


class NotificationSerializer(serializers.ModelSerializer):
    """Serializer for notifications"""
    sender_name = serializers.CharField(source='sender.full_name', read_only=True)
    sender_avatar = serializers.CharField(source='sender.avatar_url', read_only=True)
    
    class Meta:
        model = Notification
        fields = (
            'id', 'notification_type', 'title', 'message', 'post_id',
            'comment_id', 'is_read', 'created_at', 'read_at',
            'sender_name', 'sender_avatar'
        )
        read_only_fields = ('id', 'created_at', 'read_at')


class EmailVerificationSerializer(serializers.Serializer):
    """Serializer for email verification"""
    token = serializers.UUIDField()
    
    def validate_token(self, value):
        try:
            user = User.objects.get(email_verification_token=value)
            if user.is_email_verified:
                raise serializers.ValidationError("Email is already verified")
            
            # Check if token is expired (24 hours)
            if user.email_verification_sent_at:
                expiry_time = user.email_verification_sent_at + timezone.timedelta(hours=24)
                if timezone.now() > expiry_time:
                    raise serializers.ValidationError("Verification token has expired")
            
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("Invalid verification token")
