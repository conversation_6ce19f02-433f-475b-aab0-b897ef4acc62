from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from decimal import Decimal
from ..models import UserWallet, WalletTransaction, WalletDepositRequest, WalletWithdrawalRequest, WalletSettings
from ..services import WalletService


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def wallet_overview(request):
    """Get user's wallet overview"""
    try:
        user = request.user
        wallet_stats = WalletService.get_wallet_stats(user)
        
        if not wallet_stats:
            return Response({
                'success': False,
                'message': 'Error fetching wallet data'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # Get recent transactions
        wallet = WalletService.get_or_create_wallet(user)
        recent_transactions = wallet.transactions.all()[:10]
        
        transactions_data = []
        for txn in recent_transactions:
            transactions_data.append({
                'id': str(txn.id),
                'type': txn.transaction_type,
                'purpose': txn.get_purpose_display(),
                'amount': str(txn.amount),
                'formattedAmount': txn.formatted_amount,
                'balanceAfter': str(txn.balance_after),
                'status': txn.get_status_display(),
                'description': txn.description,
                'createdAt': txn.created_at.isoformat(),
            })
        
        # Get pending requests
        pending_deposits = WalletDepositRequest.objects.filter(
            wallet=wallet,
            status__in=['pending', 'processing']
        ).count()
        
        pending_withdrawals = WalletWithdrawalRequest.objects.filter(
            wallet=wallet,
            status__in=['pending', 'approved', 'processing']
        ).count()
        
        return Response({
            'success': True,
            'data': {
                'wallet': wallet_stats,
                'recent_transactions': transactions_data,
                'pending_deposits': pending_deposits,
                'pending_withdrawals': pending_withdrawals,
            }
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error fetching wallet overview: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_deposit(request):
    """Create a wallet deposit request"""
    try:
        user = request.user
        amount = request.data.get('amount')
        payment_method = request.data.get('payment_method', 'paypal')
        
        if not amount:
            return Response({
                'success': False,
                'message': 'Amount is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            amount = Decimal(str(amount))
            if amount <= 0:
                raise ValueError("Amount must be positive")
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'message': 'Invalid amount'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create deposit request
        success, result = WalletService.create_deposit_request(user, amount, payment_method)
        
        if success:
            deposit_request = result
            
            # Create payment order through payment system
            from payments.views import create_payment_order
            from rest_framework.test import APIRequestFactory
            
            factory = APIRequestFactory()
            payment_request = factory.post('/api/v1/payments/create-order/', {
                'amount': float(amount),
                'purpose': 'wallet_deposit',
                'description': f'Wallet deposit - ${amount}',
                'reference_id': str(deposit_request.id)
            }, format='json')
            payment_request.user = user
            
            payment_response = create_payment_order(payment_request)
            
            if payment_response.status_code == 200:
                payment_data = payment_response.data
                
                # Update deposit request with payment order ID
                deposit_request.payment_order_id = payment_data.get('order_id', '')
                deposit_request.save()
                
                return Response({
                    'success': True,
                    'deposit_request_id': str(deposit_request.id),
                    'payment_data': payment_data,
                    'message': f'Deposit request created for ${amount}'
                })
            else:
                return Response({
                    'success': False,
                    'message': 'Failed to create payment order'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            return Response({
                'success': False,
                'message': result
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error creating deposit: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def confirm_deposit(request):
    """Confirm a wallet deposit after payment"""
    try:
        user = request.user
        deposit_request_id = request.data.get('deposit_request_id')
        transaction_id = request.data.get('transaction_id')
        
        if not deposit_request_id:
            return Response({
                'success': False,
                'message': 'Deposit request ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Verify deposit request belongs to user
        try:
            deposit_request = WalletDepositRequest.objects.get(
                id=deposit_request_id,
                wallet__user=user
            )
        except WalletDepositRequest.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Deposit request not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Process the deposit
        success, message = WalletService.process_deposit(
            deposit_request_id,
            external_transaction_id=transaction_id
        )
        
        if success:
            # Get updated wallet balance
            wallet = WalletService.get_or_create_wallet(user)
            
            return Response({
                'success': True,
                'message': message,
                'new_balance': str(wallet.balance),
                'formatted_balance': wallet.formatted_balance
            })
        else:
            return Response({
                'success': False,
                'message': message
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error confirming deposit: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_withdrawal(request):
    """Create a wallet withdrawal request"""
    try:
        user = request.user
        amount = request.data.get('amount')
        paypal_email = request.data.get('paypal_email')
        
        if not amount or not paypal_email:
            return Response({
                'success': False,
                'message': 'Amount and PayPal email are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            amount = Decimal(str(amount))
            if amount <= 0:
                raise ValueError("Amount must be positive")
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'message': 'Invalid amount'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create withdrawal request
        success, result = WalletService.create_withdrawal_request(user, amount, paypal_email)
        
        if success:
            withdrawal_request = result
            
            return Response({
                'success': True,
                'withdrawal_request_id': str(withdrawal_request.id),
                'message': f'Withdrawal request created for ${amount}. Pending admin review.'
            })
        else:
            return Response({
                'success': False,
                'message': result
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error creating withdrawal: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def transaction_history(request):
    """Get user's wallet transaction history"""
    try:
        user = request.user
        wallet = WalletService.get_or_create_wallet(user)
        
        # Pagination
        limit = min(int(request.GET.get('limit', 20)), 100)
        offset = int(request.GET.get('offset', 0))
        
        # Filter by type if specified
        transaction_type = request.GET.get('type')  # credit, debit
        purpose = request.GET.get('purpose')
        
        transactions = wallet.transactions.all()
        
        if transaction_type:
            transactions = transactions.filter(transaction_type=transaction_type)
        
        if purpose:
            transactions = transactions.filter(purpose=purpose)
        
        total_count = transactions.count()
        transactions = transactions[offset:offset + limit]
        
        transactions_data = []
        for txn in transactions:
            transactions_data.append({
                'id': str(txn.id),
                'type': txn.transaction_type,
                'purpose': txn.get_purpose_display(),
                'amount': str(txn.amount),
                'formattedAmount': txn.formatted_amount,
                'balanceAfter': str(txn.balance_after),
                'status': txn.get_status_display(),
                'description': txn.description,
                'paymentMethod': txn.payment_method,
                'referenceId': txn.reference_id,
                'createdAt': txn.created_at.isoformat(),
                'completedAt': txn.completed_at.isoformat() if txn.completed_at else None,
            })
        
        return Response({
            'success': True,
            'data': {
                'transactions': transactions_data,
                'total_count': total_count,
                'has_more': offset + limit < total_count
            }
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error fetching transaction history: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def spend_from_wallet(request):
    """Spend money from wallet for purchases"""
    try:
        user = request.user
        amount = request.data.get('amount')
        purpose = request.data.get('purpose')
        description = request.data.get('description', '')

        if not amount or not purpose:
            return Response({
                'success': False,
                'message': 'Amount and purpose are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            amount = Decimal(str(amount))
            if amount <= 0:
                raise ValueError("Amount must be positive")
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'message': 'Invalid amount'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Spend from wallet
        success, result = WalletService.spend_from_wallet(user, amount, purpose, description)

        if success:
            transaction_record = result
            wallet = WalletService.get_or_create_wallet(user)

            return Response({
                'success': True,
                'transaction_id': str(transaction_record.id),
                'new_balance': str(wallet.balance),
                'formatted_balance': wallet.formatted_balance,
                'message': f'Successfully spent ${amount} from wallet'
            })
        else:
            return Response({
                'success': False,
                'message': result
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error spending from wallet: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def wallet_settings(request):
    """Get wallet system settings"""
    try:
        settings = WalletSettings.get_settings()

        return Response({
            'success': True,
            'settings': {
                'minimum_deposit': str(settings.minimum_deposit),
                'maximum_deposit': str(settings.maximum_deposit),
                'minimum_withdrawal': str(settings.minimum_withdrawal),
                'maximum_withdrawal': str(settings.maximum_withdrawal),
                'withdrawal_fee_percentage': str(settings.withdrawal_fee_percentage),
                'withdrawal_fee_fixed': str(settings.withdrawal_fee_fixed),
                'deposit_fee_percentage': str(settings.deposit_fee_percentage),
                'wallets_enabled': settings.wallets_enabled,
                'deposits_enabled': settings.deposits_enabled,
                'withdrawals_enabled': settings.withdrawals_enabled,
                'require_verification': settings.require_verification,
                'daily_withdrawal_limit': str(settings.daily_withdrawal_limit),
                'monthly_withdrawal_limit': str(settings.monthly_withdrawal_limit),
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error fetching wallet settings: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Admin Views

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def admin_pending_withdrawals(request):
    """Get pending withdrawal requests (admin only)"""
    try:
        if not request.user.is_staff:
            return Response({
                'success': False,
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)

        pending_withdrawals = WalletWithdrawalRequest.objects.filter(
            status='pending'
        ).select_related('wallet__user').order_by('-created_at')

        withdrawals_data = []
        for withdrawal in pending_withdrawals:
            withdrawals_data.append({
                'id': str(withdrawal.id),
                'user_id': withdrawal.wallet.user.id,
                'username': withdrawal.wallet.user.username,
                'user_email': withdrawal.wallet.user.email,
                'amount': str(withdrawal.amount),
                'paypal_email': withdrawal.paypal_email,
                'wallet_balance': str(withdrawal.wallet.balance),
                'created_at': withdrawal.created_at.isoformat(),
            })

        return Response({
            'success': True,
            'pending_withdrawals': withdrawals_data,
            'total_pending': len(withdrawals_data),
            'total_amount': sum(float(w.amount) for w in pending_withdrawals)
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error fetching pending withdrawals: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def admin_approve_withdrawal(request, withdrawal_id):
    """Approve a withdrawal request (admin only)"""
    try:
        if not request.user.is_staff:
            return Response({
                'success': False,
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)

        notes = request.data.get('notes', '')

        try:
            withdrawal = WalletWithdrawalRequest.objects.get(id=withdrawal_id)
        except WalletWithdrawalRequest.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Withdrawal request not found'
            }, status=status.HTTP_404_NOT_FOUND)

        if withdrawal.status != 'pending':
            return Response({
                'success': False,
                'message': 'Withdrawal request is not in pending status'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Update withdrawal request
        withdrawal.status = 'approved'
        withdrawal.reviewed_by = request.user
        withdrawal.reviewed_at = timezone.now()
        withdrawal.admin_notes = notes
        withdrawal.save()

        return Response({
            'success': True,
            'message': f'Withdrawal request for ${withdrawal.amount} approved'
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error approving withdrawal: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
