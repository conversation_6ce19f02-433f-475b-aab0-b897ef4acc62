# Generated by Django 5.0.4 on 2025-08-05 08:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "gamification",
            "0003_pointconversionsettings_pointconversiontransaction_and_more",
        ),
    ]

    operations = [
        migrations.<PERSON>er<PERSON>ield(
            model_name="challenge",
            name="challenge_type",
            field=models.CharField(
                choices=[
                    ("reading", "Reading Challenge"),
                    ("writing", "Writing Challenge"),
                    ("engagement", "Engagement Challenge"),
                    ("community", "Community Challenge"),
                    ("quiz", "Quiz Challenge"),
                    ("poll", "Poll Challenge"),
                    ("interactive", "Interactive Challenge"),
                ],
                max_length=20,
            ),
        ),
    ]
