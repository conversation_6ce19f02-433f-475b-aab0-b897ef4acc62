from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError
import uuid
import json

User = get_user_model()


class AIConfiguration(models.Model):
    """Global AI configuration settings manageable through Django admin"""

    PROVIDER_CHOICES = [
        ('openai', 'OpenAI (GPT)'),
        ('anthropic', 'Anthropic (Claude)'),
        ('google', 'Google (Gemini)'),
        ('huggingface', 'Hugging Face'),
        ('ollama', 'Ollama (Local)'),
        ('custom', 'Custom API'),
    ]

    MODEL_CHOICES = [
        # OpenAI Models
        ('gpt-4', 'GPT-4'),
        ('gpt-4-turbo', 'GPT-4 Turbo'),
        ('gpt-3.5-turbo', 'GPT-3.5 Turbo'),
        # Anthropic Models
        ('claude-3-opus', 'Claude 3 Opus'),
        ('claude-3-sonnet', 'Claude 3 Sonnet'),
        ('claude-3-haiku', 'Claude 3 Haiku'),
        # Google Models
        ('gemini-pro', 'Gemini Pro'),
        ('gemini-pro-vision', 'Gemini Pro Vision'),
        # Custom
        ('custom-model', 'Custom Model'),
    ]

    name = models.CharField(max_length=100, unique=True, help_text="Configuration name (e.g., 'Production AI', 'Development AI')")
    provider = models.CharField(max_length=20, choices=PROVIDER_CHOICES, default='openai')
    model_name = models.CharField(max_length=50, choices=MODEL_CHOICES, default='gpt-3.5-turbo')

    # API Configuration
    api_key = models.CharField(max_length=500, help_text="API key for the AI provider")
    api_base_url = models.URLField(blank=True, help_text="Custom API base URL (for custom providers)")
    api_version = models.CharField(max_length=20, blank=True, help_text="API version (if required)")

    # Model Parameters
    max_tokens = models.PositiveIntegerField(default=1000, help_text="Maximum tokens per request")
    temperature = models.FloatField(default=0.7, help_text="Temperature for response creativity (0.0-2.0)")
    top_p = models.FloatField(default=1.0, help_text="Top-p sampling parameter (0.0-1.0)")
    frequency_penalty = models.FloatField(default=0.0, help_text="Frequency penalty (-2.0 to 2.0)")
    presence_penalty = models.FloatField(default=0.0, help_text="Presence penalty (-2.0 to 2.0)")

    # Rate Limiting
    requests_per_minute = models.PositiveIntegerField(default=60, help_text="Maximum requests per minute")
    requests_per_day = models.PositiveIntegerField(default=1000, help_text="Maximum requests per day")

    # Feature Toggles
    is_active = models.BooleanField(default=True, help_text="Enable/disable this AI configuration")
    is_default = models.BooleanField(default=False, help_text="Use as default AI configuration")
    enable_content_generation = models.BooleanField(default=True)
    enable_grammar_improvement = models.BooleanField(default=True)
    enable_seo_suggestions = models.BooleanField(default=True)
    enable_text_completion = models.BooleanField(default=True)
    enable_readability_analysis = models.BooleanField(default=True)

    # Custom Headers (JSON field for additional API headers)
    custom_headers = models.JSONField(default=dict, blank=True, help_text="Additional headers for API requests (JSON format)")

    # Usage Statistics
    total_requests = models.PositiveIntegerField(default=0, editable=False)
    total_tokens_used = models.PositiveIntegerField(default=0, editable=False)
    last_used = models.DateTimeField(null=True, blank=True, editable=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AI Configuration"
        verbose_name_plural = "AI Configurations"
        ordering = ['-is_default', '-is_active', 'name']

    def __str__(self):
        status = "🟢" if self.is_active else "🔴"
        default = " (Default)" if self.is_default else ""
        return f"{status} {self.name} - {self.get_provider_display()}{default}"

    def clean(self):
        """Validate the configuration"""
        if self.temperature < 0.0 or self.temperature > 2.0:
            raise ValidationError("Temperature must be between 0.0 and 2.0")

        if self.top_p < 0.0 or self.top_p > 1.0:
            raise ValidationError("Top-p must be between 0.0 and 1.0")

        if self.frequency_penalty < -2.0 or self.frequency_penalty > 2.0:
            raise ValidationError("Frequency penalty must be between -2.0 and 2.0")

        if self.presence_penalty < -2.0 or self.presence_penalty > 2.0:
            raise ValidationError("Presence penalty must be between -2.0 and 2.0")

    def save(self, *args, **kwargs):
        # Ensure only one default configuration
        if self.is_default:
            AIConfiguration.objects.filter(is_default=True).exclude(pk=self.pk).update(is_default=False)

        # If this is the first configuration, make it default
        if not AIConfiguration.objects.exists():
            self.is_default = True

        super().save(*args, **kwargs)

    def update_usage_stats(self, tokens_used=0):
        """Update usage statistics"""
        self.total_requests += 1
        self.total_tokens_used += tokens_used
        self.last_used = timezone.now()
        self.save(update_fields=['total_requests', 'total_tokens_used', 'last_used'])

    @classmethod
    def get_default_config(cls):
        """Get the default AI configuration"""
        return cls.objects.filter(is_active=True, is_default=True).first() or cls.objects.filter(is_active=True).first()

    def get_api_headers(self):
        """Get complete headers for API requests"""
        headers = {
            'Content-Type': 'application/json',
        }

        # Add provider-specific headers
        if self.provider == 'openai':
            headers['Authorization'] = f'Bearer {self.api_key}'
        elif self.provider == 'anthropic':
            headers['x-api-key'] = self.api_key
            headers['anthropic-version'] = self.api_version or '2023-06-01'
        elif self.provider == 'google':
            headers['Authorization'] = f'Bearer {self.api_key}'
        elif self.provider == 'custom':
            headers['Authorization'] = f'Bearer {self.api_key}'

        # Add custom headers
        if self.custom_headers:
            headers.update(self.custom_headers)

        return headers

    def get_api_url(self):
        """Get the complete API URL"""
        if self.api_base_url:
            return self.api_base_url

        # Default URLs for known providers
        if self.provider == 'openai':
            return 'https://api.openai.com/v1/chat/completions'
        elif self.provider == 'anthropic':
            return 'https://api.anthropic.com/v1/messages'
        elif self.provider == 'google':
            return f'https://generativelanguage.googleapis.com/v1/models/{self.model_name}:generateContent'

        return self.api_base_url or ''


class AIWritingPreferences(models.Model):
    """User preferences for AI writing assistance"""
    TONE_CHOICES = [
        ('professional', 'Professional'),
        ('casual', 'Casual'),
        ('friendly', 'Friendly'),
        ('formal', 'Formal'),
        ('creative', 'Creative'),
        ('technical', 'Technical'),
        ('conversational', 'Conversational'),
    ]
    
    STYLE_CHOICES = [
        ('blog', 'Blog Post'),
        ('news', 'News Article'),
        ('tutorial', 'Tutorial'),
        ('review', 'Review'),
        ('opinion', 'Opinion Piece'),
        ('guide', 'How-to Guide'),
        ('listicle', 'List Article'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='ai_writing_preferences')
    preferred_tone = models.CharField(max_length=20, choices=TONE_CHOICES, default='professional')
    preferred_style = models.CharField(max_length=20, choices=STYLE_CHOICES, default='blog')
    target_audience = models.CharField(max_length=100, default='general audience')
    
    # Feature toggles
    enable_grammar_suggestions = models.BooleanField(default=True)
    enable_seo_suggestions = models.BooleanField(default=True)
    enable_content_generation = models.BooleanField(default=True)
    enable_readability_analysis = models.BooleanField(default=True)
    enable_auto_complete = models.BooleanField(default=True)
    
    # Content preferences
    preferred_word_count = models.PositiveIntegerField(default=800)
    include_references = models.BooleanField(default=True)
    include_images_suggestions = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.user.username}'s AI Writing Preferences"


class ContentPrompt(models.Model):
    """Predefined prompts for content generation"""
    PROMPT_TYPE_CHOICES = [
        ('introduction', 'Introduction'),
        ('conclusion', 'Conclusion'),
        ('outline', 'Content Outline'),
        ('title', 'Title Generation'),
        ('meta_description', 'Meta Description'),
        ('social_media', 'Social Media Post'),
        ('email', 'Email Content'),
        ('summary', 'Content Summary'),
    ]
    
    name = models.CharField(max_length=100)
    prompt_type = models.CharField(max_length=20, choices=PROMPT_TYPE_CHOICES)
    template = models.TextField(help_text="Use {topic}, {tone}, {audience} as placeholders")
    category = models.ForeignKey('blog.Category', on_delete=models.CASCADE, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    usage_count = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-usage_count', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.prompt_type})"


class AIWritingSession(models.Model):
    """Track AI writing assistance sessions"""
    SESSION_STATUS_CHOICES = [
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('abandoned', 'Abandoned'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ai_content_writing_sessions')
    post = models.ForeignKey('blog.Post', on_delete=models.CASCADE, null=True, blank=True, related_name='ai_content_sessions')
    
    session_data = models.JSONField(default=dict, help_text="Store session state and context")
    status = models.CharField(max_length=20, choices=SESSION_STATUS_CHOICES, default='active')
    
    # Analytics
    suggestions_generated = models.PositiveIntegerField(default=0)
    suggestions_accepted = models.PositiveIntegerField(default=0)
    words_generated = models.PositiveIntegerField(default=0)
    time_saved_minutes = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"AI Session {self.id} - {self.user.username}"


class ContentSuggestion(models.Model):
    """Individual AI-generated content suggestions"""
    SUGGESTION_TYPE_CHOICES = [
        ('grammar', 'Grammar Correction'),
        ('style', 'Style Improvement'),
        ('seo', 'SEO Optimization'),
        ('readability', 'Readability Enhancement'),
        ('expansion', 'Content Expansion'),
        ('completion', 'Text Completion'),
        ('title', 'Title Suggestion'),
        ('meta', 'Meta Description'),
        ('tags', 'Tag Suggestions'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('accepted', 'Accepted'),
        ('rejected', 'Rejected'),
        ('modified', 'Modified'),
    ]
    
    session = models.ForeignKey(AIWritingSession, on_delete=models.CASCADE, related_name='suggestions')
    suggestion_type = models.CharField(max_length=20, choices=SUGGESTION_TYPE_CHOICES)
    
    original_text = models.TextField()
    suggested_text = models.TextField()
    explanation = models.TextField(blank=True)
    confidence_score = models.FloatField(default=0.0, help_text="AI confidence score 0-1")
    
    # Position in content
    start_position = models.PositiveIntegerField(default=0)
    end_position = models.PositiveIntegerField(default=0)
    
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    user_feedback = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.suggestion_type} suggestion for {self.session.user.username}"


class AIUsageAnalytics(models.Model):
    """Track AI writing feature usage for analytics"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ai_content_usage_analytics')
    feature_used = models.CharField(max_length=50)
    usage_count = models.PositiveIntegerField(default=1)
    total_time_saved_minutes = models.PositiveIntegerField(default=0)
    total_words_generated = models.PositiveIntegerField(default=0)
    
    # Monthly aggregation
    year = models.PositiveIntegerField()
    month = models.PositiveIntegerField()
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['user', 'feature_used', 'year', 'month']
        ordering = ['-year', '-month']
    
    def __str__(self):
        return f"{self.user.username} - {self.feature_used} ({self.year}-{self.month:02d})"


class ContentTemplate(models.Model):
    """Reusable content templates for different types of posts"""
    name = models.CharField(max_length=100)
    description = models.TextField()
    template_content = models.TextField(help_text="Use placeholders like {title}, {topic}, {audience}")
    category = models.ForeignKey('blog.Category', on_delete=models.CASCADE, null=True, blank=True)
    
    # Template metadata
    estimated_word_count = models.PositiveIntegerField(default=500)
    difficulty_level = models.CharField(
        max_length=20,
        choices=[
            ('beginner', 'Beginner'),
            ('intermediate', 'Intermediate'),
            ('advanced', 'Advanced'),
        ],
        default='intermediate'
    )
    
    is_public = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_content_templates')
    usage_count = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-usage_count', 'name']
    
    def __str__(self):
        return self.name
