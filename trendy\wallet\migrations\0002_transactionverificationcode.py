# Generated by Django 5.1.7 on 2025-06-24 07:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('wallet', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TransactionVerificationCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('deposit', 'Deposit'), ('withdrawal', 'Withdrawal'), ('purchase', 'Purchase'), ('transfer', 'Transfer')], max_length=20)),
                ('transaction_id', models.CharField(max_length=100)),
                ('verification_code', models.CharField(max_length=10)),
                ('is_used', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('used_at', models.DateTimeField(blank=True, null=True)),
                ('attempts', models.PositiveIntegerField(default=0)),
                ('max_attempts', models.PositiveIntegerField(default=3)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='verification_codes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'transaction_type', 'is_used'], name='wallet_tran_user_id_a84740_idx'), models.Index(fields=['verification_code', 'expires_at'], name='wallet_tran_verific_e5e4ad_idx'), models.Index(fields=['transaction_id'], name='wallet_tran_transac_92242e_idx')],
            },
        ),
    ]
