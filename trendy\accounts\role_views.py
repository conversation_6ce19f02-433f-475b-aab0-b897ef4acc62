from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsA<PERSON>enticated, IsAdminUser
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count
from django.utils import timezone
from datetime import timedelta
from .serializers import UserSerializer
import logging

logger = logging.getLogger(__name__)

User = get_user_model()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_permissions(request):
    """Get current user's permissions and role information"""
    try:
        user = request.user
        
        # Get user's role and permissions
        role_info = {
            'user_id': user.id,
            'username': user.username,
            'email': user.email,
            'role': user.user_role,
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
            'is_content_creator': user.is_content_creator,
            'is_regular_user': user.is_regular_user,
            'can_create_content': user.can_create_content(),
            'can_moderate_content': user.can_moderate_content(),
            'groups': [group.name for group in user.groups.all()],
            'permissions': {
                'can_create_post': user.has_perm('blog.can_create_post'),
                'can_publish_post': user.has_perm('blog.can_publish_post'),
                'can_edit_own_post': user.has_perm('blog.can_edit_own_post'),
                'can_delete_own_post': user.has_perm('blog.can_delete_own_post'),
                'can_moderate_posts': user.has_perm('blog.can_moderate_posts'),
                'can_create_poll': user.has_perm('interactive.can_create_poll'),
                'can_create_quiz': user.has_perm('interactive.can_create_quiz'),
                'can_moderate_interactive': user.has_perm('interactive.can_moderate_interactive'),
            }
        }
        
        return Response({
            'success': True,
            'role_info': role_info
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def promote_user_to_content_creator(request, user_id):
    """Promote a user to content creator role (Admin only)"""
    try:
        user = get_object_or_404(User, id=user_id)
        
        # Don't change admin users
        if user.is_staff or user.is_superuser:
            return Response({
                'success': False,
                'error': 'Cannot change role of admin users'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Promote user
        success = user.promote_to_content_creator()
        
        if success:
            return Response({
                'success': True,
                'message': f'User {user.username} promoted to Content Creator',
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'role': user.user_role,
                    'is_content_creator': user.is_content_creator
                }
            })
        else:
            return Response({
                'success': False,
                'error': 'Failed to promote user. Content Creators group may not exist.'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def demote_user_to_regular(request, user_id):
    """Demote a user to regular user role (Admin only)"""
    try:
        user = get_object_or_404(User, id=user_id)
        
        # Don't change admin users
        if user.is_staff or user.is_superuser:
            return Response({
                'success': False,
                'error': 'Cannot change role of admin users'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Demote user
        success = user.demote_to_regular_user()
        
        if success:
            return Response({
                'success': True,
                'message': f'User {user.username} demoted to Regular User',
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'role': user.user_role,
                    'is_content_creator': user.is_content_creator
                }
            })
        else:
            return Response({
                'success': False,
                'error': 'Failed to demote user. Regular Users group may not exist.'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def list_users_by_role(request):
    """List all users grouped by role (Admin only)"""
    try:
        # Get all users with their roles
        users_by_role = {
            'admins': [],
            'content_creators': [],
            'regular_users': [],
            'unassigned': []
        }
        
        for user in User.objects.all().select_related().prefetch_related('groups'):
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_active': user.is_active,
                'date_joined': user.date_joined,
                'role': user.user_role,
                'groups': [group.name for group in user.groups.all()]
            }
            
            if user.is_superuser:
                users_by_role['admins'].append(user_data)
            elif user.is_staff:
                users_by_role['admins'].append(user_data)
            elif user.is_content_creator:
                users_by_role['content_creators'].append(user_data)
            elif user.is_regular_user:
                users_by_role['regular_users'].append(user_data)
            else:
                users_by_role['unassigned'].append(user_data)
        
        return Response({
            'success': True,
            'users_by_role': users_by_role,
            'counts': {
                'admins': len(users_by_role['admins']),
                'content_creators': len(users_by_role['content_creators']),
                'regular_users': len(users_by_role['regular_users']),
                'unassigned': len(users_by_role['unassigned']),
                'total': User.objects.count()
            }
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_content_creation_permission(request):
    """Check if current user can create content"""
    try:
        user = request.user
        
        can_create = user.can_create_content()
        
        return Response({
            'success': True,
            'can_create_content': can_create,
            'role': user.user_role,
            'message': 'You can create content' if can_create else 'Contact an admin to become a content creator'
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def bulk_promote_users(request):
    """Bulk promote multiple users to content creators (Admin only)"""
    try:
        user_ids = request.data.get('user_ids', [])
        if not user_ids:
            return Response({
                'success': False,
                'error': 'No user IDs provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        promoted_users = []
        failed_users = []

        for user_id in user_ids:
            try:
                user = User.objects.get(id=user_id)
                if not user.is_content_creator:
                    user.promote_to_content_creator()
                    promoted_users.append({
                        'id': user.id,
                        'username': user.username,
                        'email': user.email
                    })
                else:
                    failed_users.append({
                        'id': user.id,
                        'username': user.username,
                        'reason': 'Already a content creator'
                    })
            except User.DoesNotExist:
                failed_users.append({
                    'id': user_id,
                    'reason': 'User not found'
                })
            except Exception as e:
                failed_users.append({
                    'id': user_id,
                    'reason': str(e)
                })

        return Response({
            'success': True,
            'promoted_users': promoted_users,
            'failed_users': failed_users,
            'message': f'Promoted {len(promoted_users)} users to content creators'
        })

    except Exception as e:
        logger.error(f"Error in bulk promote users: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def bulk_demote_users(request):
    """Bulk demote multiple users to regular users (Admin only)"""
    try:
        user_ids = request.data.get('user_ids', [])
        if not user_ids:
            return Response({
                'success': False,
                'error': 'No user IDs provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        demoted_users = []
        failed_users = []

        for user_id in user_ids:
            try:
                user = User.objects.get(id=user_id)
                if user.is_content_creator and not user.is_staff:
                    user.demote_to_regular_user()
                    demoted_users.append({
                        'id': user.id,
                        'username': user.username,
                        'email': user.email
                    })
                elif user.is_staff:
                    failed_users.append({
                        'id': user.id,
                        'username': user.username,
                        'reason': 'Cannot demote staff users'
                    })
                else:
                    failed_users.append({
                        'id': user.id,
                        'username': user.username,
                        'reason': 'Already a regular user'
                    })
            except User.DoesNotExist:
                failed_users.append({
                    'id': user_id,
                    'reason': 'User not found'
                })
            except Exception as e:
                failed_users.append({
                    'id': user_id,
                    'reason': str(e)
                })

        return Response({
            'success': True,
            'demoted_users': demoted_users,
            'failed_users': failed_users,
            'message': f'Demoted {len(demoted_users)} users to regular users'
        })

    except Exception as e:
        logger.error(f"Error in bulk demote users: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def role_analytics(request):
    """Get analytics about user roles and permissions (Admin only)"""
    try:
        # Count users by role
        total_users = User.objects.count()
        admin_users = User.objects.filter(Q(is_staff=True) | Q(is_superuser=True)).count()
        content_creators = User.objects.filter(groups__name='Content Creators').count()
        regular_users = total_users - admin_users - content_creators

        # Recent activity (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_users = User.objects.filter(date_joined__gte=thirty_days_ago).count()

        # Content creation stats
        from blog.models import Post
        total_posts = Post.objects.count()
        posts_by_creators = Post.objects.filter(author__groups__name='Content Creators').count()
        posts_by_admins = Post.objects.filter(Q(author__is_staff=True) | Q(author__is_superuser=True)).count()

        # Group membership stats
        content_creator_group = Group.objects.get(name='Content Creators')
        regular_user_group = Group.objects.get(name='Regular Users')

        analytics = {
            'user_counts': {
                'total_users': total_users,
                'admin_users': admin_users,
                'content_creators': content_creators,
                'regular_users': regular_users,
                'recent_users_30_days': recent_users
            },
            'content_stats': {
                'total_posts': total_posts,
                'posts_by_creators': posts_by_creators,
                'posts_by_admins': posts_by_admins,
                'posts_by_regular_users': total_posts - posts_by_creators - posts_by_admins
            },
            'group_stats': {
                'content_creator_members': content_creator_group.user_set.count(),
                'regular_user_members': regular_user_group.user_set.count()
            },
            'percentages': {
                'admin_percentage': round((admin_users / total_users) * 100, 2) if total_users > 0 else 0,
                'creator_percentage': round((content_creators / total_users) * 100, 2) if total_users > 0 else 0,
                'regular_percentage': round((regular_users / total_users) * 100, 2) if total_users > 0 else 0
            }
        }

        return Response({
            'success': True,
            'analytics': analytics
        })

    except Exception as e:
        logger.error(f"Error getting role analytics: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def permission_audit_log(request):
    """Get audit log of permission changes (Admin only)"""
    try:
        # This would typically require a separate audit log model
        # For now, we'll return recent user role changes based on group membership

        content_creator_group = Group.objects.get(name='Content Creators')
        regular_user_group = Group.objects.get(name='Regular Users')

        # Get recent group memberships (this is a simplified version)
        recent_creators = content_creator_group.user_set.select_related().order_by('-date_joined')[:20]
        recent_regulars = regular_user_group.user_set.select_related().order_by('-date_joined')[:20]

        audit_log = []

        for user in recent_creators:
            audit_log.append({
                'user_id': user.id,
                'username': user.username,
                'email': user.email,
                'action': 'promoted_to_content_creator',
                'timestamp': user.date_joined.isoformat(),
                'role': 'content_creator'
            })

        for user in recent_regulars:
            if not user.groups.filter(name='Content Creators').exists():
                audit_log.append({
                    'user_id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'action': 'assigned_regular_user',
                    'timestamp': user.date_joined.isoformat(),
                    'role': 'regular_user'
                })

        # Sort by timestamp (most recent first)
        audit_log.sort(key=lambda x: x['timestamp'], reverse=True)

        return Response({
            'success': True,
            'audit_log': audit_log[:50],  # Return last 50 entries
            'message': 'Permission audit log retrieved successfully'
        })

    except Exception as e:
        logger.error(f"Error getting permission audit log: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def search_users_by_criteria(request):
    """Advanced user search with filtering by role, permissions, and activity (Admin only)"""
    try:
        # Get query parameters
        search_query = request.GET.get('search', '')
        role_filter = request.GET.get('role', '')
        has_posts = request.GET.get('has_posts', '')
        date_joined_after = request.GET.get('date_joined_after', '')
        date_joined_before = request.GET.get('date_joined_before', '')
        is_active = request.GET.get('is_active', '')
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))

        # Start with all users
        users = User.objects.all()

        # Apply search filter
        if search_query:
            users = users.filter(
                Q(username__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query)
            )

        # Apply role filter
        if role_filter:
            if role_filter == 'admin':
                users = users.filter(Q(is_staff=True) | Q(is_superuser=True))
            elif role_filter == 'content_creator':
                users = users.filter(groups__name='Content Creators')
            elif role_filter == 'regular_user':
                users = users.exclude(Q(is_staff=True) | Q(is_superuser=True) | Q(groups__name='Content Creators'))

        # Apply content filter
        if has_posts == 'true':
            users = users.filter(posts__isnull=False).distinct()
        elif has_posts == 'false':
            users = users.filter(posts__isnull=True)

        # Apply date filters
        if date_joined_after:
            users = users.filter(date_joined__gte=date_joined_after)
        if date_joined_before:
            users = users.filter(date_joined__lte=date_joined_before)

        # Apply active filter
        if is_active == 'true':
            users = users.filter(is_active=True)
        elif is_active == 'false':
            users = users.filter(is_active=False)

        # Get total count before pagination
        total_count = users.count()

        # Apply pagination
        start = (page - 1) * page_size
        end = start + page_size
        users = users[start:end]

        # Serialize user data
        user_data = []
        for user in users:
            user_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.user_role,
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser,
                'is_content_creator': user.is_content_creator,
                'is_regular_user': user.is_regular_user,
                'is_active': user.is_active,
                'date_joined': user.date_joined.isoformat(),
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'groups': [group.name for group in user.groups.all()],
                'post_count': user.posts.count() if hasattr(user, 'posts') else 0,
                'can_create_content': user.can_create_content(),
                'can_moderate_content': user.can_moderate_content()
            })

        # Calculate pagination info
        total_pages = (total_count + page_size - 1) // page_size
        has_next = page < total_pages
        has_previous = page > 1

        return Response({
            'success': True,
            'users': user_data,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': total_pages,
                'has_next': has_next,
                'has_previous': has_previous
            },
            'filters_applied': {
                'search_query': search_query,
                'role_filter': role_filter,
                'has_posts': has_posts,
                'date_joined_after': date_joined_after,
                'date_joined_before': date_joined_before,
                'is_active': is_active
            }
        })

    except Exception as e:
        logger.error(f"Error searching users by criteria: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
