from django.contrib import admin
from .models import Region, Country, CountryIPRange, UserLocationHistory


@admin.register(Region)
class RegionAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'country_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['name']

    def country_count(self, obj):
        return obj.countries.filter(is_active=True).count()
    country_count.short_description = 'Active Countries'


@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = [
        'display_name', 'code', 'code_3', 'region', 'currency_code', 
        'priority', 'is_active', 'allow_global_content'
    ]
    list_filter = ['region', 'is_active', 'allow_global_content', 'created_at']
    search_fields = ['name', 'code', 'code_3', 'currency_code']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-priority', 'name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'code_3', 'region', 'flag_emoji')
        }),
        ('Currency & Contact', {
            'fields': ('currency_code', 'currency_name', 'phone_code')
        }),
        ('Content Settings', {
            'fields': ('is_active', 'allow_global_content', 'priority')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def display_name(self, obj):
        return obj.display_name
    display_name.short_description = 'Country'


@admin.register(CountryIPRange)
class CountryIPRangeAdmin(admin.ModelAdmin):
    list_display = ['country', 'ip_start', 'ip_end', 'is_active', 'created_at']
    list_filter = ['country', 'is_active', 'created_at']
    search_fields = ['country__name', 'ip_start', 'ip_end']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['country__name', 'ip_start']


@admin.register(UserLocationHistory)
class UserLocationHistoryAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'detected_country', 'detection_method', 
        'confidence_score', 'ip_address', 'created_at'
    ]
    list_filter = [
        'detection_method', 'detected_country', 'created_at'
    ]
    search_fields = [
        'user__username', 'user__email', 'detected_country__name', 'ip_address'
    ]
    readonly_fields = ['created_at']
    ordering = ['-created_at']
    
    def has_add_permission(self, request):
        # Prevent manual addition - these should be created automatically
        return False
