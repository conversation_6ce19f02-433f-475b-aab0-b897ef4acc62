# Generated by Django 4.2.23 on 2025-07-29 10:47

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Region",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                (
                    "code",
                    models.CharField(
                        help_text="Short code for the region (e.g., 'AF' for Africa, 'EU' for Europe)",
                        max_length=10,
                        unique=True,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["name"],
                "indexes": [
                    models.Index(fields=["code"], name="regional_re_code_0d4115_idx"),
                    models.Index(
                        fields=["is_active"], name="regional_re_is_acti_7fec4c_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="Country",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                (
                    "code",
                    models.CharField(
                        help_text="ISO 3166-1 alpha-2 country code (e.g., 'UG' for Uganda)",
                        max_length=2,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                "^[A-Z]{2}$", "Country code must be 2 uppercase letters"
                            )
                        ],
                    ),
                ),
                (
                    "code_3",
                    models.CharField(
                        help_text="ISO 3166-1 alpha-3 country code (e.g., 'UGA' for Uganda)",
                        max_length=3,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                "^[A-Z]{3}$", "Country code must be 3 uppercase letters"
                            )
                        ],
                    ),
                ),
                ("currency_code", models.CharField(blank=True, max_length=3)),
                ("currency_name", models.CharField(blank=True, max_length=50)),
                ("phone_code", models.CharField(blank=True, max_length=10)),
                ("flag_emoji", models.CharField(blank=True, max_length=10)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "allow_global_content",
                    models.BooleanField(
                        default=True,
                        help_text="Whether users from this country can see global content",
                    ),
                ),
                (
                    "priority",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Display priority in country lists (higher = first)",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "region",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="countries",
                        to="regional.region",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Countries",
                "ordering": ["-priority", "name"],
            },
        ),
        migrations.CreateModel(
            name="UserLocationHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ip_address", models.GenericIPAddressField()),
                (
                    "detection_method",
                    models.CharField(
                        choices=[
                            ("ip_geolocation", "IP Geolocation"),
                            ("user_profile", "User Profile"),
                            ("manual_selection", "Manual Selection"),
                            ("browser_locale", "Browser Locale"),
                        ],
                        default="ip_geolocation",
                        max_length=20,
                    ),
                ),
                (
                    "confidence_score",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        help_text="Confidence in location detection (0.0 - 1.0)",
                        max_digits=3,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "detected_country",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="regional.country",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="location_history",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created_at"],
                        name="regional_us_user_id_402765_idx",
                    ),
                    models.Index(
                        fields=["detected_country"],
                        name="regional_us_detecte_cd7de6_idx",
                    ),
                    models.Index(
                        fields=["ip_address"], name="regional_us_ip_addr_6c2785_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="CountryIPRange",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ip_start", models.GenericIPAddressField()),
                ("ip_end", models.GenericIPAddressField()),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "country",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ip_ranges",
                        to="regional.country",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["ip_start", "ip_end"],
                        name="regional_co_ip_star_2d137f_idx",
                    ),
                    models.Index(
                        fields=["country", "is_active"],
                        name="regional_co_country_dc76b9_idx",
                    ),
                ],
                "unique_together": {("ip_start", "ip_end")},
            },
        ),
        migrations.AddIndex(
            model_name="country",
            index=models.Index(fields=["code"], name="regional_co_code_f171b0_idx"),
        ),
        migrations.AddIndex(
            model_name="country",
            index=models.Index(fields=["code_3"], name="regional_co_code_3_408bcd_idx"),
        ),
        migrations.AddIndex(
            model_name="country",
            index=models.Index(
                fields=["region", "is_active"], name="regional_co_region__40fdec_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="country",
            index=models.Index(
                fields=["-priority", "name"], name="regional_co_priorit_2b69d9_idx"
            ),
        ),
    ]
