import requests
import json
import uuid
from decimal import Decimal
from django.db import transaction
from django.utils import timezone
from django.conf import settings
from django.core.mail import send_mail

from .models import (
    PayPalAccount, PaymentTransaction, PayPalWebhook, 
    PayPalPayoutBatch, PaymentSettings, UserPayPalProfile
)


class PayPalService:
    """Complete PayPal integration for both incoming and outgoing payments"""
    
    def __init__(self):
        self.account = PayPalAccount.get_active_account()
        self.settings = PaymentSettings.get_settings()
        
        if not self.account:
            raise ValueError("No active PayPal account configured")
        
        # Set API URLs based on environment
        if self.account.environment == 'sandbox':
            self.base_url = 'https://api.sandbox.paypal.com'
            self.checkout_url = 'https://www.sandbox.paypal.com'
        else:
            self.base_url = 'https://api.paypal.com'
            self.checkout_url = 'https://www.paypal.com'
    
    def get_access_token(self):
        """Get PayPal access token for API calls"""
        # Check if using demo credentials
        if self.account.client_id == 'demo_client_id':
            raise Exception("Demo PayPal credentials - use development fallback")

        url = f"{self.base_url}/v1/oauth2/token"

        headers = {
            'Accept': 'application/json',
            'Accept-Language': 'en_US',
        }

        data = 'grant_type=client_credentials'

        response = requests.post(
            url,
            headers=headers,
            data=data,
            auth=(self.account.client_id, self.account.client_secret)
        )

        if response.status_code == 200:
            return response.json()['access_token']
        else:
            raise Exception(f"Failed to get PayPal access token: {response.text}")
    
    # INCOMING PAYMENTS (Users pay Admin)
    
    def create_payment_order(self, user, amount, purpose, description):
        """Create PayPal order for user to pay admin"""
        try:
            access_token = self.get_access_token()
            reference_id = f"TRENDY_{purpose.upper()}_{uuid.uuid4().hex[:8]}"
            
            # Create payment transaction record
            payment_transaction = PaymentTransaction.objects.create(
                user=user,
                transaction_type='incoming',
                payment_purpose=purpose,
                amount=amount,
                description=description,
                reference_id=reference_id,
                payee_email=self.account.business_email,
                status='pending'
            )
            
            # Create PayPal order
            url = f"{self.base_url}/v2/checkout/orders"
            
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {access_token}',
                'PayPal-Request-Id': reference_id,
            }
            
            order_data = {
                "intent": "CAPTURE",
                "purchase_units": [{
                    "reference_id": reference_id,
                    "amount": {
                        "currency_code": "USD",
                        "value": str(amount)
                    },
                    "description": description,
                    "payee": {
                        "email_address": self.account.business_email
                    }
                }],
                "application_context": {
                    "brand_name": "Trendy App",
                    "landing_page": "BILLING",
                    "user_action": "PAY_NOW",
                    "return_url": f"{settings.FRONTEND_URL}/payment/success",
                    "cancel_url": f"{settings.FRONTEND_URL}/payment/cancel"
                }
            }
            
            response = requests.post(url, headers=headers, json=order_data)
            
            if response.status_code == 201:
                order = response.json()
                
                # Update transaction with PayPal order ID
                payment_transaction.paypal_payment_id = order['id']
                payment_transaction.save()
                
                # Get approval URL
                approval_url = None
                for link in order['links']:
                    if link['rel'] == 'approve':
                        approval_url = link['href']
                        break
                
                return {
                    'success': True,
                    'order_id': order['id'],
                    'approval_url': approval_url,
                    'transaction_id': payment_transaction.id
                }
            else:
                payment_transaction.status = 'failed'
                payment_transaction.error_message = response.text
                payment_transaction.save()
                
                return {
                    'success': False,
                    'error': f"Failed to create PayPal order: {response.text}"
                }
                
        except Exception as e:
            # Check if this is a demo credentials error
            if 'Demo PayPal credentials' in str(e):
                # Return a special error that the view can handle
                return {
                    'success': False,
                    'error': 'DEMO_CREDENTIALS_ERROR',
                    'demo_mode': True
                }
            return {
                'success': False,
                'error': f"Error creating payment order: {str(e)}"
            }
    
    def capture_payment_order(self, order_id):
        """Capture approved PayPal order"""
        try:
            access_token = self.get_access_token()
            
            url = f"{self.base_url}/v2/checkout/orders/{order_id}/capture"
            
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {access_token}',
            }
            
            response = requests.post(url, headers=headers)
            
            if response.status_code == 201:
                capture_data = response.json()
                
                # Update transaction status
                try:
                    payment_transaction = PaymentTransaction.objects.get(
                        paypal_payment_id=order_id
                    )
                    payment_transaction.status = 'completed'
                    payment_transaction.completed_at = timezone.now()
                    
                    # Extract payer information
                    if 'payer' in capture_data:
                        payment_transaction.payer_email = capture_data['payer'].get('email_address', '')
                        payment_transaction.paypal_payer_id = capture_data['payer'].get('payer_id', '')
                    
                    payment_transaction.save()
                    
                    # Send confirmation email
                    self.send_payment_confirmation(payment_transaction)
                    
                    return {
                        'success': True,
                        'capture_id': capture_data['id'],
                        'transaction_id': payment_transaction.id
                    }
                    
                except PaymentTransaction.DoesNotExist:
                    return {
                        'success': False,
                        'error': 'Payment transaction not found'
                    }
            else:
                return {
                    'success': False,
                    'error': f"Failed to capture payment: {response.text}"
                }
                
        except Exception as e:
            # Check if this is a demo credentials error
            if 'Demo PayPal credentials' in str(e):
                # Return a special error that the view can handle
                return {
                    'success': False,
                    'error': 'DEMO_CREDENTIALS_ERROR',
                    'demo_mode': True
                }
            return {
                'success': False,
                'error': f"Error capturing payment: {str(e)}"
            }
    
    # OUTGOING PAYMENTS (Admin pays Users)
    
    def create_payout_batch(self, payout_items):
        """Create batch payout to multiple users"""
        try:
            access_token = self.get_access_token()
            batch_id = f"TRENDY_BATCH_{uuid.uuid4().hex[:8]}"
            
            # Calculate total amount
            total_amount = sum(Decimal(str(item['amount'])) for item in payout_items)
            
            # Create batch record
            payout_batch = PayPalPayoutBatch.objects.create(
                batch_id=batch_id,
                total_amount=total_amount,
                total_items=len(payout_items),
                batch_status='pending'
            )
            
            # Prepare PayPal payout request
            url = f"{self.base_url}/v1/payments/payouts"
            
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {access_token}',
            }
            
            # Format items for PayPal
            paypal_items = []
            for item in payout_items:
                paypal_items.append({
                    "recipient_type": "EMAIL",
                    "amount": {
                        "value": str(item['amount']),
                        "currency": "USD"
                    },
                    "receiver": item['recipient_email'],
                    "note": item['note'],
                    "sender_item_id": item['reference_id']
                })
            
            payout_data = {
                "sender_batch_header": {
                    "sender_batch_id": batch_id,
                    "email_subject": "You have a payment from Trendy App!",
                    "email_message": "You have received a reward payment from Trendy App. Thanks for being an awesome community member!"
                },
                "items": paypal_items
            }
            
            response = requests.post(url, headers=headers, json=payout_data)
            
            if response.status_code == 201:
                payout_response = response.json()
                
                # Update batch with PayPal batch ID
                payout_batch.paypal_batch_id = payout_response['batch_header']['payout_batch_id']
                payout_batch.batch_status = 'processing'
                payout_batch.submitted_at = timezone.now()
                payout_batch.save()
                
                # Update individual transaction records
                for item in payout_items:
                    try:
                        transaction = PaymentTransaction.objects.get(
                            reference_id=item['reference_id']
                        )
                        transaction.status = 'processing'
                        transaction.paypal_batch_id = payout_batch.paypal_batch_id
                        transaction.processed_at = timezone.now()
                        transaction.save()
                    except PaymentTransaction.DoesNotExist:
                        continue
                
                return {
                    'success': True,
                    'batch_id': batch_id,
                    'paypal_batch_id': payout_batch.paypal_batch_id
                }
            else:
                payout_batch.batch_status = 'denied'
                payout_batch.error_message = response.text
                payout_batch.save()
                
                return {
                    'success': False,
                    'error': f"Failed to create payout batch: {response.text}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"Error creating payout batch: {str(e)}"
            }
    
    def create_single_payout(self, user, amount, purpose, description):
        """Create single payout to user"""
        try:
            # Get user's PayPal profile
            try:
                paypal_profile = UserPayPalProfile.objects.get(user=user)
                if not paypal_profile.is_verified:
                    return {
                        'success': False,
                        'error': 'User PayPal account not verified'
                    }
                recipient_email = paypal_profile.paypal_email
            except UserPayPalProfile.DoesNotExist:
                return {
                    'success': False,
                    'error': 'User PayPal profile not found'
                }
            
            # Create transaction record
            reference_id = f"TRENDY_{purpose.upper()}_{uuid.uuid4().hex[:8]}"
            
            payment_transaction = PaymentTransaction.objects.create(
                user=user,
                transaction_type='outgoing',
                payment_purpose=purpose,
                amount=amount,
                description=description,
                reference_id=reference_id,
                payer_email=self.account.business_email,
                payee_email=recipient_email,
                status='pending'
            )
            
            # Create payout batch with single item
            payout_items = [{
                'recipient_email': recipient_email,
                'amount': amount,
                'note': description,
                'reference_id': reference_id
            }]
            
            result = self.create_payout_batch(payout_items)
            
            if result['success']:
                return {
                    'success': True,
                    'transaction_id': payment_transaction.id,
                    'batch_id': result['batch_id']
                }
            else:
                payment_transaction.status = 'failed'
                payment_transaction.error_message = result['error']
                payment_transaction.save()
                
                return result
                
        except Exception as e:
            return {
                'success': False,
                'error': f"Error creating single payout: {str(e)}"
            }
    
    # WEBHOOK HANDLING
    
    def process_webhook(self, webhook_data):
        """Process PayPal webhook events"""
        try:
            event_type = webhook_data.get('event_type')
            event_id = webhook_data.get('id')
            resource = webhook_data.get('resource', {})
            
            # Store webhook for processing
            webhook = PayPalWebhook.objects.create(
                webhook_id=webhook_data.get('webhook_id', ''),
                event_type=event_type,
                event_id=event_id,
                resource_id=resource.get('id', ''),
                resource_type=resource.get('resource_type', ''),
                raw_data=webhook_data
            )
            
            # Process based on event type
            if event_type == 'PAYMENT.CAPTURE.COMPLETED':
                self.handle_payment_completed(webhook, resource)
            elif event_type == 'PAYMENTS.PAYOUTS-ITEM.SUCCEEDED':
                self.handle_payout_succeeded(webhook, resource)
            elif event_type == 'PAYMENTS.PAYOUTS-ITEM.FAILED':
                self.handle_payout_failed(webhook, resource)
            
            webhook.processed = True
            webhook.processed_at = timezone.now()
            webhook.save()
            
            return {'success': True}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def handle_payment_completed(self, webhook, resource):
        """Handle completed payment webhook"""
        try:
            # Find transaction by PayPal payment ID
            transaction = PaymentTransaction.objects.get(
                paypal_payment_id=resource.get('supplementary_data', {}).get('related_ids', {}).get('order_id')
            )
            
            transaction.status = 'completed'
            transaction.completed_at = timezone.now()
            transaction.save()
            
            webhook.payment_transaction = transaction
            webhook.save()
            
        except PaymentTransaction.DoesNotExist:
            pass  # Transaction not found, might be external payment
    
    def handle_payout_succeeded(self, webhook, resource):
        """Handle successful payout webhook"""
        try:
            # Find transaction by reference ID
            sender_item_id = resource.get('sender_item_id')
            if sender_item_id:
                transaction = PaymentTransaction.objects.get(reference_id=sender_item_id)
                transaction.status = 'completed'
                transaction.completed_at = timezone.now()
                transaction.paypal_item_id = resource.get('payout_item_id')
                transaction.save()
                
                webhook.payment_transaction = transaction
                webhook.save()
                
                # Send completion notification
                self.send_payout_confirmation(transaction)
                
        except PaymentTransaction.DoesNotExist:
            pass
    
    def handle_payout_failed(self, webhook, resource):
        """Handle failed payout webhook"""
        try:
            sender_item_id = resource.get('sender_item_id')
            if sender_item_id:
                transaction = PaymentTransaction.objects.get(reference_id=sender_item_id)
                transaction.status = 'failed'
                transaction.error_message = resource.get('errors', [{}])[0].get('message', 'Payout failed')
                transaction.save()
                
                webhook.payment_transaction = transaction
                webhook.save()
                
        except PaymentTransaction.DoesNotExist:
            pass
    
    # EMAIL NOTIFICATIONS
    
    def send_payment_confirmation(self, transaction):
        """Send payment confirmation email"""
        send_mail(
            subject=f"Payment Confirmed - ${transaction.amount}",
            message=f"""
Hi {transaction.user.username},

Your payment of ${transaction.amount} for {transaction.get_payment_purpose_display()} has been confirmed!

Transaction Details:
- Amount: ${transaction.amount}
- Purpose: {transaction.get_payment_purpose_display()}
- Transaction ID: {transaction.reference_id}
- Date: {transaction.completed_at.strftime('%Y-%m-%d %H:%M')}

Thank you for your purchase!

Best regards,
Trendy App Team
            """,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[transaction.user.email],
            fail_silently=True,
        )
    
    def send_payout_confirmation(self, transaction):
        """Send payout confirmation email"""
        send_mail(
            subject=f"Reward Payment Sent - ${transaction.amount}",
            message=f"""
Hi {transaction.user.username},

Great news! Your reward payment of ${transaction.amount} has been sent to your PayPal account!

Payment Details:
- Amount: ${transaction.amount}
- Purpose: {transaction.get_payment_purpose_display()}
- PayPal Email: {transaction.payee_email}
- Transaction ID: {transaction.reference_id}
- Date: {transaction.completed_at.strftime('%Y-%m-%d %H:%M')}

The payment should appear in your PayPal account within a few minutes.

Keep up the great work on Trendy!

Best regards,
Trendy App Team
            """,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[transaction.user.email],
            fail_silently=True,
        )
