from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Notification
from blog.models import Post
from django.utils import timezone
import random

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample notifications for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user',
            type=str,
            help='Username to create notifications for (default: all users)',
        )
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of notifications to create per user (default: 10)',
        )

    def handle(self, *args, **options):
        username = options.get('user')
        count = options.get('count', 10)

        if username:
            try:
                users = [User.objects.get(username=username)]
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'User "{username}" does not exist')
                )
                return
        else:
            users = User.objects.all()

        if not users:
            self.stdout.write(
                self.style.ERROR('No users found')
            )
            return

        # Get some posts for reference
        posts = list(Post.objects.all()[:5])
        
        # Sample notification data
        notification_templates = [
            {
                'type': 'like',
                'title': 'New Like',
                'message_template': '{sender} liked your post "{post_title}"'
            },
            {
                'type': 'comment',
                'title': 'New Comment',
                'message_template': '{sender} commented on your post "{post_title}"'
            },
            {
                'type': 'follow',
                'title': 'New Follower',
                'message_template': '{sender} started following you'
            },
            {
                'type': 'mention',
                'title': 'You were mentioned',
                'message_template': '{sender} mentioned you in a comment'
            },
            {
                'type': 'system',
                'title': 'Welcome to Trendy!',
                'message_template': 'Welcome to Trendy! Complete your profile to get started.'
            },
            {
                'type': 'system',
                'title': 'Profile Update',
                'message_template': 'Your profile has been successfully updated.'
            },
            {
                'type': 'system',
                'title': 'Security Alert',
                'message_template': 'Your password was recently changed. If this wasn\'t you, please contact support.'
            },
            {
                'type': 'post',
                'title': 'New Post Published',
                'message_template': 'Your post "{post_title}" has been published successfully!'
            }
        ]

        sample_senders = ['John Doe', 'Jane Smith', 'Mike Johnson', 'Sarah Wilson', 'David Brown']

        total_created = 0

        for user in users:
            self.stdout.write(f'Creating notifications for user: {user.username}')
            
            for i in range(count):
                template = random.choice(notification_templates)
                
                # Create notification data
                notification_data = {
                    'recipient': user,
                    'notification_type': template['type'],
                    'title': template['title'],
                    'is_read': random.choice([True, False]),  # Random read status
                }

                # Handle sender for non-system notifications
                if template['type'] != 'system':
                    # Try to get a different user as sender
                    potential_senders = User.objects.exclude(id=user.id)
                    if potential_senders.exists():
                        sender = random.choice(potential_senders)
                        notification_data['sender'] = sender
                        sender_name = sender.get_full_name() or sender.username
                    else:
                        sender_name = random.choice(sample_senders)
                else:
                    sender_name = None

                # Handle post reference for post-related notifications
                if template['type'] in ['like', 'comment', 'post'] and posts:
                    post = random.choice(posts)
                    notification_data['post_id'] = post.id
                    post_title = post.title
                else:
                    post_title = "Sample Post Title"

                # Generate message
                if sender_name and '{sender}' in template['message_template']:
                    message = template['message_template'].format(
                        sender=sender_name,
                        post_title=post_title
                    )
                else:
                    message = template['message_template'].replace('{post_title}', post_title)

                notification_data['message'] = message

                # Create the notification
                notification = Notification.objects.create(**notification_data)
                
                # Randomly set some as read with read_at timestamp
                if notification.is_read:
                    notification.read_at = timezone.now() - timezone.timedelta(
                        hours=random.randint(1, 48)
                    )
                    notification.save(update_fields=['read_at'])

                total_created += 1

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {total_created} sample notifications'
            )
        )
