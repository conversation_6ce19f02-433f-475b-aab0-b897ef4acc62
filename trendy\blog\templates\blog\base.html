<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Trendy Blog{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4f46e5;
            --secondary-color: #6366f1;
            --accent-color: #818cf8;
            --text-color: #1e293b;
            --light-bg: #f8fafc;
            --border-color: #e2e8f0;
            --gradient: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            line-height: 1.7;
            scroll-behavior: smooth;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .navbar-brand {
            font-weight: 800;
            font-size: 1.75rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            letter-spacing: -0.5px;
            text-decoration: none;
        }

        .navbar-brand span {
            background: var(--gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .logo-container {
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .logo-container i {
            color: var(--primary-color);
            font-size: 20px;
        }
        
        .nav-link {
            color: var(--text-color) !important;
            font-weight: 500;
            transition: all 0.2s ease;
            padding: 0.5rem 1rem;
            border-radius: 0.75rem;
            position: relative;
        }
        
        .nav-link:hover {
            color: var(--primary-color) !important;
            background: rgba(79, 70, 229, 0.05);
        }
        
        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 3px;
            background: var(--gradient);
            border-radius: 2px;
        }
        
        .search-form {
            position: relative;
            max-width: 400px;
        }
        
        .search-form input {
            padding: 0.75rem 1.5rem;
            border-radius: 1.5rem;
            border: 1px solid var(--border-color);
            background: var(--light-bg);
            transition: all 0.3s ease;
        }
        
        .search-form input:focus {
            background: white;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
        }
        
        .search-form button {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--primary-color);
            opacity: 0.7;
            transition: opacity 0.3s;
        }
        
        .card {
            border: none;
            border-radius: 1.25rem;
            background: white;
            box-shadow: 0 8px 24px rgba(0,0,0,0.04);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s ease;
            overflow: hidden;
            position: relative;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }
        
        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 32px rgba(0,0,0,0.08);
        }
        
        .card:hover::before {
            opacity: 0.03;
        }
        
        .card-img-top {
            height: 240px;
            object-fit: cover;
            border-radius: 1rem 1rem 0 0;
        }
        
        .badge {
            padding: 0.5rem 1rem;
            border-radius: 0.75rem;
            font-weight: 600;
            background: var(--light-bg);
            color: var(--primary-color);
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: var(--gradient);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(79, 70, 229, 0.2);
        }
        
        .footer {
            background: var(--text-color);
            color: white;
            padding: 4rem 0 2rem;
            margin-top: 6rem;
            clip-path: polygon(0 5%, 100% 0, 100% 100%, 0% 100%);
        }
        
        .footer a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .footer a:hover {
            color: white;
            transform: translateX(4px);
        }
        
        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .social-links a {
            font-size: 1.25rem;
            padding: 0.75rem;
            background: rgba(255,255,255,0.1);
            border-radius: 0.75rem;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .newsletter-form input {
            border-radius: 0.75rem;
            border: none;
            padding: 0.875rem 1.5rem;
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .newsletter-form button {
            border-radius: 0.75rem;
            padding: 0.875rem 2rem;
        }
        
        @media (max-width: 768px) {
            .navbar {
                padding: 0.5rem 0;
            }
            
            .navbar-brand {
                font-size: 1.5rem;
            }
            
            .card-img-top {
                height: 200px;
            }
            
            .footer {
                clip-path: polygon(0 3%, 100% 0, 100% 100%, 0% 100%);
                padding: 3rem 0 2rem;
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease forwards;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <div class="logo-icon me-2">
                    <div class="logo-container">
                        <i class="fas fa-trending-up"></i>
                    </div>
                </div>
                <span class="fw-bold">Trendy</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="{% url 'home' %}">Home</a>
                    </li>
                    {% for category in categories %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}?category={{ category.slug }}">{{ category.name }}</a>
                    </li>
                    {% endfor %}
                </ul>
                <form class="search-form me-3" action="{% url 'home' %}" method="get">
                    <input type="text" name="q" class="form-control" placeholder="Search articles..." value="{{ query }}">
                    <button type="submit"><i class="fas fa-search"></i></button>
                </form>
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="avatar-sm me-2">
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 36px; height: 36px;">
                                    {{ user.username|first|upper }}
                                </div>
                            </div>
                            {{ user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            {% if user.is_staff %}
                            <li><a class="dropdown-item" href="{% url 'post-create' %}"><i class="fas fa-plus-circle me-2"></i>New Post</a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{% url 'profile' %}"><i class="fas fa-user-cog me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="{% url 'settings' %}"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><a class="dropdown-item" href="{% url 'notifications' %}"><i class="fas fa-bell me-2"></i>Notifications</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-bookmark me-2"></i>Saved Posts</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="{% url 'logout' %}"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="btn btn-outline-primary me-2" href="{% url 'login' %}">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-primary" href="{% url 'user-register' %}">Join Free</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <main class="container mt-5 animate-fade-in">
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show shadow-sm">
                <i class="fas fa-info-circle me-2"></i>{{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% block content %}
        {% endblock %}
    </main>

    <footer class="footer">
        <div class="container">
            <div class="row g-5">
                <div class="col-lg-4">
                    <h5 class="mb-4 text-white">Trendy Blog</h5>
                    <p class="opacity-75">Cutting-edge insights and innovations. Stay ahead with our expert-curated content.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-6">
                    <h5 class="mb-4">Explore</h5>
                    <ul class="list-unstyled">
                        <li class="mb-3"><a href="{% url 'home' %}">Home</a></li>
                        {% for category in categories %}
                        <li class="mb-3"><a href="{% url 'home' %}?category={{ category.slug }}">{{ category.name }}</a></li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="col-lg-2 col-6">
                    <h5 class="mb-4">Company</h5>
                    <ul class="list-unstyled">
                        <li class="mb-3"><a href="{% url 'about' %}">About</a></li>
                        <li class="mb-3"><a href="{% url 'support' %}">Support</a></li>
                        <li class="mb-3"><a href="{% url 'contact' %}">Contact</a></li>
                        <li class="mb-3"><a href="#">Careers</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h5 class="mb-4">Stay Updated</h5>
                    <form action="{% url 'newsletter-subscribe' %}" method="post" class="newsletter-form">
                        {% csrf_token %}
                        <div class="mb-3">
                            <input type="email" name="email" class="form-control" placeholder="Your email address" required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane me-2"></i>Subscribe
                        </button>
                    </form>
                </div>
            </div>
            <hr class="opacity-25 my-5">
            <div class="text-center opacity-75">
                <small>© {% now "Y" %} Trendy Blog. All rights reserved. <a href="{% url 'privacy-policy' %}" class="text-white">Privacy Policy</a> • <a href="{% url 'terms-of-service' %}" class="text-white">Terms of Service</a></small>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add smooth scroll behavior
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add intersection observer for fade-in animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        });

        document.querySelectorAll('.card, .animate-on-scroll').forEach((el) => {
            observer.observe(el);
        });
    </script>
</body>
</html>