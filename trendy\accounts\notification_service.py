"""
Notification Service for Trendy App
Handles in-app notifications and email notifications
"""

from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import Notification
from .email_service import EmailService
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class NotificationService:
    """Centralized notification service"""
    
    @staticmethod
    def create_notification(recipient, notification_type, title, message, sender=None, metadata=None):
        """Create an in-app notification"""
        try:
            notification = Notification.objects.create(
                recipient=recipient,
                sender=sender,
                notification_type=notification_type,
                title=title,
                message=message,
                metadata=metadata or {}
            )
            
            logger.info(f"Notification created for {recipient.username}: {title}")
            return notification
            
        except Exception as e:
            logger.error(f"Failed to create notification for {recipient.username}: {str(e)}")
            return None
    
    @staticmethod
    def send_welcome_notification(user):
        """Send welcome notification to new users"""
        try:
            NotificationService.create_notification(
                recipient=user,
                notification_type='system',
                title='🎉 Welcome to Trendy!',
                message=f'Hi {user.get_full_name() or user.username}! Welcome to the Trendy community. Start exploring and earning points!',
                metadata={
                    'action_type': 'welcome',
                    'action_url': '/dashboard'
                }
            )
            return True
        except Exception as e:
            logger.error(f"Failed to send welcome notification to {user.username}: {str(e)}")
            return False
    
    @staticmethod
    def send_email_verification_reminder(user):
        """Send reminder notification for email verification"""
        try:
            if user.is_email_verified:
                return False
                
            NotificationService.create_notification(
                recipient=user,
                notification_type='system',
                title='📧 Verify Your Email',
                message='Please verify your email address to unlock all features and start earning rewards.',
                metadata={
                    'action_type': 'verify_email',
                    'action_url': '/verify-email'
                }
            )
            return True
        except Exception as e:
            logger.error(f"Failed to send email verification reminder to {user.username}: {str(e)}")
            return False
    
    @staticmethod
    def send_paypal_setup_notification(user):
        """Send notification to set up PayPal for rewards"""
        try:
            NotificationService.create_notification(
                recipient=user,
                notification_type='system',
                title='💰 Set Up PayPal for Rewards',
                message='Add your PayPal email to start claiming cash rewards! Earn money for your activities.',
                metadata={
                    'action_type': 'setup_paypal',
                    'action_url': '/rewards/paypal-setup'
                }
            )
            return True
        except Exception as e:
            logger.error(f"Failed to send PayPal setup notification to {user.username}: {str(e)}")
            return False
    
    @staticmethod
    def send_reward_notification(user, reward_type, amount, status, metadata=None):
        """Send reward-related notifications"""
        try:
            if status == 'claimed':
                title = f'🎯 Reward Claimed - ${amount}'
                message = f'Your ${amount} {reward_type} reward claim has been submitted for review.'
                action_url = '/rewards/history'
            elif status == 'approved':
                title = f'✅ Reward Approved - ${amount}'
                message = f'Great news! Your ${amount} reward has been approved. Payment will be processed within 24 hours.'
                action_url = '/rewards/history'
            elif status == 'paid':
                title = f'💰 Payment Sent - ${amount}'
                message = f'Your ${amount} payment has been sent to your PayPal account!'
                action_url = '/rewards/history'
            elif status == 'rejected':
                title = f'❌ Reward Rejected - ${amount}'
                message = f'Your ${amount} reward claim was rejected. Please check the requirements and try again.'
                action_url = '/rewards'
            else:
                return False
            
            # Create in-app notification
            NotificationService.create_notification(
                recipient=user,
                notification_type='reward',
                title=title,
                message=message,
                metadata={
                    'action_type': 'reward_update',
                    'action_url': action_url,
                    'reward_type': reward_type,
                    'amount': str(amount),
                    'status': status,
                    **(metadata or {})
                }
            )
            
            # Send email notification if user has email notifications enabled
            if user.receive_email_notifications:
                EmailService.send_reward_notification(user, reward_type, amount, status)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send reward notification to {user.username}: {str(e)}")
            return False
    
    @staticmethod
    def send_level_up_notification(user, new_level, points_earned=0):
        """Send level up notification"""
        try:
            NotificationService.create_notification(
                recipient=user,
                notification_type='achievement',
                title=f'🏆 Level Up! You\'re now Level {new_level}',
                message=f'Congratulations! You\'ve reached Level {new_level} and earned {points_earned} bonus points!',
                metadata={
                    'action_type': 'level_up',
                    'action_url': '/profile',
                    'new_level': new_level,
                    'points_earned': points_earned
                }
            )
            return True
        except Exception as e:
            logger.error(f"Failed to send level up notification to {user.username}: {str(e)}")
            return False
    
    @staticmethod
    def send_premium_notification(user, status, plan=None):
        """Send premium subscription notifications"""
        try:
            if status == 'activated':
                title = '👑 Premium Activated!'
                message = f'Welcome to Premium! You\'re now earning 2x points and have access to exclusive features.'
                action_url = '/store'
            elif status == 'expired':
                title = '⏰ Premium Expired'
                message = 'Your Premium subscription has expired. Renew now to continue earning 2x points!'
                action_url = '/store'
            elif status == 'expiring_soon':
                title = '⚠️ Premium Expiring Soon'
                message = 'Your Premium subscription expires in 3 days. Renew now to avoid interruption.'
                action_url = '/store'
            else:
                return False
            
            NotificationService.create_notification(
                recipient=user,
                notification_type='subscription',
                title=title,
                message=message,
                metadata={
                    'action_type': 'premium_update',
                    'action_url': action_url,
                    'status': status,
                    'plan': plan
                }
            )
            return True
        except Exception as e:
            logger.error(f"Failed to send premium notification to {user.username}: {str(e)}")
            return False
    
    @staticmethod
    def send_social_notification(recipient, sender, notification_type, post=None):
        """Send social interaction notifications"""
        try:
            if notification_type == 'follow':
                title = f'👥 {sender.get_full_name() or sender.username} followed you'
                message = f'{sender.get_full_name() or sender.username} started following you!'
                action_url = f'/profile/{sender.username}'
            elif notification_type == 'like' and post:
                title = f'❤️ {sender.get_full_name() or sender.username} liked your post'
                message = f'{sender.get_full_name() or sender.username} liked your post: "{post.title[:50]}..."'
                action_url = f'/posts/{post.id}'
            elif notification_type == 'comment' and post:
                title = f'💬 {sender.get_full_name() or sender.username} commented on your post'
                message = f'{sender.get_full_name() or sender.username} commented on your post: "{post.title[:50]}..."'
                action_url = f'/posts/{post.id}'
            else:
                return False
            
            NotificationService.create_notification(
                recipient=recipient,
                sender=sender,
                notification_type=notification_type,
                title=title,
                message=message,
                metadata={
                    'action_type': notification_type,
                    'action_url': action_url,
                    'post_id': post.id if post else None
                }
            )
            return True
        except Exception as e:
            logger.error(f"Failed to send social notification to {recipient.username}: {str(e)}")
            return False
    
    @staticmethod
    def send_points_earned_notification(user, points, activity_type):
        """Send points earned notification"""
        try:
            activity_names = {
                'post_like': 'post like',
                'comment': 'comment',
                'post_create': 'creating a post',
                'daily_login': 'daily login',
                'profile_complete': 'completing profile',
                'referral': 'referral bonus'
            }
            
            activity_name = activity_names.get(activity_type, activity_type)
            
            NotificationService.create_notification(
                recipient=user,
                notification_type='points',
                title=f'⭐ +{points} Points Earned!',
                message=f'You earned {points} points for {activity_name}. Keep it up!',
                metadata={
                    'action_type': 'points_earned',
                    'action_url': '/profile',
                    'points': points,
                    'activity_type': activity_type
                }
            )
            return True
        except Exception as e:
            logger.error(f"Failed to send points notification to {user.username}: {str(e)}")
            return False
    
    @staticmethod
    def mark_notifications_as_read(user, notification_ids=None):
        """Mark notifications as read"""
        try:
            queryset = Notification.objects.filter(recipient=user, is_read=False)
            
            if notification_ids:
                queryset = queryset.filter(id__in=notification_ids)
            
            count = queryset.update(
                is_read=True,
                read_at=timezone.now()
            )
            
            logger.info(f"Marked {count} notifications as read for {user.username}")
            return count
            
        except Exception as e:
            logger.error(f"Failed to mark notifications as read for {user.username}: {str(e)}")
            return 0
    
    @staticmethod
    def get_unread_count(user):
        """Get unread notification count for user"""
        try:
            return Notification.objects.filter(recipient=user, is_read=False).count()
        except Exception as e:
            logger.error(f"Failed to get unread count for {user.username}: {str(e)}")
            return 0
    
    @staticmethod
    def cleanup_old_notifications(days=30):
        """Clean up old read notifications"""
        try:
            cutoff_date = timezone.now() - timezone.timedelta(days=days)
            count = Notification.objects.filter(
                is_read=True,
                read_at__lt=cutoff_date
            ).delete()[0]
            
            logger.info(f"Cleaned up {count} old notifications")
            return count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old notifications: {str(e)}")
            return 0
