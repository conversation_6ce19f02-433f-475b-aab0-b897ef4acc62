#!/usr/bin/env python3
"""
Production Deployment Script for Trendy Blog Platform
Automates the deployment process with comprehensive checks and optimizations.
"""

import os
import sys
import subprocess
import django
from datetime import datetime

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')
django.setup()

class TrendyDeployer:
    def __init__(self):
        self.deployment_time = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.log_file = f'deployment_{self.deployment_time}.log'
        
    def log(self, message, level='INFO'):
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {level}: {message}"
        print(log_message)
        
        # Write to log file
        with open(self.log_file, 'a') as f:
            f.write(log_message + '\n')
    
    def run_command(self, command, description):
        """Run a shell command and log the result"""
        self.log(f"Running: {description}")
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                self.log(f"✅ {description} completed successfully")
                return True
            else:
                self.log(f"❌ {description} failed: {result.stderr}", 'ERROR')
                return False
        except Exception as e:
            self.log(f"❌ {description} failed with exception: {str(e)}", 'ERROR')
            return False
    
    def check_environment(self):
        """Check if the environment is ready for deployment"""
        self.log("🔍 Checking deployment environment...")
        
        # Check if DEBUG is False
        from django.conf import settings
        if settings.DEBUG:
            self.log("❌ DEBUG is still True. Set DEBUG=False for production", 'ERROR')
            return False
        
        # Check if SECRET_KEY is set and secure
        if not settings.SECRET_KEY or len(settings.SECRET_KEY) < 50:
            self.log("❌ SECRET_KEY is not set or too short", 'ERROR')
            return False
        
        # Check database configuration
        db_engine = settings.DATABASES['default']['ENGINE']
        if 'sqlite' in db_engine:
            self.log("⚠️  Using SQLite database. Consider PostgreSQL for production", 'WARNING')
        
        self.log("✅ Environment checks passed")
        return True
    
    def run_tests(self):
        """Run comprehensive test suite"""
        self.log("🧪 Running comprehensive test suite...")
        
        # Run Django tests
        if not self.run_command('python manage.py test --verbosity=2', 'Django tests'):
            return False
        
        # Run health check
        if not self.run_command('python scripts/health_check.py', 'API health check'):
            return False
        
        self.log("✅ All tests passed")
        return True
    
    def collect_static_files(self):
        """Collect static files for production"""
        self.log("📦 Collecting static files...")
        return self.run_command('python manage.py collectstatic --noinput', 'Static files collection')
    
    def run_migrations(self):
        """Apply database migrations"""
        self.log("🗄️  Applying database migrations...")
        return self.run_command('python manage.py migrate', 'Database migrations')
    
    def create_sample_data(self):
        """Create sample data if database is empty"""
        self.log("📝 Checking for sample data...")
        
        from blog.models import Post, Category
        from gamification.models import Badge, Challenge
        
        if Post.objects.count() == 0:
            self.log("Creating sample blog data...")
            if not self.run_command('python manage.py loaddata sample_data.json', 'Sample data creation'):
                # Create basic data manually if fixture doesn't exist
                self.create_basic_data()
        
        if Badge.objects.count() == 0:
            self.log("Creating gamification data...")
            self.create_gamification_data()
        
        self.log("✅ Sample data ready")
        return True
    
    def create_basic_data(self):
        """Create basic blog data"""
        from blog.models import Category, Post
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Create admin user if doesn't exist
        if not User.objects.filter(is_superuser=True).exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
            self.log("Created admin user (admin/admin123)")
        
        # Create categories
        tech_cat, _ = Category.objects.get_or_create(
            name='Technology',
            defaults={'description': 'Technology and programming posts'}
        )
        
        lifestyle_cat, _ = Category.objects.get_or_create(
            name='Lifestyle',
            defaults={'description': 'Lifestyle and personal development'}
        )
        
        # Create sample posts
        admin_user = User.objects.filter(is_superuser=True).first()
        
        if not Post.objects.exists():
            Post.objects.create(
                title='Welcome to Trendy Blog Platform',
                content='''
                Welcome to the most advanced blog platform ever created! 
                
                Trendy combines traditional blogging with cutting-edge features:
                - Smart reading analytics
                - Interactive content blocks
                - Voice comments and AI assistance
                - Gamification and achievements
                - Advanced social features
                
                Start exploring and discover the future of content engagement!
                ''',
                author=admin_user,
                category=tech_cat,
                status='published'
            )
            
            Post.objects.create(
                title='Getting Started with Interactive Content',
                content='''
                Learn how to create engaging interactive content with our platform.
                
                Features include:
                - Interactive polls and quizzes
                - Real-time voting
                - Voice comments
                - Gamification elements
                
                Join the revolution in content creation!
                ''',
                author=admin_user,
                category=tech_cat,
                status='published'
            )
    
    def create_gamification_data(self):
        """Create gamification badges and challenges"""
        from gamification.models import Badge, Challenge
        from django.utils import timezone
        from datetime import timedelta
        
        # Create badges
        badges_data = [
            {
                'name': 'First Steps',
                'description': 'Welcome to Trendy! You\'ve taken your first step.',
                'badge_type': 'milestone',
                'rarity': 'common',
                'requirements': {'user_registered': True},
                'points_reward': 10,
                'icon': '🎯'
            },
            {
                'name': 'Bookworm',
                'description': 'Read 10 posts to earn this badge.',
                'badge_type': 'reading',
                'rarity': 'common',
                'requirements': {'min_posts_read': 10},
                'points_reward': 25,
                'icon': '📚'
            },
            {
                'name': 'Content Creator',
                'description': 'Publish your first post.',
                'badge_type': 'writing',
                'rarity': 'uncommon',
                'requirements': {'min_posts_written': 1},
                'points_reward': 50,
                'icon': '✍️'
            }
        ]
        
        for badge_data in badges_data:
            Badge.objects.get_or_create(
                name=badge_data['name'],
                defaults=badge_data
            )
        
        # Create challenges
        challenges_data = [
            {
                'title': '7-Day Reading Challenge',
                'description': 'Read at least one post every day for 7 days.',
                'challenge_type': 'reading',
                'difficulty': 'easy',
                'requirements': {'daily_reading_streak': 7},
                'points_reward': 100,
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=30),
                'duration_days': 7,
                'max_participants': 1000
            },
            {
                'title': 'Community Engagement',
                'description': 'Leave 20 comments on different posts.',
                'challenge_type': 'engagement',
                'difficulty': 'medium',
                'requirements': {'comments_count': 20},
                'points_reward': 150,
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=30),
                'duration_days': 14,
                'max_participants': 500
            }
        ]
        
        for challenge_data in challenges_data:
            Challenge.objects.get_or_create(
                title=challenge_data['title'],
                defaults=challenge_data
            )
    
    def optimize_database(self):
        """Optimize database for production"""
        self.log("🗄️  Optimizing database...")
        
        # Create database indexes if using PostgreSQL
        from django.conf import settings
        db_engine = settings.DATABASES['default']['ENGINE']
        
        if 'postgresql' in db_engine:
            self.log("Creating database indexes for PostgreSQL...")
            # Add any custom database optimizations here
        
        self.log("✅ Database optimization completed")
        return True
    
    def deploy(self):
        """Run the complete deployment process"""
        self.log("🚀 Starting Trendy Blog Platform deployment...")
        self.log("=" * 60)
        
        start_time = datetime.now()
        
        # Deployment steps
        steps = [
            ("Environment Check", self.check_environment),
            ("Database Migrations", self.run_migrations),
            ("Static Files Collection", self.collect_static_files),
            ("Sample Data Creation", self.create_sample_data),
            ("Database Optimization", self.optimize_database),
            ("Test Suite", self.run_tests),
        ]
        
        failed_steps = []
        
        for step_name, step_function in steps:
            self.log(f"📋 Step: {step_name}")
            if not step_function():
                failed_steps.append(step_name)
                self.log(f"❌ Step failed: {step_name}", 'ERROR')
            else:
                self.log(f"✅ Step completed: {step_name}")
            self.log("-" * 40)
        
        end_time = datetime.now()
        deployment_duration = end_time - start_time
        
        # Deployment summary
        self.log("=" * 60)
        self.log("🏁 Deployment Summary")
        self.log(f"Total Steps: {len(steps)}")
        self.log(f"Successful: {len(steps) - len(failed_steps)}")
        self.log(f"Failed: {len(failed_steps)}")
        self.log(f"Duration: {deployment_duration}")
        self.log(f"Log File: {self.log_file}")
        
        if failed_steps:
            self.log(f"❌ Deployment completed with errors in: {', '.join(failed_steps)}", 'ERROR')
            self.log("Please check the logs and fix the issues before proceeding to production.")
            return False
        else:
            self.log("✅ Deployment completed successfully!")
            self.log("🎉 Trendy Blog Platform is ready for production!")
            self.log("")
            self.log("Next steps:")
            self.log("1. Configure your web server (Nginx/Apache)")
            self.log("2. Set up SSL certificates")
            self.log("3. Configure monitoring and logging")
            self.log("4. Set up backup procedures")
            self.log("5. Configure CDN for static files")
            return True

if __name__ == '__main__':
    deployer = TrendyDeployer()
    success = deployer.deploy()
    sys.exit(0 if success else 1)
