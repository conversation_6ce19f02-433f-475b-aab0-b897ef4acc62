from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from gamification.services import GamificationService
from gamification.models import PointConversionSettings, UserStorePoints, PointConversionTransaction

User = get_user_model()


class Command(BaseCommand):
    help = 'Test point conversion system'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Username to test with')
        parser.add_argument('--gamification-points', type=int, help='Gamification points to convert')

    def handle(self, *args, **options):
        username = options.get('username', 'testuser')
        gamification_points = options.get('gamification_points', 100)

        # Get or create test user
        user, created = User.objects.get_or_create(
            username=username,
            defaults={'email': f'{username}@test.com'}
        )
        if created:
            self.stdout.write(f"Created test user: {username}")
        else:
            self.stdout.write(f"Using existing user: {username}")

        # Ensure user has enough gamification points for testing
        user_level = GamificationService.get_or_create_user_level(user)
        if user_level.total_points < 1000:
            GamificationService.award_points(
                user=user,
                points=1000,
                transaction_type='bonus',
                description='Test points for conversion testing'
            )
            self.stdout.write("Added 1000 test points to user")

        # Ensure conversion settings exist
        settings = PointConversionSettings.get_settings()
        self.stdout.write(f"Conversion settings loaded: enabled = {settings.conversion_enabled}")

        self.stdout.write("\n" + "="*60)
        self.stdout.write("POINT CONVERSION SYSTEM TEST")
        self.stdout.write("="*60)

        # Show current balances
        user_level.refresh_from_db()
        store_balance = GamificationService.get_user_store_points_balance(user)
        
        self.stdout.write(f"\n📊 CURRENT BALANCES:")
        self.stdout.write(f"   Gamification Points: {user_level.total_points}")
        self.stdout.write(f"   Store Points: {store_balance}")
        self.stdout.write(f"   User Level: {user_level.current_level}")

        # Show conversion settings
        user_rate = settings.calculate_conversion_rate(user)
        self.stdout.write(f"\n⚙️  CONVERSION SETTINGS:")
        self.stdout.write(f"   Base Rate: {settings.base_conversion_rate} gamification points = 1 store point")
        self.stdout.write(f"   Your Rate: {user_rate} gamification points = 1 store point")
        self.stdout.write(f"   Conversion Fee: {settings.conversion_fee_percentage*100}% + {settings.conversion_fee_fixed} points")
        self.stdout.write(f"   Daily Limit: {settings.daily_conversion_limit} store points")
        self.stdout.write(f"   Min/Max: {settings.minimum_conversion_amount}-{settings.maximum_conversion_amount} gamification points")

        # Test 1: Preview conversion
        self.stdout.write(f"\n🔍 TESTING CONVERSION PREVIEW ({gamification_points} points):")
        success, preview = GamificationService.get_conversion_preview(user, gamification_points)
        
        if success:
            details = preview['conversion_details']
            self.stdout.write(f"   ✅ Preview successful:")
            self.stdout.write(f"      Store Points to Receive: {details['store_points']}")
            self.stdout.write(f"      Total Cost: {details['total_cost']} gamification points")
            self.stdout.write(f"      Conversion Rate: {details['conversion_rate']}")
            self.stdout.write(f"      Fees: {details['percentage_fee']} + {details['fixed_fee']} = {details['percentage_fee'] + details['fixed_fee']} points")
            self.stdout.write(f"      Can Afford: {preview['can_afford']}")
            self.stdout.write(f"      Can Convert Today: {preview['can_convert_today']}")
        else:
            self.stdout.write(f"   ❌ Preview failed: {preview}")

        # Test 2: Actual conversion
        if success and preview['can_afford'] and preview['can_convert_today']:
            self.stdout.write(f"\n💱 PERFORMING ACTUAL CONVERSION:")
            success, result = GamificationService.convert_gamification_to_store_points(user, gamification_points)
            
            if success:
                self.stdout.write(f"   ✅ Conversion successful!")
                self.stdout.write(f"      {result['message']}")
                self.stdout.write(f"      Store Points Received: {result['store_points_received']}")
                self.stdout.write(f"      Gamification Points Spent: {result['gamification_points_spent']}")
                self.stdout.write(f"      Fees Paid: {result['fees_paid']}")
                self.stdout.write(f"      New Store Balance: {result['new_store_balance']}")
                self.stdout.write(f"      Transaction ID: {result['transaction_id']}")
            else:
                self.stdout.write(f"   ❌ Conversion failed: {result}")
        else:
            self.stdout.write(f"\n⚠️  SKIPPING ACTUAL CONVERSION (preview failed or insufficient funds)")

        # Test 3: Try converting again (should hit daily limit or duplicate prevention)
        self.stdout.write(f"\n🔄 TESTING SECOND CONVERSION (should show limits):")
        success, result = GamificationService.convert_gamification_to_store_points(user, gamification_points)
        
        if success:
            self.stdout.write(f"   ✅ Second conversion successful: {result['message']}")
        else:
            self.stdout.write(f"   ❌ Second conversion blocked: {result}")

        # Test 4: Try converting too small amount
        self.stdout.write(f"\n🔍 TESTING MINIMUM AMOUNT VALIDATION:")
        success, result = GamificationService.convert_gamification_to_store_points(user, 10)  # Below minimum
        
        if success:
            self.stdout.write(f"   ⚠️  Small conversion unexpectedly succeeded: {result}")
        else:
            self.stdout.write(f"   ✅ Small conversion correctly blocked: {result}")

        # Test 5: Try converting too large amount
        self.stdout.write(f"\n🔍 TESTING MAXIMUM AMOUNT VALIDATION:")
        success, result = GamificationService.convert_gamification_to_store_points(user, 10000)  # Above maximum
        
        if success:
            self.stdout.write(f"   ⚠️  Large conversion unexpectedly succeeded: {result}")
        else:
            self.stdout.write(f"   ✅ Large conversion correctly blocked: {result}")

        # Show final balances and statistics
        user_level.refresh_from_db()
        store_balance = GamificationService.get_user_store_points_balance(user)
        
        self.stdout.write(f"\n📊 FINAL BALANCES:")
        self.stdout.write(f"   Gamification Points: {user_level.total_points}")
        self.stdout.write(f"   Store Points: {store_balance}")

        # Show conversion history
        transactions = PointConversionTransaction.objects.filter(user=user).order_by('-created_at')[:5]
        self.stdout.write(f"\n📜 RECENT CONVERSION HISTORY ({transactions.count()} transactions):")
        
        for transaction in transactions:
            status_emoji = "✅" if transaction.status == 'completed' else "❌" if transaction.status == 'failed' else "⏳"
            self.stdout.write(f"   {status_emoji} {transaction.gamification_points_spent}→{transaction.store_points_received} "
                            f"(rate: {transaction.conversion_rate}, fee: {transaction.total_fee}) - {transaction.status}")

        # Show store points statistics
        try:
            user_store_points = UserStorePoints.objects.get(user=user)
            self.stdout.write(f"\n📈 STORE POINTS STATISTICS:")
            self.stdout.write(f"   Total Earned: {user_store_points.total_earned}")
            self.stdout.write(f"   Total Spent: {user_store_points.total_spent}")
            self.stdout.write(f"   Total Converted: {user_store_points.total_converted}")
            self.stdout.write(f"   Daily Conversions Today: {user_store_points.daily_conversions_today}")
        except UserStorePoints.DoesNotExist:
            self.stdout.write(f"\n📈 No store points record found")

        self.stdout.write("\n" + "="*60)
        self.stdout.write("CONVERSION TEST COMPLETED")
        self.stdout.write("="*60)
        
        # Show strategic analysis
        self.stdout.write(f"\n💡 STRATEGIC ANALYSIS:")
        self.stdout.write(f"   Developer Benefits:")
        self.stdout.write(f"   - Base conversion rate: {settings.base_conversion_rate}:1 (users need {settings.base_conversion_rate}x points)")
        self.stdout.write(f"   - Conversion fees: {settings.conversion_fee_percentage*100}% + {settings.conversion_fee_fixed} points")
        self.stdout.write(f"   - Daily limits prevent excessive conversions")
        self.stdout.write(f"   - Level bonuses encourage user progression")
        self.stdout.write(f"   - Premium bonuses encourage subscriptions")
        self.stdout.write(f"\n   User Benefits:")
        self.stdout.write(f"   - Can convert earned points to store currency")
        self.stdout.write(f"   - Better rates for higher levels and premium users")
        self.stdout.write(f"   - Clear preview system shows exact costs")
        self.stdout.write(f"   - Daily limits are generous but prevent abuse")
