from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from blog.models import Post
import re
import textstat

User = get_user_model()

class ReadingSession(models.Model):
    """Track individual reading sessions for analytics"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reading_sessions')
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='reading_sessions')
    start_time = models.DateTimeField(auto_now_add=True)
    end_time = models.DateTimeField(null=True, blank=True)
    progress_percentage = models.FloatField(default=0.0)  # 0-100
    reading_speed_wpm = models.IntegerField(null=True, blank=True)  # Words per minute
    session_duration = models.IntegerField(default=0)  # in seconds
    scroll_depth = models.FloatField(default=0.0)  # Maximum scroll depth reached
    is_completed = models.BooleanField(default=False)
    device_type = models.CharField(max_length=20, default='unknown')  # mobile, tablet, desktop

    class Meta:
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['user', 'post']),
            models.Index(fields=['start_time']),
        ]

    def __str__(self):
        return f"{self.user.username} reading {self.post.title}"

    def calculate_reading_speed(self):
        """Calculate reading speed based on session data"""
        if self.session_duration and self.progress_percentage:
            content_analytics = self.post.content_analytics
            words_read = (content_analytics.word_count * self.progress_percentage) / 100
            minutes_read = self.session_duration / 60
            if minutes_read > 0:
                self.reading_speed_wpm = int(words_read / minutes_read)
                self.save(update_fields=['reading_speed_wpm'])

class ContentAnalytics(models.Model):
    """Store content analysis data for posts"""
    post = models.OneToOneField(Post, on_delete=models.CASCADE, related_name='content_analytics')
    estimated_reading_time = models.IntegerField()  # in seconds
    complexity_score = models.FloatField(default=0.0)  # 0-100 (higher = more complex)
    word_count = models.IntegerField(default=0)
    sentence_count = models.IntegerField(default=0)
    paragraph_count = models.IntegerField(default=0)
    readability_score = models.FloatField(default=0.0)  # Flesch Reading Ease score
    reading_level = models.CharField(max_length=50, default='Unknown')
    average_words_per_sentence = models.FloatField(default=0.0)
    average_syllables_per_word = models.FloatField(default=0.0)

    # Engagement metrics
    total_reading_time = models.IntegerField(default=0)  # Total time spent by all users
    completion_rate = models.FloatField(default=0.0)  # Percentage of users who complete reading
    average_session_duration = models.IntegerField(default=0)  # Average time per session

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Analytics for {self.post.title}"

    @classmethod
    def analyze_content(cls, post):
        """Analyze post content and create/update analytics"""
        content = post.content

        # Basic text analysis
        word_count = len(content.split())
        sentences = re.split(r'[.!?]+', content)
        sentence_count = len([s for s in sentences if s.strip()])
        paragraphs = content.split('\n\n')
        paragraph_count = len([p for p in paragraphs if p.strip()])

        # Calculate averages
        avg_words_per_sentence = word_count / sentence_count if sentence_count > 0 else 0

        # Readability analysis using textstat
        readability_score = textstat.flesch_reading_ease(content)
        reading_level = textstat.flesch_kincaid_grade(content)
        avg_syllables_per_word = textstat.avg_syllables_per_word(content)

        # Estimate reading time (average 200 WPM for adults)
        estimated_reading_time = (word_count / 200) * 60  # in seconds

        # Calculate complexity score (0-100)
        complexity_factors = [
            min(avg_words_per_sentence / 20 * 30, 30),  # Sentence length (max 30 points)
            min(avg_syllables_per_word * 20, 25),       # Word complexity (max 25 points)
            min(reading_level * 5, 25),                 # Grade level (max 25 points)
            max(0, 20 - (readability_score / 5))       # Readability (max 20 points)
        ]
        complexity_score = sum(complexity_factors)

        # Get reading level description
        if readability_score >= 90:
            level_desc = "Very Easy"
        elif readability_score >= 80:
            level_desc = "Easy"
        elif readability_score >= 70:
            level_desc = "Fairly Easy"
        elif readability_score >= 60:
            level_desc = "Standard"
        elif readability_score >= 50:
            level_desc = "Fairly Difficult"
        elif readability_score >= 30:
            level_desc = "Difficult"
        else:
            level_desc = "Very Difficult"

        # Create or update analytics
        analytics, created = cls.objects.get_or_create(
            post=post,
            defaults={
                'estimated_reading_time': int(estimated_reading_time),
                'complexity_score': complexity_score,
                'word_count': word_count,
                'sentence_count': sentence_count,
                'paragraph_count': paragraph_count,
                'readability_score': readability_score,
                'reading_level': level_desc,
                'average_words_per_sentence': avg_words_per_sentence,
                'average_syllables_per_word': avg_syllables_per_word,
            }
        )

        if not created:
            # Update existing analytics
            analytics.estimated_reading_time = int(estimated_reading_time)
            analytics.complexity_score = complexity_score
            analytics.word_count = word_count
            analytics.sentence_count = sentence_count
            analytics.paragraph_count = paragraph_count
            analytics.readability_score = readability_score
            analytics.reading_level = level_desc
            analytics.average_words_per_sentence = avg_words_per_sentence
            analytics.average_syllables_per_word = avg_syllables_per_word
            analytics.save()

        return analytics
