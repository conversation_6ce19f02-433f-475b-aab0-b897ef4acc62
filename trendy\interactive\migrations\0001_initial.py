# Generated by Django 5.2.3 on 2025-06-23 13:28

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('blog', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='InteractiveBlock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('block_type', models.CharField(choices=[('poll', 'Poll'), ('quiz', 'Quiz'), ('code', 'Code Playground'), ('chart', 'Data Visualization'), ('timeline', 'Timeline'), ('comparison', 'Comparison'), ('embed', 'Rich Embed')], max_length=20)),
                ('title', models.Char<PERSON>ield(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('position', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interactive_blocks', to='blog.post')),
            ],
            options={
                'ordering': ['position', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='CodePlayground',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language', models.CharField(choices=[('python', 'Python'), ('javascript', 'JavaScript'), ('html', 'HTML'), ('css', 'CSS'), ('sql', 'SQL'), ('json', 'JSON'), ('markdown', 'Markdown')], default='python', max_length=20)),
                ('initial_code', models.TextField(blank=True)),
                ('expected_output', models.TextField(blank=True)),
                ('instructions', models.TextField(blank=True)),
                ('is_editable', models.BooleanField(default=True)),
                ('show_line_numbers', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('interactive_block', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='code_playground', to='interactive.interactiveblock')),
            ],
        ),
        migrations.CreateModel(
            name='Poll',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.CharField(max_length=500)),
                ('allow_multiple_choices', models.BooleanField(default=False)),
                ('show_results_immediately', models.BooleanField(default=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('is_anonymous', models.BooleanField(default=False)),
                ('total_votes', models.PositiveIntegerField(default=0)),
                ('unique_voters', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('interactive_block', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='poll', to='interactive.interactiveblock')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PollOption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.CharField(max_length=200)),
                ('image_url', models.URLField(blank=True)),
                ('vote_count', models.PositiveIntegerField(default=0)),
                ('position', models.PositiveIntegerField(default=0)),
                ('poll', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='options', to='interactive.poll')),
            ],
            options={
                'ordering': ['position', 'id'],
            },
        ),
        migrations.CreateModel(
            name='PollVote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('voted_at', models.DateTimeField(auto_now_add=True)),
                ('option', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='votes', to='interactive.polloption')),
                ('poll', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='votes', to='interactive.poll')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Quiz',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('instructions', models.TextField(blank=True)),
                ('time_limit', models.PositiveIntegerField(blank=True, null=True)),
                ('show_correct_answers', models.BooleanField(default=True)),
                ('randomize_questions', models.BooleanField(default=False)),
                ('passing_score', models.PositiveIntegerField(default=70, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('total_attempts', models.PositiveIntegerField(default=0)),
                ('average_score', models.FloatField(default=0.0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('interactive_block', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='quiz', to='interactive.interactiveblock')),
            ],
        ),
        migrations.CreateModel(
            name='QuizAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.FloatField(default=0.0)),
                ('total_points', models.PositiveIntegerField(default=0)),
                ('earned_points', models.PositiveIntegerField(default=0)),
                ('time_taken', models.PositiveIntegerField(default=0)),
                ('completed_at', models.DateTimeField(auto_now_add=True)),
                ('answers', models.JSONField(default=dict)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attempts', to='interactive.quiz')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quiz_attempts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-completed_at'],
            },
        ),
        migrations.CreateModel(
            name='QuizQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_type', models.CharField(choices=[('multiple_choice', 'Multiple Choice'), ('true_false', 'True/False'), ('text_input', 'Text Input'), ('number_input', 'Number Input')], default='multiple_choice', max_length=20)),
                ('question_text', models.TextField()),
                ('explanation', models.TextField(blank=True)),
                ('points', models.PositiveIntegerField(default=1)),
                ('position', models.PositiveIntegerField(default=0)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='interactive.quiz')),
            ],
            options={
                'ordering': ['position', 'id'],
            },
        ),
        migrations.CreateModel(
            name='QuizAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('answer_text', models.CharField(max_length=500)),
                ('is_correct', models.BooleanField(default=False)),
                ('position', models.PositiveIntegerField(default=0)),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='answers', to='interactive.quizquestion')),
            ],
            options={
                'ordering': ['position', 'id'],
            },
        ),
        migrations.AddIndex(
            model_name='interactiveblock',
            index=models.Index(fields=['post', 'block_type'], name='interactive_post_id_7f0cea_idx'),
        ),
        migrations.AddIndex(
            model_name='interactiveblock',
            index=models.Index(fields=['post', 'position'], name='interactive_post_id_d08a11_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='polloption',
            unique_together={('poll', 'position')},
        ),
        migrations.AddIndex(
            model_name='pollvote',
            index=models.Index(fields=['poll', 'user'], name='interactive_poll_id_0bcccf_idx'),
        ),
        migrations.AddIndex(
            model_name='pollvote',
            index=models.Index(fields=['poll', 'ip_address'], name='interactive_poll_id_5d9986_idx'),
        ),
        migrations.AddIndex(
            model_name='pollvote',
            index=models.Index(fields=['voted_at'], name='interactive_voted_a_da3a8a_idx'),
        ),
        migrations.AddIndex(
            model_name='quizattempt',
            index=models.Index(fields=['quiz', 'user'], name='interactive_quiz_id_95d6df_idx'),
        ),
        migrations.AddIndex(
            model_name='quizattempt',
            index=models.Index(fields=['completed_at'], name='interactive_complet_b2e660_idx'),
        ),
    ]
