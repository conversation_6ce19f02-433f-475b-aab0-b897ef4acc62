# Generated by Django 5.2.3 on 2025-06-23 13:28

import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MonetizationSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('premium_monthly_price', models.DecimalField(decimal_places=2, default=Decimal('9.99'), max_digits=10)),
                ('premium_point_multiplier', models.DecimalField(decimal_places=1, default=Decimal('2.0'), max_digits=3)),
                ('premium_daily_bonus', models.PositiveIntegerField(default=15)),
                ('engagement_tier_price', models.DecimalField(decimal_places=2, default=Decimal('2.99'), max_digits=10)),
                ('achievement_tier_price', models.DecimalField(decimal_places=2, default=Decimal('4.99'), max_digits=10)),
                ('elite_tier_price', models.DecimalField(decimal_places=2, default=Decimal('9.99'), max_digits=10)),
                ('referral_join_points', models.PositiveIntegerField(default=100)),
                ('referral_level_5_reward', models.DecimalField(decimal_places=2, default=Decimal('2.00'), max_digits=10)),
                ('referral_premium_reward', models.DecimalField(decimal_places=2, default=Decimal('5.00'), max_digits=10)),
                ('monetization_enabled', models.BooleanField(default=True)),
                ('premium_enabled', models.BooleanField(default=True)),
                ('tier_unlocks_enabled', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Monetization Settings',
                'verbose_name_plural': 'Monetization Settings',
            },
        ),
        migrations.CreateModel(
            name='PurchaseTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('premium_subscription', 'Premium Subscription'), ('point_boost', 'Point Boost Package'), ('tier_unlock', 'Reward Tier Unlock'), ('streak_protection', 'Streak Protection'), ('cosmetic_item', 'Cosmetic Item'), ('functional_item', 'Functional Item')], max_length=30)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('item_name', models.CharField(max_length=100)),
                ('item_description', models.TextField(blank=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('payment_method', models.CharField(blank=True, max_length=50)),
                ('payment_transaction_id', models.CharField(blank=True, max_length=100)),
                ('wallet_transaction_id', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchases', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PointBoostPurchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('package', models.CharField(choices=[('quick_start', 'Quick Start - 200+50 points - $1.99'), ('power_boost', 'Power Boost - 600+200 points - $4.99'), ('mega_boost', 'Mega Boost - 1500+500 points - $9.99'), ('ultimate_boost', 'Ultimate Boost - 3500+1500 points - $19.99')], max_length=20)),
                ('base_points', models.PositiveIntegerField()),
                ('bonus_points', models.PositiveIntegerField()),
                ('total_points', models.PositiveIntegerField()),
                ('points_awarded', models.BooleanField(default=False)),
                ('awarded_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='point_boosts', to=settings.AUTH_USER_MODEL)),
                ('purchase_transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='monetization.purchasetransaction')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReferralProgram',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('referral_code', models.CharField(max_length=20, unique=True)),
                ('referee_joined_at', models.DateTimeField(auto_now_add=True)),
                ('referee_reached_level_5', models.BooleanField(default=False)),
                ('referee_went_premium', models.BooleanField(default=False)),
                ('referee_made_purchase', models.BooleanField(default=False)),
                ('join_reward_given', models.BooleanField(default=False)),
                ('level_5_reward_given', models.BooleanField(default=False)),
                ('premium_reward_given', models.BooleanField(default=False)),
                ('total_revenue_generated', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('referee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='referral_source', to=settings.AUTH_USER_MODEL)),
                ('referrer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='referrals_made', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='RewardTierUnlock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tier', models.CharField(choices=[('starter', 'Starter Tier - FREE'), ('engagement', 'Engagement Tier - $2.99'), ('achievement', 'Achievement Tier - $4.99'), ('elite', 'Elite Tier - $9.99')], max_length=20)),
                ('unlock_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('unlocked_at', models.DateTimeField(auto_now_add=True)),
                ('purchase_transaction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='monetization.purchasetransaction')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tier_unlocks', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='StreakProtection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('protection_type', models.CharField(choices=[('shield', 'Streak Shield - 1 day - $0.99'), ('insurance', 'Streak Insurance - 3 days - $2.99'), ('guardian', 'Streak Guardian - 7 days - $4.99')], max_length=20)),
                ('protection_days', models.PositiveIntegerField()),
                ('days_remaining', models.PositiveIntegerField()),
                ('is_active', models.BooleanField(default=True)),
                ('used_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('purchase_transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='monetization.purchasetransaction')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='streak_protections', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VirtualItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('category', models.CharField(choices=[('cosmetic', 'Cosmetic'), ('functional', 'Functional'), ('temporary', 'Temporary Boost')], max_length=20)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('is_active', models.BooleanField(default=True)),
                ('is_limited_time', models.BooleanField(default=False)),
                ('max_purchases_per_user', models.PositiveIntegerField(blank=True, null=True)),
                ('effects', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['category', 'price'],
                'indexes': [models.Index(fields=['category', 'is_active'], name='monetizatio_categor_8501b9_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserVirtualItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('uses_remaining', models.PositiveIntegerField(blank=True, null=True)),
                ('purchased_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('purchase_transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='monetization.purchasetransaction')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='virtual_items', to=settings.AUTH_USER_MODEL)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='monetization.virtualitem')),
            ],
        ),
        migrations.CreateModel(
            name='PremiumSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan', models.CharField(choices=[('monthly', 'Monthly - $9.99'), ('quarterly', 'Quarterly - $24.99'), ('yearly', 'Yearly - $99.99')], default='monthly', max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('cancelled', 'Cancelled'), ('expired', 'Expired'), ('pending', 'Pending Payment'), ('failed', 'Payment Failed')], default='pending', max_length=20)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField()),
                ('last_payment_date', models.DateTimeField(blank=True, null=True)),
                ('next_payment_date', models.DateTimeField(blank=True, null=True)),
                ('monthly_price', models.DecimalField(decimal_places=2, default=Decimal('9.99'), max_digits=10)),
                ('total_paid', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('point_multiplier', models.DecimalField(decimal_places=1, default=Decimal('2.0'), max_digits=3)),
                ('daily_streak_bonus', models.PositiveIntegerField(default=15)),
                ('voice_comments_limit', models.PositiveIntegerField(default=999)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='premium_subscription', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['user', 'status'], name='monetizatio_user_id_2178b2_idx'), models.Index(fields=['status', 'end_date'], name='monetizatio_status_a0d1d6_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='purchasetransaction',
            index=models.Index(fields=['user', 'status'], name='monetizatio_user_id_8fe4a3_idx'),
        ),
        migrations.AddIndex(
            model_name='purchasetransaction',
            index=models.Index(fields=['transaction_type', 'status'], name='monetizatio_transac_91e617_idx'),
        ),
        migrations.AddIndex(
            model_name='purchasetransaction',
            index=models.Index(fields=['created_at'], name='monetizatio_created_d06975_idx'),
        ),
        migrations.AddIndex(
            model_name='pointboostpurchase',
            index=models.Index(fields=['user', 'points_awarded'], name='monetizatio_user_id_dc81e2_idx'),
        ),
        migrations.AddIndex(
            model_name='referralprogram',
            index=models.Index(fields=['referrer'], name='monetizatio_referre_a72229_idx'),
        ),
        migrations.AddIndex(
            model_name='referralprogram',
            index=models.Index(fields=['referral_code'], name='monetizatio_referra_26dc66_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='referralprogram',
            unique_together={('referrer', 'referee')},
        ),
        migrations.AddIndex(
            model_name='rewardtierunlock',
            index=models.Index(fields=['user', 'tier'], name='monetizatio_user_id_b2c6f6_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='rewardtierunlock',
            unique_together={('user', 'tier')},
        ),
        migrations.AddIndex(
            model_name='streakprotection',
            index=models.Index(fields=['user', 'is_active'], name='monetizatio_user_id_bbcf29_idx'),
        ),
        migrations.AddIndex(
            model_name='uservirtualitem',
            index=models.Index(fields=['user', 'is_active'], name='monetizatio_user_id_1d5951_idx'),
        ),
        migrations.AddIndex(
            model_name='uservirtualitem',
            index=models.Index(fields=['item', 'user'], name='monetizatio_item_id_7bc1c2_idx'),
        ),
    ]
