from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timed<PERSON>ta
from gamification.models import Badge, Challenge


class Command(BaseCommand):
    help = 'Set up initial gamification system with balanced rewards'

    def handle(self, *args, **options):
        self.stdout.write('Setting up gamification system...')
        
        # Create badges
        self.create_badges()
        
        # Create challenges
        self.create_challenges()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up gamification system!')
        )

    def create_badges(self):
        """Create a comprehensive badge system"""
        badges = [
            # READING BADGES (Progressive difficulty)
            {
                'name': 'First Reader',
                'description': 'Read your first post',
                'badge_type': 'reading',
                'rarity': 'common',
                'points_reward': 10,
                'requirements': {'min_posts_read': 1},
                'is_secret': False,
            },
            {
                'name': 'Curious Mind',
                'description': 'Read 10 posts',
                'badge_type': 'reading',
                'rarity': 'common',
                'points_reward': 25,
                'requirements': {'min_posts_read': 10},
                'is_secret': False,
            },
            {
                'name': 'Bookworm',
                'description': 'Read 50 posts',
                'badge_type': 'reading',
                'rarity': 'uncommon',
                'points_reward': 100,
                'requirements': {'min_posts_read': 50},
                'is_secret': False,
            },
            {
                'name': 'Knowledge Seeker',
                'description': 'Read 100 posts',
                'badge_type': 'reading',
                'rarity': 'rare',
                'points_reward': 250,
                'requirements': {'min_posts_read': 100},
                'is_secret': False,
            },
            {
                'name': 'Scholar',
                'description': 'Read 250 posts',
                'badge_type': 'reading',
                'rarity': 'epic',
                'points_reward': 500,
                'requirements': {'min_posts_read': 250},
                'is_secret': False,
            },
            {
                'name': 'Master Reader',
                'description': 'Read 500 posts - A true reading champion!',
                'badge_type': 'reading',
                'rarity': 'legendary',
                'points_reward': 1000,
                'requirements': {'min_posts_read': 500},
                'is_secret': False,
            },

            # WRITING BADGES (Harder to achieve)
            {
                'name': 'First Words',
                'description': 'Write your first post',
                'badge_type': 'writing',
                'rarity': 'common',
                'points_reward': 50,
                'requirements': {'min_posts_written': 1},
                'is_secret': False,
            },
            {
                'name': 'Budding Writer',
                'description': 'Write 5 posts',
                'badge_type': 'writing',
                'rarity': 'uncommon',
                'points_reward': 150,
                'requirements': {'min_posts_written': 5},
                'is_secret': False,
            },
            {
                'name': 'Content Creator',
                'description': 'Write 15 posts',
                'badge_type': 'writing',
                'rarity': 'rare',
                'points_reward': 400,
                'requirements': {'min_posts_written': 15},
                'is_secret': False,
            },
            {
                'name': 'Prolific Author',
                'description': 'Write 50 posts',
                'badge_type': 'writing',
                'rarity': 'epic',
                'points_reward': 1000,
                'requirements': {'min_posts_written': 50},
                'is_secret': False,
            },
            {
                'name': 'Master Storyteller',
                'description': 'Write 100 posts - Your words inspire thousands!',
                'badge_type': 'writing',
                'rarity': 'legendary',
                'points_reward': 2500,
                'requirements': {'min_posts_written': 100},
                'is_secret': False,
            },

            # ENGAGEMENT BADGES (Community building)
            {
                'name': 'Friendly Voice',
                'description': 'Make your first comment',
                'badge_type': 'engagement',
                'rarity': 'common',
                'points_reward': 15,
                'requirements': {'min_comments': 1},
                'is_secret': False,
            },
            {
                'name': 'Conversationalist',
                'description': 'Make 25 comments',
                'badge_type': 'engagement',
                'rarity': 'uncommon',
                'points_reward': 75,
                'requirements': {'min_comments': 25},
                'is_secret': False,
            },
            {
                'name': 'Community Builder',
                'description': 'Make 100 comments',
                'badge_type': 'engagement',
                'rarity': 'rare',
                'points_reward': 300,
                'requirements': {'min_comments': 100},
                'is_secret': False,
            },
            {
                'name': 'Discussion Leader',
                'description': 'Make 250 comments',
                'badge_type': 'engagement',
                'rarity': 'epic',
                'points_reward': 750,
                'requirements': {'min_comments': 250},
                'is_secret': False,
            },

            # STREAK BADGES (Consistency rewards)
            {
                'name': 'Daily Reader',
                'description': 'Read posts for 3 days in a row',
                'badge_type': 'reading',
                'rarity': 'common',
                'points_reward': 50,
                'requirements': {'min_reading_streak': 3},
                'is_secret': False,
            },
            {
                'name': 'Dedicated Reader',
                'description': 'Read posts for 7 days in a row',
                'badge_type': 'reading',
                'rarity': 'uncommon',
                'points_reward': 150,
                'requirements': {'min_reading_streak': 7},
                'is_secret': False,
            },
            {
                'name': 'Reading Habit',
                'description': 'Read posts for 30 days in a row',
                'badge_type': 'reading',
                'rarity': 'rare',
                'points_reward': 500,
                'requirements': {'min_reading_streak': 30},
                'is_secret': False,
            },
            {
                'name': 'Reading Addict',
                'description': 'Read posts for 100 days in a row - Incredible dedication!',
                'badge_type': 'reading',
                'rarity': 'legendary',
                'points_reward': 2000,
                'requirements': {'min_reading_streak': 100},
                'is_secret': False,
            },

            # LEVEL BADGES (Achievement milestones)
            {
                'name': 'Rising Star',
                'description': 'Reach level 5',
                'badge_type': 'achievement',
                'rarity': 'uncommon',
                'points_reward': 100,
                'requirements': {'min_level': 5},
                'is_secret': False,
            },
            {
                'name': 'Experienced User',
                'description': 'Reach level 10',
                'badge_type': 'achievement',
                'rarity': 'rare',
                'points_reward': 250,
                'requirements': {'min_level': 10},
                'is_secret': False,
            },
            {
                'name': 'Elite Member',
                'description': 'Reach level 25',
                'badge_type': 'achievement',
                'rarity': 'epic',
                'points_reward': 750,
                'requirements': {'min_level': 25},
                'is_secret': False,
            },
            {
                'name': 'Legendary User',
                'description': 'Reach level 50 - You are among the elite!',
                'badge_type': 'achievement',
                'rarity': 'legendary',
                'points_reward': 2000,
                'requirements': {'min_level': 50},
                'is_secret': False,
            },

            # SECRET BADGES (Hidden achievements)
            {
                'name': 'Night Owl',
                'description': 'Read 10 posts between midnight and 6 AM',
                'badge_type': 'special',
                'rarity': 'rare',
                'points_reward': 300,
                'requirements': {'special': 'night_reading'},
                'is_secret': True,
            },
            {
                'name': 'Early Bird',
                'description': 'Read 10 posts between 5 AM and 8 AM',
                'badge_type': 'special',
                'rarity': 'rare',
                'points_reward': 300,
                'requirements': {'special': 'early_reading'},
                'is_secret': True,
            },
            {
                'name': 'Speed Reader',
                'description': 'Read 20 posts in a single day',
                'badge_type': 'special',
                'rarity': 'epic',
                'points_reward': 500,
                'requirements': {'special': 'speed_reading'},
                'is_secret': True,
            },
        ]

        for badge_data in badges:
            badge, created = Badge.objects.get_or_create(
                name=badge_data['name'],
                defaults=badge_data
            )
            if created:
                self.stdout.write(f'Created badge: {badge.name}')
            else:
                self.stdout.write(f'Badge already exists: {badge.name}')

    def create_challenges(self):
        """Create engaging challenges"""
        now = timezone.now()
        
        challenges = [
            # WEEKLY CHALLENGES (Achievable)
            {
                'title': 'Weekly Reader',
                'description': 'Read 10 posts this week',
                'challenge_type': 'reading',
                'difficulty': 'easy',
                'start_date': now,
                'end_date': now + timedelta(days=7),
                'requirements': {'posts_to_read': 10},
                'points_reward': 100,
                'is_featured': True,
                'is_active': True,
            },
            {
                'title': 'Content Creator Week',
                'description': 'Write 3 posts this week',
                'challenge_type': 'writing',
                'difficulty': 'medium',
                'start_date': now,
                'end_date': now + timedelta(days=7),
                'requirements': {'posts_to_write': 3},
                'points_reward': 200,
                'is_featured': True,
                'is_active': True,
            },
            {
                'title': 'Community Engager',
                'description': 'Make 15 comments this week',
                'challenge_type': 'engagement',
                'difficulty': 'easy',
                'start_date': now,
                'end_date': now + timedelta(days=7),
                'requirements': {'comments_to_make': 15},
                'points_reward': 150,
                'is_featured': False,
                'is_active': True,
            },

            # MONTHLY CHALLENGES (More challenging)
            {
                'title': 'Monthly Reading Marathon',
                'description': 'Read 100 posts this month',
                'challenge_type': 'reading',
                'difficulty': 'hard',
                'start_date': now,
                'end_date': now + timedelta(days=30),
                'requirements': {'posts_to_read': 100},
                'points_reward': 500,
                'is_featured': True,
                'is_active': True,
            },
            {
                'title': 'Prolific Writer',
                'description': 'Write 10 posts this month',
                'challenge_type': 'writing',
                'difficulty': 'hard',
                'start_date': now,
                'end_date': now + timedelta(days=30),
                'requirements': {'posts_to_write': 10},
                'points_reward': 750,
                'is_featured': True,
                'is_active': True,
            },
            {
                'title': 'Streak Master',
                'description': 'Maintain a 14-day reading streak',
                'challenge_type': 'reading',
                'difficulty': 'medium',
                'start_date': now,
                'end_date': now + timedelta(days=30),
                'requirements': {'reading_streak_days': 14},
                'points_reward': 400,
                'is_featured': False,
                'is_active': True,
            },

            # SPECIAL CHALLENGES (Very challenging)
            {
                'title': 'Ultimate Reader',
                'description': 'Read 500 posts in 3 months',
                'challenge_type': 'reading',
                'difficulty': 'expert',
                'start_date': now,
                'end_date': now + timedelta(days=90),
                'requirements': {'posts_to_read': 500},
                'points_reward': 2000,
                'is_featured': True,
                'is_active': True,
            },
            {
                'title': 'Master Creator',
                'description': 'Write 25 posts in 3 months',
                'challenge_type': 'writing',
                'difficulty': 'expert',
                'start_date': now,
                'end_date': now + timedelta(days=90),
                'requirements': {'posts_to_write': 25},
                'points_reward': 3000,
                'is_featured': True,
                'is_active': True,
            },
        ]

        for challenge_data in challenges:
            challenge, created = Challenge.objects.get_or_create(
                title=challenge_data['title'],
                defaults=challenge_data
            )
            if created:
                self.stdout.write(f'Created challenge: {challenge.title}')
            else:
                self.stdout.write(f'Challenge already exists: {challenge.title}')
