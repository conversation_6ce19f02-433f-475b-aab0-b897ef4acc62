"""
Core models for system maintenance and feature management
"""
from django.db import models
from django.core.cache import cache
from django.utils import timezone
from django.contrib.auth import get_user_model
import json

User = get_user_model()


class SystemMaintenance(models.Model):
    """Model to manage system-wide maintenance mode"""
    
    MAINTENANCE_TYPES = [
        ('full', 'Full System Maintenance'),
        ('partial', 'Partial Feature Maintenance'),
        ('database', 'Database Maintenance'),
        ('upgrade', 'System Upgrade'),
        ('emergency', 'Emergency Maintenance'),
    ]
    
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    title = models.CharField(max_length=200, help_text="Maintenance title")
    description = models.TextField(help_text="Detailed description of maintenance")
    maintenance_type = models.CharField(
        max_length=20, 
        choices=MAINTENANCE_TYPES,
        default='partial'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='scheduled'
    )
    
    # Timing
    scheduled_start = models.DateTimeField(help_text="When maintenance is scheduled to start")
    scheduled_end = models.DateTimeField(help_text="When maintenance is scheduled to end")
    actual_start = models.DateTimeField(null=True, blank=True)
    actual_end = models.DateTimeField(null=True, blank=True)
    
    # Settings
    is_active = models.BooleanField(default=False, help_text="Is maintenance currently active")
    allow_admin_access = models.BooleanField(default=True, help_text="Allow admin users during maintenance")
    allow_staff_access = models.BooleanField(default=False, help_text="Allow staff users during maintenance")
    
    # Messages
    public_message = models.TextField(
        default="System is currently under maintenance. Please try again later.",
        help_text="Message shown to users during maintenance"
    )
    admin_message = models.TextField(
        blank=True,
        help_text="Internal message for admin users"
    )
    
    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "System Maintenance"
        verbose_name_plural = "System Maintenance"
        ordering = ['-scheduled_start']
    
    def __str__(self):
        return f"{self.title} ({self.get_status_display()})"
    
    def save(self, *args, **kwargs):
        # Auto-update actual times based on status
        if self.status == 'active' and not self.actual_start:
            self.actual_start = timezone.now()
        elif self.status == 'completed' and not self.actual_end:
            self.actual_end = timezone.now()
        
        super().save(*args, **kwargs)
        
        # Clear cache when maintenance settings change
        cache.delete('system_maintenance_active')
        cache.delete('feature_toggles_active')
    
    @classmethod
    def is_maintenance_active(cls):
        """Check if any maintenance is currently active"""
        cached = cache.get('system_maintenance_active')
        if cached is not None:
            return cached

        active = cls.objects.filter(
            is_active=True,
            status='active'
        ).exists()

        cache.set('system_maintenance_active', active, 300)  # Cache for 5 minutes
        return active

    @classmethod
    def get_active_maintenance(cls):
        """Get the currently active maintenance record"""
        try:
            return cls.objects.filter(
                is_active=True,
                status='active'
            ).first()
        except cls.DoesNotExist:
            return None
    
    @classmethod
    def get_active_maintenance(cls):
        """Get currently active maintenance"""
        return cls.objects.filter(
            is_active=True,
            status='active'
        ).first()
    
    def can_user_access(self, user):
        """Check if user can access system during this maintenance"""
        if not self.is_active:
            return True
        
        if user.is_superuser and self.allow_admin_access:
            return True
        
        if user.is_staff and self.allow_staff_access:
            return True
        
        return False


class FeatureToggle(models.Model):
    """Model to manage individual feature toggles"""
    
    FEATURE_TYPES = [
        ('api', 'API Endpoint'),
        ('ui', 'UI Feature'),
        ('service', 'Background Service'),
        ('integration', 'Third-party Integration'),
        ('experimental', 'Experimental Feature'),
    ]
    
    name = models.CharField(max_length=100, unique=True, help_text="Feature identifier")
    display_name = models.CharField(max_length=200, help_text="Human-readable feature name")
    description = models.TextField(blank=True, help_text="Feature description")
    feature_type = models.CharField(max_length=20, choices=FEATURE_TYPES, default='ui')
    
    # Toggle settings
    is_enabled = models.BooleanField(default=True, help_text="Is feature currently enabled")
    is_global = models.BooleanField(default=True, help_text="Apply to all users")
    
    # User restrictions
    enabled_for_admins = models.BooleanField(default=True, help_text="Enable for admin users")
    enabled_for_staff = models.BooleanField(default=True, help_text="Enable for staff users")
    enabled_for_users = models.BooleanField(default=True, help_text="Enable for regular users")
    
    # Timing
    disable_start = models.DateTimeField(null=True, blank=True, help_text="When to disable feature")
    disable_end = models.DateTimeField(null=True, blank=True, help_text="When to re-enable feature")
    
    # Messages
    disabled_message = models.TextField(
        default="This feature is temporarily unavailable.",
        help_text="Message shown when feature is disabled"
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Feature Toggle"
        verbose_name_plural = "Feature Toggles"
        ordering = ['display_name']
    
    def __str__(self):
        status = "✅" if self.is_enabled else "❌"
        return f"{status} {self.display_name}"
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Clear cache when feature settings change
        cache.delete(f'feature_toggle_{self.name}')
        cache.delete('feature_toggles_active')
    
    @property
    def is_currently_enabled(self):
        """Check if feature is currently enabled based on timing"""
        if not self.is_enabled:
            return False
        
        now = timezone.now()
        
        # Check if we're in a disable period
        if self.disable_start and self.disable_end:
            if self.disable_start <= now <= self.disable_end:
                return False
        elif self.disable_start and now >= self.disable_start:
            return False
        
        return True
    
    def is_enabled_for_user(self, user):
        """Check if feature is enabled for specific user"""
        if not self.is_currently_enabled:
            return False
        
        if not self.is_global:
            return False
        
        if user.is_superuser:
            return self.enabled_for_admins
        elif user.is_staff:
            return self.enabled_for_staff
        else:
            return self.enabled_for_users
    
    @classmethod
    def is_feature_enabled(cls, feature_name, user=None):
        """Check if a specific feature is enabled"""
        cache_key = f'feature_toggle_{feature_name}'
        cached = cache.get(cache_key)
        
        if cached is None:
            try:
                feature = cls.objects.get(name=feature_name)
                cached = feature
                cache.set(cache_key, feature, 300)  # Cache for 5 minutes
            except cls.DoesNotExist:
                # Feature doesn't exist, assume enabled
                return True
        
        if user:
            return cached.is_enabled_for_user(user)
        else:
            return cached.is_currently_enabled


class MaintenanceLog(models.Model):
    """Log of maintenance activities"""
    
    LOG_TYPES = [
        ('start', 'Maintenance Started'),
        ('end', 'Maintenance Ended'),
        ('update', 'Settings Updated'),
        ('feature_toggle', 'Feature Toggled'),
        ('error', 'Error Occurred'),
        ('info', 'Information'),
    ]
    
    maintenance = models.ForeignKey(
        SystemMaintenance, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='logs'
    )
    feature_toggle = models.ForeignKey(
        FeatureToggle,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='logs'
    )
    
    log_type = models.CharField(max_length=20, choices=LOG_TYPES)
    message = models.TextField()
    details = models.JSONField(default=dict, blank=True)
    
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Maintenance Log"
        verbose_name_plural = "Maintenance Logs"
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.get_log_type_display()} - {self.timestamp.strftime('%Y-%m-%d %H:%M')}"


# Predefined feature toggles
PREDEFINED_FEATURES = [
    {
        'name': 'user_registration',
        'display_name': 'User Registration',
        'description': 'Allow new users to register accounts',
        'feature_type': 'api',
    },
    {
        'name': 'post_creation',
        'display_name': 'Post Creation',
        'description': 'Allow users to create new posts',
        'feature_type': 'ui',
    },
    {
        'name': 'commenting',
        'display_name': 'Commenting System',
        'description': 'Allow users to comment on posts',
        'feature_type': 'ui',
    },
    {
        'name': 'voting',
        'display_name': 'Voting System',
        'description': 'Allow users to vote on posts and polls',
        'feature_type': 'ui',
    },
    {
        'name': 'messaging',
        'display_name': 'Messaging System',
        'description': 'Allow users to send private messages',
        'feature_type': 'ui',
    },
    {
        'name': 'payments',
        'display_name': 'Payment Processing',
        'description': 'Process payments and transactions',
        'feature_type': 'service',
    },
    {
        'name': 'ai_writing',
        'display_name': 'AI Writing Assistant',
        'description': 'AI-powered writing assistance for content creators',
        'feature_type': 'service',
    },
    {
        'name': 'voice_features',
        'display_name': 'Voice Features',
        'description': 'Text-to-speech and voice comments',
        'feature_type': 'service',
    },
    {
        'name': 'blockchain',
        'display_name': 'Blockchain Features',
        'description': 'Cryptocurrency and NFT functionality',
        'feature_type': 'integration',
    },
    {
        'name': 'analytics',
        'display_name': 'Analytics Tracking',
        'description': 'User behavior and system analytics',
        'feature_type': 'service',
    },
]
