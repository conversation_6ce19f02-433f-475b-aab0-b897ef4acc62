from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timed<PERSON>ta
from decimal import Decimal
from gamification.models import PayPalReward, PayPalSettings


class Command(BaseCommand):
    help = 'Set up PayPal reward system with hard but achievable rewards'

    def handle(self, *args, **options):
        self.stdout.write('Setting up PayPal reward system...')
        
        # Create PayPal settings
        self.create_paypal_settings()
        
        # Create PayPal rewards
        self.create_paypal_rewards()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up PayPal reward system!')
        )

    def create_paypal_settings(self):
        """Create or update PayPal settings"""
        settings, created = PayPalSettings.objects.get_or_create(
            pk=1,
            defaults={
                'rewards_enabled': True,
                'minimum_payout': Decimal('5.00'),
                'maximum_monthly_payout_per_user': Decimal('100.00'),
                'require_email_verification': True,
                'require_phone_verification': False,  # Start with email only
                'minimum_account_age_days': 30,
                'minimum_activity_score': 100,
                'max_claims_per_day': 5,
                'max_claims_per_month': 20,
                'cooldown_period_days': 7,
                'paypal_mode': 'sandbox',
                'admin_email_notifications': True,
                'user_email_notifications': True,
            }
        )
        
        if created:
            self.stdout.write('Created PayPal settings')
        else:
            self.stdout.write('PayPal settings already exist')

    def create_paypal_rewards(self):
        """Create a comprehensive PayPal reward system"""
        rewards = [
            # STARTER REWARDS (Easy to achieve - build trust)
            {
                'name': 'First Steps Bonus',
                'description': 'Welcome reward for reading 50 posts and reaching level 3',
                'reward_type': 'milestone',
                'points_required': 500,
                'level_required': 3,
                'requirements': {'min_posts_read': 50},
                'usd_amount': Decimal('5.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 1000,  # Limit total payouts
                'minimum_account_age_days': 7,  # Shorter for starter
                'is_featured': True,
                'status': 'active',
            },

            # ENGAGEMENT REWARDS (Medium difficulty)
            {
                'name': 'Active Reader Reward',
                'description': 'For reading 200 posts and engaging with the community',
                'reward_type': 'milestone',
                'points_required': 1500,
                'level_required': 5,
                'requirements': {'min_posts_read': 200, 'min_comments': 25},
                'usd_amount': Decimal('10.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 500,
                'minimum_account_age_days': 30,
                'is_featured': True,
                'status': 'active',
            },

            {
                'name': 'Community Engager',
                'description': 'For making 100 comments and liking 500 posts',
                'reward_type': 'milestone',
                'points_required': 2500,
                'level_required': 8,
                'requirements': {'min_comments': 100, 'min_likes': 500, 'min_posts_read': 300},
                'usd_amount': Decimal('15.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 300,
                'minimum_account_age_days': 45,
                'is_featured': True,
                'status': 'active',
            },
            
            # CONSISTENCY REWARDS (Challenging)
            {
                'name': 'Streak Master Reward',
                'description': 'For maintaining a 30-day reading streak',
                'reward_type': 'streak',
                'points_required': 3000,
                'level_required': 10,
                'streak_required': 30,
                'usd_amount': Decimal('20.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 200,
                'minimum_account_age_days': 60,
                'is_featured': True,
                'status': 'active',
            },
            
            {
                'name': 'Super Streaker',
                'description': 'For maintaining a 60-day reading streak - incredible dedication!',
                'reward_type': 'streak',
                'points_required': 5000,
                'level_required': 15,
                'streak_required': 60,
                'usd_amount': Decimal('35.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 100,
                'minimum_account_age_days': 90,
                'is_featured': True,
                'status': 'active',
            },
            
            # HIGH ACHIEVEMENT REWARDS (Hard but possible)
            {
                'name': 'Elite Member Bonus',
                'description': 'For reaching level 20 with 10,000 points',
                'reward_type': 'level',
                'points_required': 10000,
                'level_required': 20,
                'requirements': {'min_posts_read': 300, 'min_posts_written': 50},
                'usd_amount': Decimal('50.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 50,
                'minimum_account_age_days': 120,
                'is_featured': True,
                'status': 'active',
            },
            
            {
                'name': 'Community Champion',
                'description': 'For exceptional community engagement and content creation',
                'reward_type': 'milestone',
                'points_required': 15000,
                'level_required': 25,
                'requirements': {
                    'min_posts_read': 500,
                    'min_posts_written': 75,
                    'min_comments': 200
                },
                'usd_amount': Decimal('75.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 25,
                'minimum_account_age_days': 180,
                'is_featured': True,
                'status': 'active',
            },
            
            # LEGENDARY REWARDS (Very hard - for top users)
            {
                'name': 'Trendy Legend',
                'description': 'For reaching level 35 with 25,000 points - you are a Trendy legend!',
                'reward_type': 'level',
                'points_required': 25000,
                'level_required': 35,
                'requirements': {
                    'min_posts_read': 1000,
                    'min_posts_written': 100,
                    'min_comments': 500
                },
                'usd_amount': Decimal('100.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 10,
                'minimum_account_age_days': 365,
                'is_featured': True,
                'status': 'active',
            },
            
            # SPECIAL EVENT REWARDS (Limited time)
            {
                'name': 'Launch Month Bonus',
                'description': 'Special reward for early adopters who reach level 10',
                'reward_type': 'special',
                'points_required': 2500,
                'level_required': 10,
                'usd_amount': Decimal('25.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 100,
                'minimum_account_age_days': 14,
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=60),  # 2 months
                'is_featured': True,
                'status': 'limited',
            },
            
            # RECURRING REWARDS (Can be claimed multiple times with cooldown)
            {
                'name': 'Monthly Achiever',
                'description': 'Monthly reward for consistent high engagement',
                'reward_type': 'milestone',
                'points_required': 1000,  # Points gained in current month
                'level_required': 8,
                'requirements': {'monthly_posts_read': 50, 'monthly_comments': 20},
                'usd_amount': Decimal('12.00'),
                'max_claims_per_user': 12,  # Once per month
                'max_total_claims': 1200,  # 100 users * 12 months
                'minimum_account_age_days': 30,
                'is_featured': False,
                'status': 'active',
            },
        ]

        for reward_data in rewards:
            # Set default values
            reward_data.setdefault('currency', 'USD')
            reward_data.setdefault('requires_verification', True)
            reward_data.setdefault('start_date', timezone.now())
            
            reward, created = PayPalReward.objects.get_or_create(
                name=reward_data['name'],
                defaults=reward_data
            )
            
            if created:
                self.stdout.write(f'Created PayPal reward: {reward.name} - ${reward.usd_amount}')
            else:
                self.stdout.write(f'PayPal reward already exists: {reward.name}')

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all PayPal rewards (delete existing)',
        )
        
    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting PayPal rewards...')
            PayPalReward.objects.all().delete()
            self.stdout.write('Deleted all existing PayPal rewards')
        
        self.stdout.write('Setting up PayPal reward system...')
        
        # Create PayPal settings
        self.create_paypal_settings()
        
        # Create PayPal rewards
        self.create_paypal_rewards()
        
        # Display summary
        self.display_summary()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up PayPal reward system!')
        )
    
    def display_summary(self):
        """Display a summary of created rewards"""
        rewards = PayPalReward.objects.all().order_by('points_required')
        total_potential_payout = sum(r.usd_amount * (r.max_total_claims or 1) for r in rewards)
        
        self.stdout.write('\n=== PAYPAL REWARD SUMMARY ===')
        self.stdout.write(f'Total rewards created: {rewards.count()}')
        self.stdout.write(f'Total potential payout: ${total_potential_payout}')
        
        self.stdout.write('\n=== REWARD TIERS ===')
        for reward in rewards:
            difficulty = "🟢 Easy" if reward.points_required < 1000 else \
                        "🟡 Medium" if reward.points_required < 5000 else \
                        "🟠 Hard" if reward.points_required < 15000 else \
                        "🔴 Legendary"
            
            self.stdout.write(
                f'{difficulty} | ${reward.usd_amount:>6} | {reward.points_required:>6} pts | '
                f'Level {reward.level_required:>2} | {reward.name}'
            )
