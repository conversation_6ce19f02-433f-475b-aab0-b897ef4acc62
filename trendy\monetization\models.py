from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import string
import random

User = get_user_model()


class PremiumSubscription(models.Model):
    """Premium subscription management"""
    SUBSCRIPTION_STATUS = [
        ('active', 'Active'),
        ('cancelled', 'Cancelled'),
        ('expired', 'Expired'),
        ('pending', 'Pending Payment'),
        ('failed', 'Payment Failed'),
    ]
    
    SUBSCRIPTION_PLANS = [
        ('monthly', 'Monthly - $9.99'),
        ('quarterly', 'Quarterly - $24.99'),
        ('yearly', 'Yearly - $99.99'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='premium_subscription')
    plan = models.CharField(max_length=20, choices=SUBSCRIPTION_PLANS, default='monthly')
    status = models.Char<PERSON>ield(max_length=20, choices=SUBSCRIPTION_STATUS, default='pending')
    
    # Subscription dates
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField()
    last_payment_date = models.DateTimeField(null=True, blank=True)
    next_payment_date = models.DateTimeField(null=True, blank=True)
    
    # Payment details
    monthly_price = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('9.99'))
    total_paid = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Features
    point_multiplier = models.DecimalField(max_digits=3, decimal_places=1, default=Decimal('2.0'))
    daily_streak_bonus = models.PositiveIntegerField(default=15)
    voice_comments_limit = models.PositiveIntegerField(default=999)  # 999 = unlimited
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['status', 'end_date']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.plan} ({self.status})"
    
    @property
    def is_active(self):
        """Check if subscription is currently active"""
        return (
            self.status == 'active' and 
            self.end_date > timezone.now()
        )
    
    @property
    def days_remaining(self):
        """Get days remaining in subscription"""
        if self.is_active:
            return (self.end_date - timezone.now()).days
        return 0


class PurchaseTransaction(models.Model):
    """Track all in-app purchases"""
    TRANSACTION_TYPES = [
        ('premium_subscription', 'Premium Subscription'),
        ('point_boost', 'Point Boost Package'),
        ('tier_unlock', 'Reward Tier Unlock'),
        ('streak_protection', 'Streak Protection'),
        ('cosmetic_item', 'Cosmetic Item'),
        ('functional_item', 'Functional Item'),
    ]
    
    TRANSACTION_STATUS = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='purchases')
    transaction_type = models.CharField(max_length=30, choices=TRANSACTION_TYPES)
    status = models.CharField(max_length=20, choices=TRANSACTION_STATUS, default='pending')
    
    # Purchase details
    item_name = models.CharField(max_length=100)
    item_description = models.TextField(blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    
    # Payment processing
    payment_method = models.CharField(max_length=50, blank=True)
    payment_transaction_id = models.CharField(max_length=100, blank=True)
    wallet_transaction_id = models.CharField(max_length=100, blank=True)  # For wallet payments
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['transaction_type', 'status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.item_name} - ${self.amount}"


class RewardTierUnlock(models.Model):
    """Track which reward tiers users have unlocked"""
    TIER_TYPES = [
        ('starter', 'Starter Tier - FREE'),
        ('engagement', 'Engagement Tier - $2.99'),
        ('achievement', 'Achievement Tier - $4.99'),
        ('elite', 'Elite Tier - $9.99'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tier_unlocks')
    tier = models.CharField(max_length=20, choices=TIER_TYPES)
    unlock_price = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Purchase reference
    purchase_transaction = models.ForeignKey(
        PurchaseTransaction, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True
    )
    
    unlocked_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['user', 'tier']
        indexes = [
            models.Index(fields=['user', 'tier']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.tier} tier"


class PointBoostPurchase(models.Model):
    """Track point boost package purchases"""
    BOOST_PACKAGES = [
        ('quick_start', 'Quick Start - 200+50 points - $1.99'),
        ('power_boost', 'Power Boost - 600+200 points - $4.99'),
        ('mega_boost', 'Mega Boost - 1500+500 points - $9.99'),
        ('ultimate_boost', 'Ultimate Boost - 3500+1500 points - $19.99'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='point_boosts')
    package = models.CharField(max_length=20, choices=BOOST_PACKAGES)
    
    # Points details
    base_points = models.PositiveIntegerField()
    bonus_points = models.PositiveIntegerField()
    total_points = models.PositiveIntegerField()
    
    # Purchase reference
    purchase_transaction = models.ForeignKey(
        PurchaseTransaction, 
        on_delete=models.CASCADE
    )
    
    # Status
    points_awarded = models.BooleanField(default=False)
    awarded_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'points_awarded']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.package} - {self.total_points} points"


class StreakProtection(models.Model):
    """Track streak protection purchases and usage"""
    PROTECTION_TYPES = [
        ('shield', 'Streak Shield - 1 day - $0.99'),
        ('insurance', 'Streak Insurance - 3 days - $2.99'),
        ('guardian', 'Streak Guardian - 7 days - $4.99'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='streak_protections')
    protection_type = models.CharField(max_length=20, choices=PROTECTION_TYPES)
    
    # Protection details
    protection_days = models.PositiveIntegerField()
    days_remaining = models.PositiveIntegerField()
    
    # Purchase reference
    purchase_transaction = models.ForeignKey(
        PurchaseTransaction, 
        on_delete=models.CASCADE
    )
    
    # Usage tracking
    is_active = models.BooleanField(default=True)
    used_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.protection_type} - {self.days_remaining} days left"


class VirtualItem(models.Model):
    """Virtual items available for purchase"""
    ITEM_CATEGORIES = [
        ('cosmetic', 'Cosmetic'),
        ('functional', 'Functional'),
        ('temporary', 'Temporary Boost'),
    ]
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    category = models.CharField(max_length=20, choices=ITEM_CATEGORIES)
    
    # Pricing
    price = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    
    # Item properties
    is_active = models.BooleanField(default=True)
    is_limited_time = models.BooleanField(default=False)
    max_purchases_per_user = models.PositiveIntegerField(null=True, blank=True)
    
    # Effects (JSON field for flexibility)
    effects = models.JSONField(default=dict)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['category', 'price']
        indexes = [
            models.Index(fields=['category', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.name} - ${self.price}"


class UserVirtualItem(models.Model):
    """Track virtual items owned by users"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='virtual_items')
    item = models.ForeignKey(VirtualItem, on_delete=models.CASCADE)

    # Purchase details
    purchase_price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    payment_method = models.CharField(
        max_length=20,
        choices=[
            ('store_points', 'Store Points'),
            ('real_money', 'Real Money'),
            ('free', 'Free'),
        ],
        default='store_points'
    )
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('completed', 'Completed'),
            ('failed', 'Failed'),
            ('refunded', 'Refunded'),
        ],
        default='pending'
    )

    # Purchase reference (optional for store point purchases)
    purchase_transaction = models.ForeignKey(
        PurchaseTransaction,
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )

    # Usage tracking
    is_active = models.BooleanField(default=True)
    uses_remaining = models.PositiveIntegerField(null=True, blank=True)

    purchased_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['item', 'user']),
            models.Index(fields=['payment_method', 'status']),
        ]

    def __str__(self):
        return f"{self.user.username} owns {self.item.name} ({self.payment_method})"


class UserReferralCode(models.Model):
    """Unique referral codes for each user"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='referral_code_obj')
    code = models.CharField(max_length=12, unique=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    usage_count = models.PositiveIntegerField(default=0)

    class Meta:
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['user']),
        ]

    def __str__(self):
        return f"{self.user.username}: {self.code}"

    @staticmethod
    def generate_unique_code():
        """Generate a unique 8-character referral code"""
        while True:
            # Generate code: 2 letters + 6 digits
            letters = ''.join(random.choices(string.ascii_uppercase, k=2))
            numbers = ''.join(random.choices(string.digits, k=6))
            code = letters + numbers

            # Check if code already exists
            if not UserReferralCode.objects.filter(code=code).exists():
                return code

    @classmethod
    def get_or_create_for_user(cls, user):
        """Get or create referral code for user"""
        try:
            return cls.objects.get(user=user)
        except cls.DoesNotExist:
            code = cls.generate_unique_code()
            return cls.objects.create(user=user, code=code)

    def increment_usage(self):
        """Increment usage count when code is used"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])


class ReferralProgram(models.Model):
    """Track referral relationships and rewards"""
    referrer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='referrals_made')
    referee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='referral_source')
    
    # Referral tracking
    referral_code_used = models.CharField(max_length=12)  # The code that was used
    referee_joined_at = models.DateTimeField(auto_now_add=True)
    
    # Milestone tracking
    referee_reached_level_5 = models.BooleanField(default=False)
    referee_went_premium = models.BooleanField(default=False)
    referee_made_purchase = models.BooleanField(default=False)
    
    # Rewards given
    join_reward_given = models.BooleanField(default=False)
    level_5_reward_given = models.BooleanField(default=False)
    premium_reward_given = models.BooleanField(default=False)
    
    # Revenue tracking
    total_revenue_generated = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    class Meta:
        unique_together = ['referrer', 'referee']
        indexes = [
            models.Index(fields=['referrer']),
            models.Index(fields=['referral_code_used']),
            models.Index(fields=['referee_joined_at']),
        ]
    
    def __str__(self):
        return f"{self.referrer.username} referred {self.referee.username}"


class MonetizationSettings(models.Model):
    """Global monetization settings"""
    # Premium subscription settings
    premium_monthly_price = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('9.99'))
    premium_point_multiplier = models.DecimalField(max_digits=3, decimal_places=1, default=Decimal('2.0'))
    premium_daily_bonus = models.PositiveIntegerField(default=15)
    
    # Tier unlock prices
    engagement_tier_price = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('2.99'))
    achievement_tier_price = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('4.99'))
    elite_tier_price = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('9.99'))
    
    # Referral rewards
    referral_join_points = models.PositiveIntegerField(default=100)
    referral_level_5_reward = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('2.00'))
    referral_premium_reward = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('5.00'))
    
    # System controls
    monetization_enabled = models.BooleanField(default=True)
    premium_enabled = models.BooleanField(default=True)
    tier_unlocks_enabled = models.BooleanField(default=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Monetization Settings"
        verbose_name_plural = "Monetization Settings"
    
    def save(self, *args, **kwargs):
        # Ensure only one settings instance exists
        if not self.pk and MonetizationSettings.objects.exists():
            raise ValueError("Only one MonetizationSettings instance is allowed")
        super().save(*args, **kwargs)
    
    @classmethod
    def get_settings(cls):
        """Get or create the single settings instance"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings
