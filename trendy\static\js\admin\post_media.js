document.addEventListener('DOMContentLoaded', function() {
    function toggleMediaFields() {
        $('.inline-related').each(function() {
            var $inlineGroup = $(this);
            var mediaType = $inlineGroup.find('.field-media_type select').val() || 'image';
            
            // Hide all type-specific fields initially
            $inlineGroup.find('.field-image, .field-image_url, .field-caption').hide();
            $inlineGroup.find('.field-video, .field-thumbnail, .field-title, .field-description').hide();
            
            // Show relevant fields based on media type
            if (mediaType === 'image') {
                $inlineGroup.find('.field-image, .field-image_url, .field-caption').show();
            } else if (mediaType === 'video') {
                $inlineGroup.find('.field-video, .field-thumbnail, .field-title, .field-description').show();
            }
        });
    }

    // Initial setup
    toggleMediaFields();
    
    // Event delegation for media type changes
    $(document).on('change', '.field-media_type select', function() {
        var $inlineGroup = $(this).closest('.inline-related');
        var mediaType = $(this).val();
        
        // Toggle fields
        if (mediaType === 'image') {
            $inlineGroup.find('.field-image, .field-image_url, .field-caption').show();
            $inlineGroup.find('.field-video, .field-thumbnail, .field-title, .field-description').hide();
        } else if (mediaType === 'video') {
            $inlineGroup.find('.field-video, .field-thumbnail, .field-title, .field-description').show();
            $inlineGroup.find('.field-image, .field-image_url, .field-caption').hide();
        }
    });

    // Handle dynamically added inlines
    django.jQuery(document).on('formset:added', function(event, $row) {
        toggleMediaFields();
    });
});