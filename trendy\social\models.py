from django.db import models
from django.contrib.auth import get_user_model
from blog.models import Post

User = get_user_model()

class UserProfile(models.Model):
    """Extended user profile with social features"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')

    # Profile information
    bio = models.TextField(max_length=500, blank=True)
    location = models.CharField(max_length=100, blank=True)
    website = models.URLField(blank=True)
    birth_date = models.DateField(null=True, blank=True)

    # Avatar and cover
    avatar = models.ImageField(upload_to='avatars/', null=True, blank=True)
    cover_image = models.ImageField(upload_to='covers/', null=True, blank=True)

    # Social links
    twitter_handle = models.CharField(max_length=50, blank=True)
    linkedin_url = models.URLField(blank=True)
    github_username = models.CharField(max_length=50, blank=True)

    # Privacy settings
    is_public = models.<PERSON><PERSON>anField(default=True)
    show_email = models.BooleanField(default=False)
    show_reading_activity = models.BooleanField(default=True)

    # Verification
    is_verified = models.BooleanField(default=False)
    verification_date = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['is_public']),
            models.Index(fields=['is_verified']),
        ]

    def __str__(self):
        return f"{self.user.username}'s profile"

class Follow(models.Model):
    """User following system"""
    follower = models.ForeignKey(User, on_delete=models.CASCADE, related_name='following')
    following = models.ForeignKey(User, on_delete=models.CASCADE, related_name='followers')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['follower', 'following']
        indexes = [
            models.Index(fields=['follower', 'created_at']),
            models.Index(fields=['following', 'created_at']),
        ]

    def __str__(self):
        return f"{self.follower.username} follows {self.following.username}"

class BookmarkCollection(models.Model):
    """Collections for organizing bookmarks"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bookmark_collections')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_public = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'name']
        ordering = ['name']

    def __str__(self):
        return f"{self.user.username}'s {self.name} collection"

class Bookmark(models.Model):
    """User bookmarks for posts"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bookmarks')
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='bookmarks')
    collection = models.ForeignKey(BookmarkCollection, on_delete=models.CASCADE, related_name='bookmarks', null=True, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'post']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['post']),
        ]

    def __str__(self):
        return f"{self.user.username} bookmarked {self.post.title}"

class ReadingList(models.Model):
    """User reading lists"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reading_lists')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_public = models.BooleanField(default=False)
    posts = models.ManyToManyField(Post, through='ReadingListItem', related_name='reading_lists')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'name']
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.user.username}'s {self.name} reading list"

class ReadingListItem(models.Model):
    """Items in reading lists"""
    reading_list = models.ForeignKey(ReadingList, on_delete=models.CASCADE)
    post = models.ForeignKey(Post, on_delete=models.CASCADE)
    added_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = ['reading_list', 'post']
        ordering = ['-added_at']

    def __str__(self):
        return f"{self.post.title} in {self.reading_list.name}"

class Notification(models.Model):
    """User notifications system"""
    NOTIFICATION_TYPES = [
        ('like', 'Post Liked'),
        ('comment', 'New Comment'),
        ('voice_comment', 'Voice Comment'),
        ('follow', 'New Follower'),
        ('mention', 'Mentioned'),
        ('badge', 'Badge Earned'),
        ('challenge', 'Challenge Update'),
        ('post_published', 'Post Published'),
        ('system', 'System Notification'),
    ]

    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='social_notifications')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='social_sent_notifications', null=True, blank=True)
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=200)
    message = models.TextField()

    # Related objects
    related_post = models.ForeignKey(Post, on_delete=models.CASCADE, null=True, blank=True)
    related_object_type = models.CharField(max_length=50, blank=True)
    related_object_id = models.PositiveIntegerField(null=True, blank=True)

    # Status
    is_read = models.BooleanField(default=False)
    is_seen = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', '-created_at']),
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['notification_type']),
        ]

    def __str__(self):
        return f"Notification for {self.recipient.username}: {self.title}"

class UserActivity(models.Model):
    """Track user activity for feeds"""
    ACTIVITY_TYPES = [
        ('post_published', 'Published a post'),
        ('post_liked', 'Liked a post'),
        ('comment_made', 'Commented on a post'),
        ('voice_comment', 'Left a voice comment'),
        ('user_followed', 'Followed a user'),
        ('badge_earned', 'Earned a badge'),
        ('challenge_completed', 'Completed a challenge'),
        ('reading_streak', 'Reading streak milestone'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities')
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES)
    description = models.CharField(max_length=200)

    # Related objects
    related_post = models.ForeignKey(Post, on_delete=models.CASCADE, null=True, blank=True)
    related_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='related_activities')
    related_object_type = models.CharField(max_length=50, blank=True)
    related_object_id = models.PositiveIntegerField(null=True, blank=True)

    # Metadata
    metadata = models.JSONField(default=dict, blank=True)

    # Visibility
    is_public = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['activity_type', '-created_at']),
            models.Index(fields=['is_public', '-created_at']),
        ]

    def __str__(self):
        return f"{self.user.username}: {self.description}"

class Report(models.Model):
    """Content reporting system"""
    REPORT_TYPES = [
        ('spam', 'Spam'),
        ('harassment', 'Harassment'),
        ('hate_speech', 'Hate Speech'),
        ('inappropriate', 'Inappropriate Content'),
        ('copyright', 'Copyright Violation'),
        ('misinformation', 'Misinformation'),
        ('other', 'Other'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('reviewing', 'Under Review'),
        ('resolved', 'Resolved'),
        ('dismissed', 'Dismissed'),
    ]

    reporter = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reports_made')
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES)
    description = models.TextField()

    # Reported content
    reported_post = models.ForeignKey(Post, on_delete=models.CASCADE, null=True, blank=True)
    reported_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='reports_received')
    reported_object_type = models.CharField(max_length=50, blank=True)
    reported_object_id = models.PositiveIntegerField(null=True, blank=True)

    # Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='reports_reviewed')
    resolution_notes = models.TextField(blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['report_type']),
            models.Index(fields=['reporter']),
        ]

    def __str__(self):
        return f"Report by {self.reporter.username}: {self.report_type}"
