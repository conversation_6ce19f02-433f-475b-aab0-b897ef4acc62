{% extends 'blog/base.html' %}

{% block title %}Register - Trendy Blog{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="row justify-content-center min-vh-100 align-items-center">
        <div class="col-lg-6 col-md-8 col-sm-10">
            <div class="auth-card">
                <!-- <PERSON><PERSON> and Header -->
                <div class="auth-header text-center mb-4">
                    <div class="auth-logo mb-3">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h2 class="auth-title">Join Trendy</h2>
                    <p class="auth-subtitle">Create your account to start sharing and discovering amazing content</p>
                </div>

                <!-- Registration Form -->
                <form method="post" class="auth-form needs-validation" novalidate>
                    {% csrf_token %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="id_first_name" class="form-label">
                                    <i class="fas fa-user me-2"></i>First Name
                                </label>
                                <input type="text"
                                       name="first_name"
                                       class="form-control auth-input"
                                       id="id_first_name"
                                       placeholder="Enter your first name"
                                       required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="id_last_name" class="form-label">
                                    <i class="fas fa-user me-2"></i>Last Name
                                </label>
                                <input type="text"
                                       name="last_name"
                                       class="form-control auth-input"
                                       id="id_last_name"
                                       placeholder="Enter your last name"
                                       required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="id_username" class="form-label">
                            <i class="fas fa-at me-2"></i>Username
                        </label>
                        <input type="text"
                               name="username"
                               class="form-control auth-input"
                               id="id_username"
                               placeholder="Choose a unique username"
                               required>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Username must be unique and can contain letters, numbers, and underscores.
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="id_email" class="form-label">
                            <i class="fas fa-envelope me-2"></i>Email Address
                        </label>
                        <input type="email"
                               name="email"
                               class="form-control auth-input"
                               id="id_email"
                               placeholder="Enter your email address"
                               required>
                    </div>

                    <div class="form-group mb-3">
                        <label for="id_password1" class="form-label">
                            <i class="fas fa-lock me-2"></i>Password
                        </label>
                        <div class="password-input-wrapper">
                            <input type="password"
                                   name="password1"
                                   class="form-control auth-input"
                                   id="id_password1"
                                   placeholder="Create a strong password"
                                   required>
                            <button type="button" class="password-toggle" onclick="togglePassword('id_password1')">
                                <i class="fas fa-eye" id="id_password1-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength mt-2">
                            <div class="password-strength-bar">
                                <div class="password-strength-fill" id="password-strength-fill"></div>
                            </div>
                            <small class="password-strength-text" id="password-strength-text">Password strength</small>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="id_password2" class="form-label">
                            <i class="fas fa-lock me-2"></i>Confirm Password
                        </label>
                        <div class="password-input-wrapper">
                            <input type="password"
                                   name="password2"
                                   class="form-control auth-input"
                                   id="id_password2"
                                   placeholder="Confirm your password"
                                   required>
                            <button type="button" class="password-toggle" onclick="togglePassword('id_password2')">
                                <i class="fas fa-eye" id="id_password2-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="form-group mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" class="auth-link">Terms of Service</a> and
                                <a href="#" class="auth-link">Privacy Policy</a>
                            </label>
                        </div>
                    </div>

                    <!-- Newsletter Subscription -->
                    <div class="form-group mb-4">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="newsletter" name="newsletter" checked>
                            <label class="form-check-label" for="newsletter">
                                Subscribe to our newsletter for updates and tips
                            </label>
                        </div>
                    </div>

                    <!-- Display form errors -->
                    {% if form.errors %}
                    <div class="alert alert-danger auth-alert mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Please correct the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <li>{{ field|title }}: {{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}

                    <button type="submit" class="btn auth-btn w-100 mb-3">
                        <i class="fas fa-user-plus me-2"></i>Create Account
                    </button>
                </form>

                <!-- Login Link -->
                <div class="auth-footer text-center">
                    <p class="mb-0">
                        Already have an account?
                        <a href="{% url 'login' %}" class="auth-link fw-bold">
                            Sign in here
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.password-strength {
    margin-top: 8px;
}

.password-strength-bar {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.password-strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.password-strength-text {
    display: block;
    margin-top: 4px;
    color: #6c757d;
    font-size: 12px;
}

.strength-weak .password-strength-fill {
    width: 25%;
    background: #dc3545;
}

.strength-fair .password-strength-fill {
    width: 50%;
    background: #fd7e14;
}

.strength-good .password-strength-fill {
    width: 75%;
    background: #ffc107;
}

.strength-strong .password-strength-fill {
    width: 100%;
    background: #28a745;
}
</style>

<script>
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const eye = document.getElementById(inputId + '-eye');

    if (input.type === 'password') {
        input.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}

// Password strength checker
document.getElementById('id_password1').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.getElementById('password-strength-fill');
    const strengthText = document.getElementById('password-strength-text');
    const strengthContainer = document.querySelector('.password-strength');

    // Remove existing classes
    strengthContainer.className = 'password-strength';

    if (password.length === 0) {
        strengthText.textContent = 'Password strength';
        return;
    }

    let score = 0;

    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;

    // Character variety checks
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    // Set strength level
    if (score < 3) {
        strengthContainer.classList.add('strength-weak');
        strengthText.textContent = 'Weak password';
    } else if (score < 4) {
        strengthContainer.classList.add('strength-fair');
        strengthText.textContent = 'Fair password';
    } else if (score < 5) {
        strengthContainer.classList.add('strength-good');
        strengthText.textContent = 'Good password';
    } else {
        strengthContainer.classList.add('strength-strong');
        strengthText.textContent = 'Strong password';
    }
});

// Password confirmation check
document.getElementById('id_password2').addEventListener('input', function() {
    const password1 = document.getElementById('id_password1').value;
    const password2 = this.value;

    if (password2.length > 0) {
        if (password1 === password2) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        }
    } else {
        this.classList.remove('is-valid', 'is-invalid');
    }
});
</script>
{% endblock %}