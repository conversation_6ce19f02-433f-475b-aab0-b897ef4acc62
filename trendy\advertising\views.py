from rest_framework import status, viewsets, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.generics import ListAPIView
from django.utils import timezone
from django.db import models
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from .services import AdService
from .models import (
    AdSettings, AdNetwork, AdPlacement, AdImpression,
    RewardedAd, SponsoredPost, AdNetworkPlacement
)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_ad(request):
    """Get ad for specific placement"""
    try:
        user = request.user
        placement_location = request.GET.get('placement')
        
        if not placement_location:
            return Response({
                'success': False,
                'error': 'Placement location is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get user context
        context = {
            'ip_address': request.META.get('REMOTE_ADDR'),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        }
        
        # Get ad for placement
        ad_data = AdService.get_ad_for_placement(user, placement_location, context)
        
        if ad_data:
            return Response({
                'success': True,
                'ad': ad_data
            })
        else:
            return Response({
                'success': True,
                'ad': None,
                'message': 'No ad available for this placement'
            })
            
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error getting ad: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def record_impression(request):
    """Record ad impression"""
    try:
        user = request.user
        placement_id = request.data.get('placement_id')
        ad_network_id = request.data.get('ad_network_id')
        ad_id = request.data.get('ad_id')
        
        if not placement_id or not ad_network_id:
            return Response({
                'success': False,
                'error': 'Placement ID and ad network ID are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get user context
        context = {
            'ip_address': request.META.get('REMOTE_ADDR'),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        }
        
        # Record impression
        result = AdService.record_ad_impression(
            user=user,
            placement_id=placement_id,
            ad_network_id=ad_network_id,
            ad_id=ad_id,
            context=context
        )
        
        if result['success']:
            return Response({
                'success': True,
                'impression_id': result['impression_id'],
                'revenue': result['revenue']
            })
        else:
            return Response({
                'success': False,
                'error': result['error']
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error recording impression: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def record_click(request):
    """Record ad click"""
    try:
        impression_id = request.data.get('impression_id')
        
        if not impression_id:
            return Response({
                'success': False,
                'error': 'Impression ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get user context
        context = {
            'ip_address': request.META.get('REMOTE_ADDR'),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        }
        
        # Record click
        result = AdService.record_ad_click(impression_id, context)
        
        if result['success']:
            return Response({
                'success': True,
                'click_revenue': result['click_revenue'],
                'total_revenue': result['total_revenue']
            })
        else:
            return Response({
                'success': False,
                'error': result['error']
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error recording click: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_rewarded_ad(request):
    """Start rewarded ad session"""
    try:
        user = request.user
        placement_id = request.data.get('placement_id')
        ad_network_id = request.data.get('ad_network_id')
        
        if not placement_id or not ad_network_id:
            return Response({
                'success': False,
                'error': 'Placement ID and ad network ID are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Start rewarded ad session
        result = AdService.start_rewarded_ad_session(user, placement_id, ad_network_id)
        
        if result['success']:
            return Response({
                'success': True,
                'session_id': result['session_id'],
                'points_offered': result['points_offered']
            })
        else:
            return Response({
                'success': False,
                'error': result['error']
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error starting rewarded ad: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def complete_rewarded_ad(request):
    """Complete rewarded ad session and award points"""
    try:
        session_id = request.data.get('session_id')
        completed = request.data.get('completed', True)
        
        if not session_id:
            return Response({
                'success': False,
                'error': 'Session ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Complete rewarded ad session
        result = AdService.complete_rewarded_ad_session(session_id, completed)
        
        if result['success']:
            return Response({
                'success': True,
                'points_awarded': result['points_awarded'],
                'message': result['message']
            })
        else:
            return Response({
                'success': False,
                'error': result['error']
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error completing rewarded ad: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_ad_stats(request):
    """Get user's ad interaction statistics"""
    try:
        user = request.user
        
        # Get user's ad statistics
        from .models import AdImpression
        
        # Today's stats
        today = timezone.now().replace(hour=0, minute=0, second=0)
        
        today_impressions = AdImpression.objects.filter(
            user=user,
            created_at__gte=today
        ).count()
        
        today_points = AdImpression.objects.filter(
            user=user,
            created_at__gte=today,
            points_awarded__gt=0
        ).aggregate(total=models.Sum('points_awarded'))['total'] or 0
        
        # All-time stats
        total_impressions = AdImpression.objects.filter(user=user).count()
        total_clicks = AdImpression.objects.filter(user=user, was_clicked=True).count()
        total_points_earned = AdImpression.objects.filter(
            user=user,
            points_awarded__gt=0
        ).aggregate(total=models.Sum('points_awarded'))['total'] or 0
        
        # Rewarded ad stats - count completed rewarded ad impressions
        completed_rewarded_ads = AdImpression.objects.filter(
            user=user,
            impression_type='completed',
            points_awarded__gt=0
        ).count()

        # Check daily limits
        settings = AdSettings.get_settings()
        max_daily_points = settings.max_rewarded_ads_per_day * 50  # Assume 50 points per ad
        daily_limit_remaining = max(0, max_daily_points - today_points)
        
        return Response({
            'success': True,
            'stats': {
                'today': {
                    'impressions': today_impressions,
                    'points_earned': today_points,
                    'daily_limit_remaining': daily_limit_remaining,
                },
                'all_time': {
                    'total_impressions': total_impressions,
                    'total_clicks': total_clicks,
                    'total_points_earned': total_points_earned,
                    'completed_rewarded_ads': completed_rewarded_ads,
                    'click_through_rate': round((total_clicks / total_impressions * 100) if total_impressions > 0 else 0, 2)
                },
                'limits': {
                    'max_daily_ad_points': max_daily_points,
                    'max_ads_per_session': settings.max_ads_per_session,
                    'min_time_between_ads': settings.min_time_between_ads,
                }
            }
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error getting user ad stats: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def ad_settings(request):
    """Get ad system settings for user"""
    try:
        settings = AdSettings.get_settings()
        
        # Check if user is premium
        from monetization.services import MonetizationService
        premium_status = MonetizationService.get_user_premium_status(request.user)
        
        return Response({
            'success': True,
            'settings': {
                'ads_enabled': settings.ads_enabled,
                'rewarded_ads_enabled': settings.rewarded_ads_enabled,
                'base_points_per_ad': 25,  # Default value since field doesn't exist
                'max_daily_ad_points': settings.max_rewarded_ads_per_day * 50,  # Calculated from existing fields
                'skip_ads_for_premium': not settings.show_ads_to_premium and premium_status['is_premium'],
                'user_is_premium': premium_status['is_premium'],
            }
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error getting ad settings: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def available_placements(request):
    """Get available ad placements for user"""
    try:
        user = request.user
        
        # Get available placements
        from .models import AdPlacement
        placements = AdPlacement.objects.filter(is_active=True)

        # Check premium targeting
        from monetization.services import MonetizationService
        premium_status = MonetizationService.get_user_premium_status(user)

        if not premium_status['is_premium']:
            # Show ads to non-premium users, or premium users if show_to_premium_users is True
            placements = placements.filter(show_to_premium_users=False)
        # Premium users see all active placements
        
        # Format placement data
        placement_data = []
        for placement in placements:
            # Check if this placement has rewarded ads
            from .models import RewardedAd
            rewarded_ads = RewardedAd.objects.filter(ad_placement=placement, is_active=True)
            first_ad = rewarded_ads.first()
            points_reward = first_ad.points_reward if first_ad else 0

            placement_data.append({
                'id': placement.id,
                'name': placement.name,
                'location': placement.location,
                'placement_type': placement.placement_type,
                'points_reward': points_reward,
                'is_rewarded': points_reward > 0,
                'frequency': placement.frequency_cap,
            })
        
        return Response({
            'success': True,
            'placements': placement_data
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error getting available placements: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Additional API Views for Flutter integration

class AdSettingsView(APIView):
    """Get advertising settings"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            settings = AdSettings.get_settings()

            # Check if user is premium
            from monetization.services import MonetizationService
            premium_status = MonetizationService.get_user_premium_status(request.user)

            return Response({
                'success': True,
                'settings': {
                    'ads_enabled': settings.ads_enabled,
                    'rewarded_ads_enabled': settings.rewarded_ads_enabled,
                    'sponsored_content_enabled': settings.sponsored_posts_enabled,
                    'max_ads_per_session': settings.max_ads_per_session,
                    'min_time_between_ads': settings.min_time_between_ads,
                    'base_points_per_ad': 25,  # Default value since field doesn't exist
                    'bonus_points_multiplier': 1.0,  # Default value
                    'max_daily_ad_points': settings.max_rewarded_ads_per_day * 50,  # Calculated from existing fields
                    'skip_ads_for_premium': not settings.show_ads_to_premium and premium_status['is_premium'],
                    'user_is_premium': premium_status['is_premium'],
                }
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': f'Error getting ad settings: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AdAvailabilityView(APIView):
    """Check ad availability for user"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user
            settings = AdSettings.get_settings()

            # Check daily limits
            today = timezone.now().replace(hour=0, minute=0, second=0)
            today_points = AdImpression.objects.filter(
                user=user,
                created_at__gte=today,
                points_awarded__gt=0
            ).aggregate(total=models.Sum('points_awarded'))['total'] or 0

            # Calculate max daily points from rewarded ads limit
            max_daily_points = settings.max_rewarded_ads_per_day * 50  # Assume 50 points per ad

            can_watch_ads = (
                settings.ads_enabled and
                today_points < max_daily_points
            )

            return Response({
                'success': True,
                'can_watch_ads': can_watch_ads,
                'daily_points_earned': today_points,
                'daily_limit': max_daily_points,
                'remaining_points': max(0, max_daily_points - today_points)
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserAdAnalyticsView(APIView):
    """Get user ad analytics"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user

            # Get user's ad statistics
            from .models import AdImpression

            # Today's stats
            today = timezone.now().replace(hour=0, minute=0, second=0)

            today_impressions = AdImpression.objects.filter(
                user=user,
                created_at__gte=today
            ).count()

            today_points = AdImpression.objects.filter(
                user=user,
                created_at__gte=today,
                points_awarded__gt=0
            ).aggregate(total=models.Sum('points_awarded'))['total'] or 0

            # All-time stats
            total_impressions = AdImpression.objects.filter(user=user).count()
            total_clicks = AdImpression.objects.filter(user=user, impression_type='clicked').count()
            total_points_earned = AdImpression.objects.filter(
                user=user,
                points_awarded__gt=0
            ).aggregate(total=models.Sum('points_awarded'))['total'] or 0

            # Rewarded ad stats - count completed rewarded ad impressions
            completed_rewarded_ads = AdImpression.objects.filter(
                user=user,
                impression_type='completed',
                points_awarded__gt=0
            ).count()

            # Check daily limits
            settings = AdSettings.get_settings()
            max_daily_points = settings.max_rewarded_ads_per_day * 50  # Assume 50 points per ad
            daily_limit_remaining = max(0, max_daily_points - today_points)

            return Response({
                'success': True,
                'stats': {
                    'today': {
                        'impressions': today_impressions,
                        'points_earned': today_points,
                        'daily_limit_remaining': daily_limit_remaining,
                    },
                    'all_time': {
                        'total_impressions': total_impressions,
                        'total_clicks': total_clicks,
                        'total_points_earned': total_points_earned,
                        'completed_rewarded_ads': completed_rewarded_ads,
                        'click_through_rate': round((total_clicks / total_impressions * 100) if total_impressions > 0 else 0, 2)
                    },
                    'limits': {
                        'max_daily_ad_points': max_daily_points,
                        'max_ads_per_session': settings.max_ads_per_session,
                        'min_time_between_ads': settings.min_time_between_ads,
                    }
                }
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': f'Error getting user ad stats: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserAdHistoryView(APIView):
    """Get user ad history"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user
            page = int(request.GET.get('page', 1))
            page_size = 20

            # Get user's ad impressions
            impressions = AdImpression.objects.filter(user=user).order_by('-created_at')

            # Paginate
            start = (page - 1) * page_size
            end = start + page_size
            page_impressions = impressions[start:end]

            # Format data
            history_data = []
            for impression in page_impressions:
                history_data.append({
                    'id': impression.id,
                    'placement_name': impression.ad_placement.name,
                    'impression_type': impression.impression_type,
                    'points_awarded': impression.points_awarded,
                    'created_at': impression.created_at.isoformat(),
                })

            return Response({
                'success': True,
                'results': history_data,
                'has_more': len(impressions) > end
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class StartRewardedAdView(APIView):
    """Start rewarded ad session"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            user = request.user
            ad_id = request.data.get('ad_id')

            if not ad_id:
                return Response({
                    'success': False,
                    'error': 'Ad ID is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the rewarded ad
            try:
                rewarded_ad = RewardedAd.objects.get(id=ad_id, is_active=True)
            except RewardedAd.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'Rewarded ad not found or inactive'
                }, status=status.HTTP_404_NOT_FOUND)

            # Check daily limits
            settings = AdSettings.get_settings()
            today = timezone.now().replace(hour=0, minute=0, second=0)
            today_points = AdImpression.objects.filter(
                user=user,
                created_at__gte=today,
                points_awarded__gt=0
            ).aggregate(total=models.Sum('points_awarded'))['total'] or 0

            if today_points >= settings.max_daily_ad_points:
                return Response({
                    'success': False,
                    'error': 'Daily ad points limit reached'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create rewarded ad session
            session = RewardedAdSession.objects.create(
                user=user,
                rewarded_ad=rewarded_ad,
                status='started',
                points_to_award=rewarded_ad.points_reward
            )

            return Response({
                'success': True,
                'session_id': session.session_id,
                'points_reward': rewarded_ad.points_reward,
                'estimated_duration': rewarded_ad.estimated_duration
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': f'Error starting rewarded ad: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CompleteRewardedAdView(APIView):
    """Complete rewarded ad session"""
    permission_classes = [IsAuthenticated]

    def post(self, request, session_id):
        try:
            user = request.user

            # Get the rewarded ad session
            try:
                session = RewardedAdSession.objects.get(
                    session_id=session_id,
                    user=user,
                    status='started'
                )
            except RewardedAdSession.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'Rewarded ad session not found or already completed'
                }, status=status.HTTP_404_NOT_FOUND)

            # Mark session as completed
            session.status = 'completed'
            session.completed_at = timezone.now()
            session.save()

            # Award points to user
            from gamification.services import GamificationService
            GamificationService.award_points(
                user=user,
                points=session.points_to_award,
                reason=f'Completed rewarded ad: {session.rewarded_ad.name}',
                category='advertising'
            )

            # Update user's total points
            user_points = GamificationService.get_user_points(user)

            return Response({
                'success': True,
                'points_awarded': session.points_to_award,
                'total_points': user_points['total_points'],
                'session_completed': True
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': f'Error completing rewarded ad: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SponsoredContentListView(APIView):
    """Get sponsored content"""
    permission_classes = []  # Allow unauthenticated access to sponsored content

    def get(self, request):
        try:
            content_type = request.GET.get('content_type', 'post')

            # Get active sponsored content
            sponsored_content = SponsoredPost.objects.filter(
                status='active',
                start_date__lte=timezone.now(),
                end_date__gte=timezone.now()
            ).order_by('-created_at')[:10]

            # Format data
            content_data = []
            for content in sponsored_content:
                content_data.append({
                    'id': str(content.id),
                    'title': content.title,
                    'content': getattr(content, 'content_description', content.title),
                    'sponsor_name': content.sponsor_name,
                    'sponsor_type': content.sponsor_type,
                    'sponsor_logo': getattr(content, 'sponsor_logo', None),
                    'image_url': getattr(content, 'content_image', None),
                    'video_url': getattr(content, 'video_url', None),
                    'call_to_action': getattr(content, 'call_to_action', 'Learn More'),
                    'target_url': getattr(content, 'target_url', ''),
                    'status': content.status,
                    'start_date': content.start_date.isoformat(),
                    'end_date': content.end_date.isoformat(),
                    'total_impressions': getattr(content, 'total_impressions', 0),
                    'total_clicks': getattr(content, 'total_clicks', 0),
                })

            return Response({
                'success': True,
                'results': content_data
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SponsoredContentClickView(APIView):
    """Record sponsored content click and award points"""
    permission_classes = [IsAuthenticated]

    def post(self, request, content_id):
        try:
            user = request.user
            print(f"🔍 SponsoredContentClickView: content_id={content_id}, user={user}")

            # Get the sponsored content
            try:
                sponsored_content = SponsoredPost.objects.get(id=content_id, status='active')
                print(f"✅ Found sponsored content: {sponsored_content.title}")
            except SponsoredPost.DoesNotExist:
                print(f"❌ Sponsored content not found: {content_id}")
                return Response({
                    'success': False,
                    'error': 'Sponsored content not found or inactive'
                }, status=status.HTTP_404_NOT_FOUND)

            # Check if user already clicked this content today (prevent spam)
            today = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
            existing_click = AdImpression.objects.filter(
                user=user,
                impression_type='clicked',
                created_at__gte=today,
                session_id=str(content_id)  # Use content_id as session identifier
            ).exists()

            if existing_click:
                return Response({
                    'success': False,
                    'error': 'You have already clicked this ad today'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Record click (increment click count)
            SponsoredPost.objects.filter(id=content_id).update(
                total_clicks=models.F('total_clicks') + 1
            )

            # Get default placement and network (use first available)
            default_placement = AdPlacement.objects.first()
            default_network = AdNetwork.objects.first()

            if default_placement and default_network:
                # Create impression record for click
                AdImpression.objects.create(
                    user=user,
                    ad_placement=default_placement,
                    ad_network=default_network,
                    impression_type='clicked',
                    session_id=str(content_id),
                    points_awarded=10,  # Points for clicking
                    revenue_amount=Decimal('0.05')
                )

            # Award points to user
            from gamification.services import GamificationService
            GamificationService.award_points(
                user=user,
                points=10,
                transaction_type='ad_click',
                description=f'Clicked sponsored content: {sponsored_content.title}',
                related_object_type='sponsored_content',
                related_object_id=content_id
            )

            return Response({
                'success': True,
                'message': 'Click recorded',
                'points_awarded': 10
            })

        except Exception as e:
            import traceback
            print(f"❌ Error in SponsoredContentClickView: {str(e)}")
            print(f"❌ Traceback: {traceback.format_exc()}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SponsoredContentImpressionView(APIView):
    """Record sponsored content impression and award points"""
    permission_classes = [permissions.AllowAny]  # Allow anonymous users

    def post(self, request, content_id):
        try:
            user = request.user if request.user.is_authenticated else None
            ip_address = request.META.get('REMOTE_ADDR', '')

            # Get the sponsored content
            try:
                sponsored_content = SponsoredPost.objects.get(id=content_id, status='active')
            except SponsoredPost.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'Sponsored content not found or inactive'
                }, status=status.HTTP_404_NOT_FOUND)

            # For authenticated users, check daily impression limits
            if user:
                today = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
                today_impressions = AdImpression.objects.filter(
                    user=user,
                    impression_type='shown',
                    created_at__gte=today,
                    session_id=str(content_id)
                ).count()

                if today_impressions >= 5:
                    return Response({
                        'success': False,
                        'error': 'Daily impression limit reached for this content'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Check if user viewed this content in the last 5 minutes (prevent rapid spam)
                five_minutes_ago = timezone.now() - timedelta(minutes=5)
                recent_impression = AdImpression.objects.filter(
                    user=user,
                    impression_type='shown',
                    created_at__gte=five_minutes_ago,
                    session_id=str(content_id)
                ).exists()

                if recent_impression:
                    return Response({
                        'success': True,
                        'message': 'Impression already recorded recently',
                        'points_awarded': 0
                    })
            else:
                # For anonymous users, check by IP address to prevent spam
                five_minutes_ago = timezone.now() - timedelta(minutes=5)
                recent_impression = AdImpression.objects.filter(
                    user=None,
                    impression_type='shown',
                    created_at__gte=five_minutes_ago,
                    session_id=str(content_id),
                    ip_address=ip_address
                ).exists()

                if recent_impression:
                    return Response({
                        'success': True,
                        'message': 'Impression already recorded recently',
                        'points_awarded': 0
                    })

            # Record impression (increment impression count)
            SponsoredPost.objects.filter(id=content_id).update(
                total_impressions=models.F('total_impressions') + 1
            )

            # Get default placement and network (use first available)
            default_placement = AdPlacement.objects.first()
            default_network = AdNetwork.objects.first()

            if not default_placement or not default_network:
                return Response({
                    'success': False,
                    'error': 'Ad placement or network not configured'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Create impression record
            impression_data = {
                'user': user,
                'ad_placement': default_placement,
                'ad_network': default_network,
                'impression_type': 'shown',
                'session_id': str(content_id),
                'points_awarded': 3 if user else 0,  # Only award points to authenticated users
                'revenue_amount': Decimal('0.02')
            }

            # Add IP address for anonymous users
            if not user:
                impression_data['ip_address'] = ip_address

            AdImpression.objects.create(**impression_data)

            points_awarded = 0
            # Award points only to authenticated users
            if user:
                print(f"🎯 Awarding points to authenticated user: {user}")
                from gamification.services import GamificationService
                GamificationService.award_points(
                    user=user,
                    points=3,
                    transaction_type='ad_view',
                    description=f'Viewed sponsored content: {sponsored_content.title}',
                    related_object_type='sponsored_content',
                    related_object_id=content_id
                )
                points_awarded = 3
                print(f"✅ Awarded {points_awarded} points for impression")
            else:
                print("⚠️ No authenticated user - no points awarded for impression")

            return Response({
                'success': True,
                'message': 'Impression recorded',
                'points_awarded': points_awarded
            })

        except Exception as e:
            import traceback
            print(f"❌ Error in SponsoredContentImpressionView: {str(e)}")
            print(f"❌ Traceback: {traceback.format_exc()}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RecordImpressionView(APIView):
    """Record ad impression"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            user = request.user
            placement_id = request.data.get('placement_id')
            ad_network_id = request.data.get('ad_network_id')
            ad_id = request.data.get('ad_id')

            if not placement_id or not ad_network_id:
                return Response({
                    'success': False,
                    'error': 'Placement ID and ad network ID are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get user context
            context = {
                'ip_address': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            }

            # Record impression
            result = AdService.record_ad_impression(
                user=user,
                placement_id=placement_id,
                ad_network_id=ad_network_id,
                ad_id=ad_id,
                context=context
            )

            if result['success']:
                return Response({
                    'success': True,
                    'impression_id': result['impression_id'],
                    'points_awarded': result.get('points_awarded', 0)
                })
            else:
                return Response({
                    'success': False,
                    'error': result['error']
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'error': f'Error recording impression: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ViewSets for admin/management access
class AdNetworkViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for ad networks"""
    queryset = AdNetwork.objects.all()
    permission_classes = [IsAuthenticated]

    def list(self, request):
        networks = self.get_queryset().filter(is_active=True)
        data = [{'id': n.id, 'name': n.name, 'network_type': n.network_type} for n in networks]
        return Response({'results': data})


class AdPlacementViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for ad placements"""
    queryset = AdPlacement.objects.all()
    permission_classes = [IsAuthenticated]

    def list(self, request):
        try:
            user = request.user

            # Get active placements
            placements = AdPlacement.objects.filter(is_active=True)

            # Check premium targeting
            from monetization.services import MonetizationService
            premium_status = MonetizationService.get_user_premium_status(user)

            if not premium_status['is_premium']:
                # Show ads to non-premium users, or premium users if show_to_premium_users is True
                placements = placements.filter(show_to_premium_users=False)
            # Premium users see all active placements

            # Format placement data
            placement_data = []
            for placement in placements:
                # Check if this placement has rewarded ads
                from .models import RewardedAd
                rewarded_ads = RewardedAd.objects.filter(ad_placement=placement, is_active=True)
                first_ad = rewarded_ads.first()
                points_reward = first_ad.points_reward if first_ad else 0

                placement_data.append({
                    'id': str(placement.id),
                    'name': placement.name,
                    'location': placement.location,
                    'placement_type': placement.placement_type,
                    'points_reward': points_reward,
                    'is_rewarded': points_reward > 0,
                    'frequency': placement.frequency_cap,
                    'is_active': placement.is_active,
                    'max_per_session': placement.frequency_cap,  # Use frequency_cap as max_per_session
                    'min_user_level': 1,  # Default value since field doesn't exist
                    'premium_users_only': placement.show_to_premium_users,
                    'free_users_only': not placement.show_to_premium_users,  # Inverse of premium_users_only
                })

            return Response({
                'success': True,
                'placements': placement_data
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': f'Error getting available placements: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AdImpressionViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for ad impressions"""
    queryset = AdImpression.objects.all()
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user)


class RewardedAdViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for rewarded ads"""
    queryset = RewardedAd.objects.all()
    permission_classes = [IsAuthenticated]

    def list(self, request):
        ads = self.get_queryset().filter(is_active=True)
        data = [{'id': a.id, 'name': a.name, 'points_reward': a.points_reward} for a in ads]
        return Response({'results': data})


class SponsoredContentViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for sponsored content"""
    queryset = SponsoredPost.objects.all()
    permission_classes = []  # Allow unauthenticated access to sponsored content

    def list(self, request):
        view = SponsoredContentListView()
        view.request = request
        return view.get(request)
