# Generated by Django 5.1.7 on 2025-06-24 11:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('blockchain', '0002_alter_stakingpool_maximum_stake_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='blockchainnetwork',
            name='name',
            field=models.CharField(choices=[('polygon', 'Polygon (MATIC)'), ('polygon_testnet', 'Polygon Mumbai Testnet'), ('bsc', 'Binance Smart Chain'), ('bsc_testnet', 'BSC Testnet'), ('ethereum', 'Ethereum'), ('ethereum_testnet', 'Ethereum Goerli Testnet'), ('solana', 'Solana'), ('solana_testnet', 'Solana Devnet')], max_length=50, unique=True),
        ),
    ]
