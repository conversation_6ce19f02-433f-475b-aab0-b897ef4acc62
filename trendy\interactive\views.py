from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db.models import F
from django.db import transaction
from blog.models import Post
from blog.permissions import CanCreateInteractiveContent
from gamification.services import GamificationService
from .models import (
    InteractiveBlock, Poll, PollOption, PollVote,
    Quiz, QuizAttempt
)
from .serializers import (
    InteractiveBlockSerializer, PollSerializer,
    QuizSerializer, QuizAttemptSerializer,
    CreateInteractiveBlockSerializer
)

def get_client_ip(request):
    """Get client IP address"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

@api_view(['GET'])
@permission_classes([AllowAny])
def get_post_interactive_blocks(request, post_id):
    """Get all interactive blocks for a post"""
    try:
        post = get_object_or_404(Post, id=post_id)
        blocks = InteractiveBlock.objects.filter(
            post=post,
            is_active=True
        ).order_by('position')

        serializer = InteractiveBlockSerializer(
            blocks,
            many=True,
            context={'request': request}
        )
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_interactive_block(request, post_id):
    """Create a new interactive block for a post"""
    try:
        post = get_object_or_404(Post, id=post_id)

        # Check if user is the author or has permission
        if post.author != request.user and not request.user.is_staff:
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        data = request.data.copy()
        data['post'] = post.id

        serializer = CreateInteractiveBlockSerializer(data=data)
        if serializer.is_valid():
            block = serializer.save(post=post)
            response_serializer = InteractiveBlockSerializer(
                block,
                context={'request': request}
            )
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def debug_poll_auth(request, poll_id):
    """Debug endpoint to check authentication for poll voting"""
    return Response({
        'poll_id': poll_id,
        'headers': dict(request.headers),
        'user': str(request.user),
        'is_authenticated': request.user.is_authenticated,
        'auth_header': request.headers.get('Authorization', 'NOT FOUND'),
        'method': request.method,
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def vote_poll(request, poll_id):
    """Vote on a poll"""
    try:
        poll = get_object_or_404(Poll, id=poll_id)

        if not poll.is_active:
            return Response({
                'error': 'Poll is not active or has expired'
            }, status=status.HTTP_400_BAD_REQUEST)

        option_ids = request.data.get('option_ids', [])
        if not option_ids:
            return Response({
                'error': 'No options selected'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate option IDs
        options = PollOption.objects.filter(
            id__in=option_ids,
            poll=poll
        )

        if len(options) != len(option_ids):
            return Response({
                'error': 'Invalid option IDs'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if multiple choices are allowed
        if not poll.allow_multiple_choices and len(option_ids) > 1:
            return Response({
                'error': 'Multiple choices not allowed for this poll'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if user has already voted
        ip_address = get_client_ip(request)

        if poll.is_anonymous:
            # For anonymous polls, check by IP address
            existing_votes = PollVote.objects.filter(
                poll=poll,
                ip_address=ip_address
            )
        else:
            # For non-anonymous polls, check by user
            existing_votes = PollVote.objects.filter(
                poll=poll,
                user=request.user
            )

        if existing_votes.exists():
            return Response({
                'error': 'You have already voted on this poll'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create votes
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        with transaction.atomic():
            votes_created = []
            for option in options:
                vote = PollVote.objects.create(
                    poll=poll,
                    user=request.user if not poll.is_anonymous else None,
                    option=option,
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                votes_created.append(vote)

                # Update option vote count
                option.vote_count = F('vote_count') + 1
                option.save(update_fields=['vote_count'])

            # Update poll totals
            poll.total_votes = F('total_votes') + len(votes_created)
            if not poll.is_anonymous:
                poll.unique_voters = F('unique_voters') + 1
            poll.save(update_fields=['total_votes', 'unique_voters'])

            # Award gamification points for poll participation
            if not poll.is_anonymous and request.user.is_authenticated:
                try:
                    success, message = GamificationService.update_engagement_activity(
                        user=request.user,
                        activity_type='poll_vote',
                        object_id=poll.id,
                        target_type='poll'
                    )
                    print(f"🎯 Poll vote gamification: {success} - {message}")
                except Exception as e:
                    print(f"❌ Failed to award poll vote points: {e}")

        # Refresh poll data
        poll.refresh_from_db()
        serializer = PollSerializer(poll, context={'request': request})

        return Response({
            'message': 'Vote recorded successfully',
            'poll': serializer.data
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def get_poll_results(request, poll_id):
    """Get poll results"""
    try:
        poll = get_object_or_404(Poll, id=poll_id)

        # Check if results should be shown
        if not poll.show_results_immediately and poll.is_active:
            # Only show results to users who have voted
            if request.user.is_authenticated:
                user_voted = PollVote.objects.filter(
                    poll=poll,
                    user=request.user
                ).exists()
                if not user_voted:
                    return Response({
                        'error': 'Results not available until you vote or poll ends'
                    }, status=status.HTTP_403_FORBIDDEN)

        serializer = PollSerializer(poll, context={'request': request})
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def submit_quiz(request, quiz_id):
    """Submit quiz answers"""
    try:
        quiz = get_object_or_404(Quiz, id=quiz_id)

        answers = request.data.get('answers', {})  # {question_id: [answer_ids]}
        time_taken = request.data.get('time_taken', 0)

        if not answers:
            return Response({
                'error': 'No answers provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Calculate score
        total_points = 0
        earned_points = 0

        for question in quiz.questions.all():
            total_points += question.points

            question_answers = answers.get(str(question.id), [])
            if question_answers:
                # Check if answers are correct
                correct_answers = question.answers.filter(is_correct=True)
                user_answers = question.answers.filter(id__in=question_answers)

                # Simple scoring: all correct answers must be selected
                if set(correct_answers) == set(user_answers):
                    earned_points += question.points

        score = (earned_points / total_points * 100) if total_points > 0 else 0

        # Create quiz attempt
        attempt = QuizAttempt.objects.create(
            quiz=quiz,
            user=request.user,
            score=score,
            total_points=total_points,
            earned_points=earned_points,
            time_taken=time_taken,
            answers=answers
        )

        # Update quiz statistics
        quiz.total_attempts = F('total_attempts') + 1
        quiz.save(update_fields=['total_attempts'])

        # Award gamification points for quiz completion
        try:
            # Base points for completion
            success, message = GamificationService.update_engagement_activity(
                user=request.user,
                activity_type='quiz_complete',
                object_id=quiz.id,
                target_type='quiz'
            )

            # Bonus points for high performance
            if score >= 90:
                # Excellent performance bonus
                GamificationService.award_points(
                    user=request.user,
                    points=10,
                    transaction_type='bonus',
                    description=f'Excellent quiz performance: {score:.1f}%',
                    related_object_type='quiz',
                    related_object_id=quiz.id
                )
            elif score >= 70:
                # Good performance bonus
                GamificationService.award_points(
                    user=request.user,
                    points=5,
                    transaction_type='bonus',
                    description=f'Good quiz performance: {score:.1f}%',
                    related_object_type='quiz',
                    related_object_id=quiz.id
                )

            print(f"🎯 Quiz completion gamification: {success} - {message}")
        except Exception as e:
            print(f"❌ Failed to award quiz completion points: {e}")

        serializer = QuizAttemptSerializer(attempt)
        return Response({
            'message': 'Quiz submitted successfully',
            'attempt': serializer.data
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_quiz_attempts(request, quiz_id):
    """Get user's quiz attempts"""
    try:
        quiz = get_object_or_404(Quiz, id=quiz_id)

        attempts = QuizAttempt.objects.filter(
            quiz=quiz,
            user=request.user
        ).order_by('-completed_at')

        serializer = QuizAttemptSerializer(attempts, many=True)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_interactive_block(request, block_id):
    """Delete an interactive block"""
    try:
        block = get_object_or_404(InteractiveBlock, id=block_id)

        # Check permissions
        if block.post.author != request.user and not request.user.is_staff:
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        block.delete()
        return Response({
            'message': 'Interactive block deleted successfully'
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def get_interactive_block(request, block_id):
    """Get a specific interactive block"""
    try:
        block = get_object_or_404(InteractiveBlock, id=block_id, is_active=True)

        serializer = InteractiveBlockSerializer(
            block,
            context={'request': request}
        )
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
