from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from .models import PointTransaction, UserLevel
from .services import GamificationService
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


@receiver(post_save, sender=User)
def create_user_gamification_profile(sender, instance, created, **kwargs):
    """Create gamification profile and award welcome points when a new user is created"""
    if created:
        try:
            # Create or get user level
            user_level, level_created = UserLevel.objects.get_or_create(
                user=instance,
                defaults={
                    'total_points': 0,
                    'current_level': 1,
                    'points_to_next_level': 100,
                }
            )
            
            if level_created:
                logger.info(f"Created gamification profile for user {instance.username}")
            
            # Award welcome points
            welcome_points = 100  # Standard welcome bonus
            
            # Check if user already has welcome points (to avoid duplicates)
            existing_welcome = PointTransaction.objects.filter(
                user=instance,
                transaction_type='welcome'
            ).exists()
            
            if not existing_welcome:
                # Create welcome point transaction
                PointTransaction.objects.create(
                    user=instance,
                    points=welcome_points,
                    transaction_type='welcome',
                    description='Welcome bonus for new user registration'
                )
                
                # Update user level points
                user_level.total_points += welcome_points
                user_level.save()
                
                logger.info(f"Awarded {welcome_points} welcome points to user {instance.username}")
                
                # Check for badges and level progression
                try:
                    GamificationService.check_and_award_badges(instance)
                except Exception as e:
                    logger.warning(f"Failed to check badges for user {instance.username}: {str(e)}")
            else:
                logger.info(f"User {instance.username} already has welcome points, skipping")
                
        except Exception as e:
            logger.error(f"Error creating gamification profile for user {instance.username}: {str(e)}")


@receiver(post_save, sender=User)
def assign_user_to_regular_group(sender, instance, created, **kwargs):
    """Assign new users to Regular Users group if they don't have any groups"""
    if created:
        try:
            # Skip if user is admin/staff
            if instance.is_staff or instance.is_superuser:
                logger.info(f"Skipping group assignment for admin user: {instance.username}")
                return
            
            # Check if user already has groups
            if instance.groups.exists():
                logger.info(f"User {instance.username} already has groups, skipping assignment")
                return
            
            # Assign to Regular Users group
            regular_users_group = Group.objects.filter(name='Regular Users').first()
            if regular_users_group:
                instance.groups.add(regular_users_group)
                logger.info(f"Assigned user {instance.username} to Regular Users group")
            else:
                logger.warning("Regular Users group not found. Run 'python manage.py setup_user_roles' to create user groups.")
                
        except Exception as e:
            logger.error(f"Error assigning group to user {instance.username}: {str(e)}")
