from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from .models import UserReferralCode
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


@receiver(post_save, sender=User)
def create_referral_code(sender, instance, created, **kwargs):
    """Create a unique referral code when a new user is created"""
    if created:
        try:
            # Create referral code for new user
            referral_code = UserReferralCode.get_or_create_for_user(instance)
            logger.info(f"Created referral code {referral_code.code} for user {instance.username}")
        except Exception as e:
            logger.error(f"Error creating referral code for user {instance.username}: {str(e)}")


@receiver(post_save, sender=User)
def ensure_referral_code_exists(sender, instance, **kwargs):
    """Ensure existing users have referral codes"""
    if not hasattr(instance, 'referral_code_obj') or not instance.referral_code_obj:
        try:
            referral_code = UserReferralCode.get_or_create_for_user(instance)
            logger.info(f"Ensured referral code {referral_code.code} exists for user {instance.username}")
        except Exception as e:
            logger.error(f"Error ensuring referral code for user {instance.username}: {str(e)}")
