from rest_framework import serializers
from .models import (
    AIConfiguration, AIWritingPreferences, ContentPrompt, AIWritingSession,
    ContentSuggestion, AIUsageAnalytics, ContentTemplate
)


class AIConfigurationSerializer(serializers.ModelSerializer):
    """Serializer for AI configuration (public info only)"""

    class Meta:
        model = AIConfiguration
        fields = [
            'id', 'name', 'provider', 'model_name', 'is_active', 'is_default',
            'enable_content_generation', 'enable_grammar_improvement',
            'enable_seo_suggestions', 'enable_text_completion', 'enable_readability_analysis',
            'max_tokens', 'temperature', 'requests_per_minute', 'requests_per_day',
            'total_requests', 'last_used'
        ]
        read_only_fields = ['total_requests', 'last_used']


class AIWritingPreferencesSerializer(serializers.ModelSerializer):
    """Serializer for AI writing preferences"""
    
    class Meta:
        model = AIWritingPreferences
        fields = [
            'preferred_tone', 'preferred_style', 'target_audience',
            'enable_grammar_suggestions', 'enable_seo_suggestions',
            'enable_content_generation', 'enable_readability_analysis',
            'enable_auto_complete', 'preferred_word_count',
            'include_references', 'include_images_suggestions'
        ]


class ContentPromptSerializer(serializers.ModelSerializer):
    """Serializer for content prompts"""
    
    class Meta:
        model = ContentPrompt
        fields = [
            'id', 'name', 'prompt_type', 'template', 'category',
            'is_active', 'usage_count'
        ]
        read_only_fields = ['usage_count']


class AIWritingSessionSerializer(serializers.ModelSerializer):
    """Serializer for AI writing sessions"""
    
    class Meta:
        model = AIWritingSession
        fields = [
            'id', 'post', 'session_data', 'status',
            'suggestions_generated', 'suggestions_accepted',
            'words_generated', 'time_saved_minutes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ContentSuggestionSerializer(serializers.ModelSerializer):
    """Serializer for content suggestions"""
    
    class Meta:
        model = ContentSuggestion
        fields = [
            'id', 'session', 'suggestion_type', 'original_text',
            'suggested_text', 'explanation', 'confidence_score',
            'start_position', 'end_position', 'status', 'user_feedback',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class ContentTemplateSerializer(serializers.ModelSerializer):
    """Serializer for content templates"""
    
    class Meta:
        model = ContentTemplate
        fields = [
            'id', 'name', 'description', 'template_content',
            'category', 'estimated_word_count', 'difficulty_level',
            'is_public', 'usage_count', 'created_at'
        ]
        read_only_fields = ['usage_count', 'created_at']


# Request/Response serializers for AI operations
class ContentIdeaRequestSerializer(serializers.Serializer):
    """Serializer for content idea generation requests"""
    topic = serializers.CharField(max_length=200)
    count = serializers.IntegerField(default=5, min_value=1, max_value=10)


class ContentIdeaResponseSerializer(serializers.Serializer):
    """Serializer for content idea generation responses"""
    ideas = serializers.ListField(child=serializers.CharField())


class ContentOutlineRequestSerializer(serializers.Serializer):
    """Serializer for content outline generation requests"""
    title = serializers.CharField(max_length=200)


class ContentOutlineResponseSerializer(serializers.Serializer):
    """Serializer for content outline generation responses"""
    introduction = serializers.ListField(child=serializers.CharField())
    main_sections = serializers.ListField(child=serializers.DictField())
    conclusion = serializers.ListField(child=serializers.CharField())
    estimated_word_count = serializers.IntegerField()


class GrammarImprovementRequestSerializer(serializers.Serializer):
    """Serializer for grammar improvement requests"""
    text = serializers.CharField()


class GrammarImprovementResponseSerializer(serializers.Serializer):
    """Serializer for grammar improvement responses"""
    improved_text = serializers.CharField()
    changes = serializers.ListField(child=serializers.DictField())
    readability_score = serializers.FloatField()


class SEOSuggestionsRequestSerializer(serializers.Serializer):
    """Serializer for SEO suggestions requests"""
    content = serializers.CharField()
    title = serializers.CharField(required=False, allow_blank=True)


class SEOSuggestionsResponseSerializer(serializers.Serializer):
    """Serializer for SEO suggestions responses"""
    title_suggestions = serializers.ListField(child=serializers.CharField())
    meta_description = serializers.CharField()
    keywords = serializers.ListField(child=serializers.CharField())
    content_suggestions = serializers.ListField(child=serializers.CharField())
    readability_issues = serializers.ListField(child=serializers.CharField())


class TextCompletionRequestSerializer(serializers.Serializer):
    """Serializer for text completion requests"""
    partial_text = serializers.CharField()
    context = serializers.CharField(required=False, allow_blank=True)


class TextCompletionResponseSerializer(serializers.Serializer):
    """Serializer for text completion responses"""
    completed_text = serializers.CharField()


class ReadabilityAnalysisRequestSerializer(serializers.Serializer):
    """Serializer for readability analysis requests"""
    content = serializers.CharField()


class ReadabilityAnalysisResponseSerializer(serializers.Serializer):
    """Serializer for readability analysis responses"""
    word_count = serializers.IntegerField()
    sentence_count = serializers.IntegerField()
    paragraph_count = serializers.IntegerField()
    avg_words_per_sentence = serializers.FloatField()
    avg_sentences_per_paragraph = serializers.FloatField()
    readability_score = serializers.FloatField()
    reading_level = serializers.CharField()
    estimated_reading_time = serializers.IntegerField()
    suggestions = serializers.ListField(child=serializers.CharField())


class AIUsageAnalyticsSerializer(serializers.ModelSerializer):
    """Serializer for AI usage analytics"""
    
    class Meta:
        model = AIUsageAnalytics
        fields = [
            'feature_used', 'usage_count', 'total_time_saved_minutes',
            'total_words_generated', 'year', 'month'
        ]
