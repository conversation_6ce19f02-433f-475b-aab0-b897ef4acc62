import openai
import re
import json
import requests
from typing import Dict, List, Optional, Tuple
from django.conf import settings
from django.utils import timezone
from datetime import datetime
from .models import (
    AIConfiguration, AIWritingPreferences, ContentPrompt, AIWritingSession,
    ContentSuggestion, AIUsageAnalytics, ContentTemplate
)


class AIWritingService:
    """Service for AI-powered writing assistance"""

    def __init__(self):
        self.ai_config = AIConfiguration.get_default_config()
        if self.ai_config and self.ai_config.provider == 'openai':
            openai.api_key = self.ai_config.api_key
        elif hasattr(settings, 'OPENAI_API_KEY'):
            openai.api_key = settings.OPENAI_API_KEY
        
    def get_user_preferences(self, user):
        """Get or create user AI writing preferences"""
        preferences, created = AIWritingPreferences.objects.get_or_create(
            user=user,
            defaults={
                'preferred_tone': 'professional',
                'preferred_style': 'blog',
                'target_audience': 'general audience'
            }
        )
        return preferences
    
    def create_writing_session(self, user, post=None):
        """Create a new AI writing session"""
        session = AIWritingSession.objects.create(
            user=user,
            post=post,
            session_data={'started_at': timezone.now().isoformat()}
        )
        return session

    def make_ai_request(self, prompt: str, user=None, feature_type: str = 'general') -> str:
        """Make AI request using configured AI provider"""
        if not self.ai_config or not self.ai_config.is_active:
            return self._generate_fallback_response(prompt, feature_type)

        try:
            if self.ai_config.provider == 'openai':
                return self._make_openai_request(prompt)
            elif self.ai_config.provider == 'anthropic':
                return self._make_anthropic_request(prompt)
            elif self.ai_config.provider == 'google':
                return self._make_google_request(prompt)
            elif self.ai_config.provider == 'custom':
                return self._make_custom_request(prompt)
            else:
                return self._generate_fallback_response(prompt, feature_type)
        except Exception as e:
            print(f"Error making AI request: {e}")
            return self._generate_fallback_response(prompt, feature_type)

    def _make_openai_request(self, prompt: str) -> str:
        """Make request to OpenAI API"""
        response = openai.ChatCompletion.create(
            model=self.ai_config.model_name,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=self.ai_config.max_tokens,
            temperature=self.ai_config.temperature,
            top_p=self.ai_config.top_p,
            frequency_penalty=self.ai_config.frequency_penalty,
            presence_penalty=self.ai_config.presence_penalty
        )

        # Update usage statistics
        tokens_used = response.usage.total_tokens if hasattr(response, 'usage') else 0
        self.ai_config.update_usage_stats(tokens_used)

        return response.choices[0].message.content.strip()

    def _make_anthropic_request(self, prompt: str) -> str:
        """Make request to Anthropic Claude API"""
        headers = self.ai_config.get_api_headers()

        data = {
            "model": self.ai_config.model_name,
            "max_tokens": self.ai_config.max_tokens,
            "temperature": self.ai_config.temperature,
            "messages": [{"role": "user", "content": prompt}]
        }

        response = requests.post(
            self.ai_config.get_api_url(),
            headers=headers,
            json=data,
            timeout=30
        )
        response.raise_for_status()

        result = response.json()
        self.ai_config.update_usage_stats()

        return result.get('content', [{}])[0].get('text', '')

    def _make_google_request(self, prompt: str) -> str:
        """Make request to Google Gemini API"""
        headers = self.ai_config.get_api_headers()

        data = {
            "contents": [{
                "parts": [{"text": prompt}]
            }],
            "generationConfig": {
                "temperature": self.ai_config.temperature,
                "topP": self.ai_config.top_p,
                "maxOutputTokens": self.ai_config.max_tokens
            }
        }

        response = requests.post(
            self.ai_config.get_api_url(),
            headers=headers,
            json=data,
            timeout=30
        )
        response.raise_for_status()

        result = response.json()
        self.ai_config.update_usage_stats()

        candidates = result.get('candidates', [])
        if candidates:
            return candidates[0].get('content', {}).get('parts', [{}])[0].get('text', '')
        return ''

    def _make_custom_request(self, prompt: str) -> str:
        """Make request to custom AI API"""
        headers = self.ai_config.get_api_headers()

        data = {
            "prompt": prompt,
            "max_tokens": self.ai_config.max_tokens,
            "temperature": self.ai_config.temperature,
            "top_p": self.ai_config.top_p
        }

        response = requests.post(
            self.ai_config.get_api_url(),
            headers=headers,
            json=data,
            timeout=30
        )
        response.raise_for_status()

        result = response.json()
        self.ai_config.update_usage_stats()

        # Adjust based on your custom API response format
        return result.get('response', result.get('text', result.get('content', '')))

    def _generate_fallback_response(self, prompt: str, feature_type: str) -> str:
        """Generate fallback response when AI is not available"""
        fallback_responses = {
            'content_ideas': "Here are some content ideas based on your topic:\n1. Introduction to the topic\n2. Key benefits and features\n3. Common challenges and solutions\n4. Best practices and tips\n5. Future trends and predictions",
            'grammar_improvement': "Your text looks good! Consider checking for:\n- Sentence structure variety\n- Active vs passive voice\n- Clarity and conciseness\n- Proper punctuation",
            'seo_suggestions': "SEO suggestions:\n- Include relevant keywords naturally\n- Use descriptive headings\n- Add meta description\n- Include internal and external links\n- Optimize for readability",
            'text_completion': "Continue developing your ideas with supporting details, examples, and a clear conclusion.",
            'general': "I'm currently unable to process your request. Please try again later or contact support."
        }
        return fallback_responses.get(feature_type, fallback_responses['general'])
    
    def generate_content_ideas(self, topic: str, user, count: int = 5) -> List[str]:
        """Generate content ideas based on topic"""
        preferences = self.get_user_preferences(user)
        
        prompt = f"""
        Generate {count} engaging blog post ideas about "{topic}" for a {preferences.target_audience}.
        The tone should be {preferences.preferred_tone} and style should be {preferences.preferred_style}.
        
        Return only the titles, one per line, without numbering.
        """
        
        try:
            if self.ai_config and self.ai_config.is_active and self.ai_config.enable_content_generation:
                response_text = self.make_ai_request(prompt, user, 'content_ideas')
                ideas = [idea.strip() for idea in response_text.split('\n') if idea.strip()]
                return ideas[:count] if len(ideas) > count else ideas
            else:
                return self._generate_mock_ideas(topic, count)
        except Exception as e:
            print(f"Error generating content ideas: {e}")
            return self._generate_mock_ideas(topic, count)
    
    def generate_content_outline(self, title: str, user) -> Dict:
        """Generate a content outline for a given title"""
        preferences = self.get_user_preferences(user)
        
        prompt = f"""
        Create a detailed outline for a blog post titled "{title}".
        Target audience: {preferences.target_audience}
        Tone: {preferences.preferred_tone}
        Style: {preferences.preferred_style}
        
        Return a JSON structure with:
        - introduction: brief intro points
        - main_sections: array of section titles with bullet points
        - conclusion: conclusion points
        - estimated_word_count: number
        """
        
        try:
            if self.ai_config and self.ai_config.is_active and self.ai_config.enable_content_generation:
                response_text = self.make_ai_request(prompt, user, 'content_outline')
                try:
                    return json.loads(response_text)
                except json.JSONDecodeError:
                    # If response is not JSON, create a simple outline structure
                    return {
                        "title": title,
                        "sections": [section.strip() for section in response_text.split('\n') if section.strip()]
                    }
            else:
                return self._generate_mock_outline(title)
        except Exception as e:
            print(f"Error generating outline: {e}")
            return self._generate_mock_outline(title)
    
    def improve_grammar_and_style(self, text: str, user) -> Dict:
        """Improve grammar and style of text"""
        preferences = self.get_user_preferences(user)
        
        prompt = f"""
        Improve the grammar, style, and readability of this text.
        Target tone: {preferences.preferred_tone}
        Target audience: {preferences.target_audience}
        
        Original text: "{text}"
        
        Return JSON with:
        - improved_text: the improved version
        - changes: array of objects with {{"original": "...", "improved": "...", "reason": "..."}}
        - readability_score: number from 1-10
        """
        
        try:
            if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
                response = openai.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=800,
                    temperature=0.3
                )
                return json.loads(response.choices[0].message.content)
            else:
                return self._generate_mock_improvement(text)
        except Exception as e:
            print(f"Error improving text: {e}")
            return self._generate_mock_improvement(text)
    
    def generate_seo_suggestions(self, content: str, title: str = "") -> Dict:
        """Generate SEO optimization suggestions"""
        prompt = f"""
        Analyze this content for SEO optimization:
        Title: "{title}"
        Content: "{content[:1000]}..."
        
        Return JSON with:
        - title_suggestions: array of SEO-optimized title alternatives
        - meta_description: optimized meta description (150-160 chars)
        - keywords: array of relevant keywords
        - content_suggestions: array of improvement suggestions
        - readability_issues: array of readability problems
        """
        
        try:
            if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
                response = openai.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=600,
                    temperature=0.4
                )
                return json.loads(response.choices[0].message.content)
            else:
                return self._generate_mock_seo_suggestions(content, title)
        except Exception as e:
            print(f"Error generating SEO suggestions: {e}")
            return self._generate_mock_seo_suggestions(content, title)
    
    def complete_text(self, partial_text: str, user, context: str = "") -> str:
        """Complete partial text using AI"""
        preferences = self.get_user_preferences(user)
        
        prompt = f"""
        Complete this text in a {preferences.preferred_tone} tone for {preferences.target_audience}.
        Context: {context}
        
        Partial text: "{partial_text}"
        
        Continue writing naturally, maintaining the same style and flow. 
        Write 2-3 sentences to complete the thought.
        """
        
        try:
            if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
                response = openai.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=150,
                    temperature=0.7
                )
                return response.choices[0].message.content.strip()
            else:
                return self._generate_mock_completion(partial_text)
        except Exception as e:
            print(f"Error completing text: {e}")
            return ""
    
    def analyze_readability(self, content: str) -> Dict:
        """Analyze content readability"""
        # Basic readability metrics
        word_count = len(content.split())
        sentence_count = len(re.findall(r'[.!?]+', content))
        paragraph_count = len([p for p in content.split('\n\n') if p.strip()])
        
        avg_words_per_sentence = word_count / max(sentence_count, 1)
        avg_sentences_per_paragraph = sentence_count / max(paragraph_count, 1)
        
        # Simple readability score (Flesch-like)
        readability_score = max(0, min(100, 206.835 - (1.015 * avg_words_per_sentence) - (84.6 * (word_count / max(sentence_count, 1)))))
        
        return {
            'word_count': word_count,
            'sentence_count': sentence_count,
            'paragraph_count': paragraph_count,
            'avg_words_per_sentence': round(avg_words_per_sentence, 1),
            'avg_sentences_per_paragraph': round(avg_sentences_per_paragraph, 1),
            'readability_score': round(readability_score, 1),
            'reading_level': self._get_reading_level(readability_score),
            'estimated_reading_time': max(1, word_count // 200),  # Assuming 200 WPM
            'suggestions': self._get_readability_suggestions(avg_words_per_sentence, paragraph_count, word_count)
        }
    
    def track_usage(self, user, feature_used: str, words_generated: int = 0, time_saved: int = 0):
        """Track AI feature usage for analytics"""
        now = timezone.now()
        analytics, created = AIUsageAnalytics.objects.get_or_create(
            user=user,
            feature_used=feature_used,
            year=now.year,
            month=now.month,
            defaults={
                'usage_count': 0,
                'total_words_generated': 0,
                'total_time_saved_minutes': 0
            }
        )
        
        analytics.usage_count += 1
        analytics.total_words_generated += words_generated
        analytics.total_time_saved_minutes += time_saved
        analytics.save()
    
    # Mock implementations for fallback
    def _generate_mock_ideas(self, topic: str, count: int) -> List[str]:
        return [
            f"The Ultimate Guide to {topic}",
            f"10 Common Mistakes in {topic} (And How to Avoid Them)",
            f"Why {topic} Matters More Than Ever in 2025",
            f"A Beginner's Journey into {topic}",
            f"The Future of {topic}: Trends and Predictions"
        ][:count]
    
    def _generate_mock_outline(self, title: str) -> Dict:
        return {
            "introduction": ["Hook the reader", "Introduce the topic", "Preview main points"],
            "main_sections": [
                {"title": "Background and Context", "points": ["Historical perspective", "Current situation"]},
                {"title": "Key Concepts", "points": ["Main ideas", "Important definitions"]},
                {"title": "Practical Applications", "points": ["Real-world examples", "Implementation tips"]}
            ],
            "conclusion": ["Summarize key points", "Call to action", "Future considerations"],
            "estimated_word_count": 800
        }
    
    def _generate_mock_improvement(self, text: str) -> Dict:
        return {
            "improved_text": text,  # In real implementation, this would be improved
            "changes": [],
            "readability_score": 7.5
        }
    
    def _generate_mock_seo_suggestions(self, content: str, title: str) -> Dict:
        return {
            "title_suggestions": [title + " - Complete Guide", title + " in 2025"],
            "meta_description": f"Learn everything about {title.lower()} with our comprehensive guide. Expert tips and practical advice.",
            "keywords": ["guide", "tips", "tutorial"],
            "content_suggestions": ["Add more subheadings", "Include relevant keywords"],
            "readability_issues": []
        }
    
    def _generate_mock_completion(self, partial_text: str) -> str:
        return "This is a continuation of your text that maintains the same tone and style."
    
    def _get_reading_level(self, score: float) -> str:
        if score >= 90:
            return "Very Easy"
        elif score >= 80:
            return "Easy"
        elif score >= 70:
            return "Fairly Easy"
        elif score >= 60:
            return "Standard"
        elif score >= 50:
            return "Fairly Difficult"
        elif score >= 30:
            return "Difficult"
        else:
            return "Very Difficult"
    
    def _get_readability_suggestions(self, avg_words_per_sentence: float, paragraph_count: int, word_count: int) -> List[str]:
        suggestions = []
        
        if avg_words_per_sentence > 20:
            suggestions.append("Consider breaking up long sentences for better readability")
        
        if paragraph_count < word_count / 150:
            suggestions.append("Add more paragraph breaks to improve visual flow")
        
        if word_count < 300:
            suggestions.append("Consider expanding your content for better SEO and reader value")
        
        return suggestions
