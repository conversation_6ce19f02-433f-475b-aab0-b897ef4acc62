"""
Automated Transaction Views
Handles automated PayPal transactions with email verification
"""

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from decimal import Decimal, InvalidOperation
from django.utils import timezone
import logging

from ..services.automated_transactions import AutomatedTransactionService
from ..models import WalletSettings

logger = logging.getLogger(__name__)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def automated_deposit(request):
    """
    Initiate an automated PayPal deposit with email verification
    """
    try:
        # Get and validate amount
        amount_str = request.data.get('amount')
        if not amount_str:
            return Response({
                'success': False,
                'message': 'Amount is required.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            amount = Decimal(str(amount_str))
        except (InvalidOperation, ValueError):
            return Response({
                'success': False,
                'message': 'Invalid amount format.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate amount limits
        settings = WalletSettings.get_settings()
        if amount < settings.minimum_deposit:
            return Response({
                'success': False,
                'message': f'Minimum deposit amount is ${settings.minimum_deposit}.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if amount > settings.maximum_deposit:
            return Response({
                'success': False,
                'message': f'Maximum deposit amount is ${settings.maximum_deposit}.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Check if deposits are enabled
        if not settings.deposits_enabled:
            return Response({
                'success': False,
                'message': 'Deposits are currently disabled.'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        
        # Process automated deposit
        result = AutomatedTransactionService.process_automated_deposit(
            user=request.user,
            amount=amount,
            payment_method='paypal'
        )
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Error in automated_deposit for user {request.user.id}: {str(e)}")
        return Response({
            'success': False,
            'message': 'An unexpected error occurred. Please try again.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_transaction(request):
    """
    Verify transaction using email verification code
    """
    try:
        transaction_id = request.data.get('transaction_id')
        verification_code = request.data.get('verification_code')
        transaction_type = request.data.get('transaction_type')
        
        if not all([transaction_id, verification_code, transaction_type]):
            return Response({
                'success': False,
                'message': 'Transaction ID, verification code, and transaction type are required.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Verify the transaction
        result = AutomatedTransactionService.verify_transaction_code(
            user=request.user,
            transaction_id=transaction_id,
            verification_code=verification_code,
            transaction_type=transaction_type
        )
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Error in verify_transaction for user {request.user.id}: {str(e)}")
        return Response({
            'success': False,
            'message': 'An unexpected error occurred. Please try again.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def automated_withdrawal(request):
    """
    Initiate an automated PayPal withdrawal with email verification
    """
    try:
        # Get and validate data
        amount_str = request.data.get('amount')
        paypal_email = request.data.get('paypal_email')
        
        if not amount_str or not paypal_email:
            return Response({
                'success': False,
                'message': 'Amount and PayPal email are required.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            amount = Decimal(str(amount_str))
        except (InvalidOperation, ValueError):
            return Response({
                'success': False,
                'message': 'Invalid amount format.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate amount limits
        settings = WalletSettings.get_settings()
        if amount < settings.minimum_withdrawal:
            return Response({
                'success': False,
                'message': f'Minimum withdrawal amount is ${settings.minimum_withdrawal}.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if amount > settings.maximum_withdrawal:
            return Response({
                'success': False,
                'message': f'Maximum withdrawal amount is ${settings.maximum_withdrawal}.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Check if withdrawals are enabled
        if not settings.withdrawals_enabled:
            return Response({
                'success': False,
                'message': 'Withdrawals are currently disabled.'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        
        # Process automated withdrawal
        result = AutomatedTransactionService.process_automated_withdrawal(
            user=request.user,
            amount=amount,
            paypal_email=paypal_email
        )
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Error in automated_withdrawal for user {request.user.id}: {str(e)}")
        return Response({
            'success': False,
            'message': 'An unexpected error occurred. Please try again.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def automated_purchase(request):
    """
    Process an automated purchase from wallet
    """
    try:
        # Get and validate data
        amount_str = request.data.get('amount')
        item_name = request.data.get('item_name')
        description = request.data.get('description', '')
        
        if not amount_str or not item_name:
            return Response({
                'success': False,
                'message': 'Amount and item name are required.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            amount = Decimal(str(amount_str))
        except (InvalidOperation, ValueError):
            return Response({
                'success': False,
                'message': 'Invalid amount format.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if amount <= 0:
            return Response({
                'success': False,
                'message': 'Amount must be greater than zero.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Process automated purchase
        result = AutomatedTransactionService.process_automated_purchase(
            user=request.user,
            amount=amount,
            item_name=item_name,
            description=description
        )
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Error in automated_purchase for user {request.user.id}: {str(e)}")
        return Response({
            'success': False,
            'message': 'An unexpected error occurred. Please try again.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def transaction_status(request, transaction_id):
    """
    Get the status of a transaction
    """
    try:
        from ..models import WalletDepositRequest, WalletWithdrawalRequest, WalletTransaction
        
        # Try to find the transaction in different models
        transaction = None
        transaction_type = None
        
        # Check deposit requests
        try:
            transaction = WalletDepositRequest.objects.get(id=transaction_id, wallet__user=request.user)
            transaction_type = 'deposit'
        except WalletDepositRequest.DoesNotExist:
            pass
        
        # Check withdrawal requests
        if not transaction:
            try:
                transaction = WalletWithdrawalRequest.objects.get(id=transaction_id, wallet__user=request.user)
                transaction_type = 'withdrawal'
            except WalletWithdrawalRequest.DoesNotExist:
                pass
        
        # Check completed transactions
        if not transaction:
            try:
                transaction = WalletTransaction.objects.get(id=transaction_id, wallet__user=request.user)
                transaction_type = 'transaction'
            except WalletTransaction.DoesNotExist:
                pass
        
        if not transaction:
            return Response({
                'success': False,
                'message': 'Transaction not found.'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return transaction status
        return Response({
            'success': True,
            'transaction': {
                'id': str(transaction.id),
                'type': transaction_type,
                'status': transaction.status,
                'amount': str(transaction.amount),
                'created_at': transaction.created_at.isoformat(),
                'completed_at': transaction.completed_at.isoformat() if hasattr(transaction, 'completed_at') and transaction.completed_at else None,
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in transaction_status for user {request.user.id}: {str(e)}")
        return Response({
            'success': False,
            'message': 'An unexpected error occurred.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def pending_verifications(request):
    """
    Get pending verification codes for the user
    """
    try:
        from ..models import TransactionVerificationCode
        
        pending_codes = TransactionVerificationCode.objects.filter(
            user=request.user,
            is_used=False,
            expires_at__gt=timezone.now()
        ).order_by('-created_at')
        
        verifications = []
        for code in pending_codes:
            verifications.append({
                'transaction_id': code.transaction_id,
                'transaction_type': code.transaction_type,
                'created_at': code.created_at.isoformat(),
                'expires_at': code.expires_at.isoformat(),
                'attempts': code.attempts,
                'max_attempts': code.max_attempts,
            })
        
        return Response({
            'success': True,
            'pending_verifications': verifications
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in pending_verifications for user {request.user.id}: {str(e)}")
        return Response({
            'success': False,
            'message': 'An unexpected error occurred.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
