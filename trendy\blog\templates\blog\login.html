{% extends 'blog/base.html' %}

{% block title %}Login - Trendy Blog{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="row justify-content-center min-vh-100 align-items-center">
        <div class="col-lg-5 col-md-7 col-sm-9">
            <div class="auth-card">
                <!-- <PERSON><PERSON> and <PERSON>er -->
                <div class="auth-header text-center mb-4">
                    <div class="auth-logo mb-3">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h2 class="auth-title">Welcome Back</h2>
                    <p class="auth-subtitle">Sign in to your account to continue</p>
                </div>

                <!-- Login Form -->
                <form method="post" class="auth-form">
                    {% csrf_token %}

                    <div class="form-group mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-2"></i>Username or Email
                        </label>
                        <input type="text"
                               name="username"
                               class="form-control auth-input"
                               id="username"
                               placeholder="Enter your username or email"
                               required>
                    </div>

                    <div class="form-group mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-2"></i>Password
                        </label>
                        <div class="password-input-wrapper">
                            <input type="password"
                                   name="password"
                                   class="form-control auth-input"
                                   id="password"
                                   placeholder="Enter your password"
                                   required>
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye" id="password-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group mb-3 d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                Remember me
                            </label>
                        </div>
                        <a href="#" class="auth-link">
                            Forgot password?
                        </a>
                    </div>

                    {% if form.errors %}
                    <div class="alert alert-danger auth-alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Your username and password didn't match. Please try again.
                    </div>
                    {% endif %}

                    <button type="submit" class="btn auth-btn w-100 mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                    </button>
                </form>

                <!-- Register Link -->
                <div class="auth-footer text-center">
                    <p class="mb-0">
                        Don't have an account?
                        <a href="{% url 'user-register' %}" class="auth-link fw-bold">
                            Create one here
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.auth-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
    margin-top: -80px;
}

.auth-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.auth-logo {
    width: 80px;
    height: 80px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 2rem;
    color: white;
}

.auth-title {
    color: var(--text-color);
    font-weight: 700;
    margin-bottom: 8px;
}

.auth-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}

.auth-input {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.auth-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
}

.password-input-wrapper {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
}

.auth-btn {
    background: var(--gradient);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 16px;
    color: white;
    transition: all 0.3s ease;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
    color: white;
}

.auth-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.auth-link:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

.auth-alert {
    border-radius: 12px;
    border: none;
    background: #f8d7da;
    color: #721c24;
}

@media (max-width: 768px) {
    .auth-card {
        padding: 30px 20px;
        margin: 10px;
    }
}
</style>

<script>
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const eye = document.getElementById(inputId + '-eye');

    if (input.type === 'password') {
        input.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}
</script>
{% endblock %}