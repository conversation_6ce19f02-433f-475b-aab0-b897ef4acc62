#!/usr/bin/env python
"""
Test script to verify welcome points system is working correctly
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendy.settings')
django.setup()

from django.contrib.auth import get_user_model
from gamification.models import PointTransaction, UserLevel
from django.contrib.auth.models import Group

User = get_user_model()

def test_welcome_points_system():
    """Test the welcome points system"""
    print("🧪 Testing Welcome Points System")
    print("=" * 50)
    
    # Clean up any existing test user
    test_username = 'test_welcome_user_final'
    User.objects.filter(username=test_username).delete()
    
    print(f"1. Creating test user: {test_username}")
    
    # Create a test user to verify welcome points system
    test_user = User.objects.create_user(
        username=test_username,
        email='<EMAIL>',
        password='testpass123'
    )
    
    print(f"✅ Created user: {test_user.username}")
    
    # Check if user level was created
    print("\n2. Checking user level creation...")
    try:
        user_level = UserLevel.objects.get(user=test_user)
        print(f"✅ User level created: Level {user_level.current_level}, Points: {user_level.total_points}")
    except UserLevel.DoesNotExist:
        print("❌ No user level found")
        return False
    
    # Check if welcome points were awarded
    print("\n3. Checking welcome points...")
    welcome_transactions = PointTransaction.objects.filter(
        user=test_user,
        transaction_type='welcome'
    )
    
    print(f"Welcome transactions found: {welcome_transactions.count()}")
    if welcome_transactions.exists():
        for transaction in welcome_transactions:
            print(f"  ✅ {transaction.points} points: {transaction.description}")
    else:
        print("  ❌ No welcome transactions found")
        return False
    
    # Check if user was assigned to Regular Users group
    print("\n4. Checking user group assignment...")
    user_groups = test_user.groups.all()
    print(f"User groups: {[group.name for group in user_groups]}")
    
    regular_users_group = Group.objects.filter(name='Regular Users').first()
    if regular_users_group and test_user.groups.filter(name='Regular Users').exists():
        print("  ✅ User assigned to Regular Users group")
    else:
        print("  ⚠️  User not assigned to Regular Users group (may need to run setup_user_roles)")
    
    # Verify total points match
    print("\n5. Verifying points consistency...")
    total_transaction_points = sum(t.points for t in PointTransaction.objects.filter(user=test_user))
    if user_level.total_points == total_transaction_points:
        print(f"  ✅ Points consistent: {user_level.total_points} = {total_transaction_points}")
    else:
        print(f"  ❌ Points mismatch: Level={user_level.total_points}, Transactions={total_transaction_points}")
        return False
    
    print("\n🎉 Welcome Points System Test PASSED!")
    print(f"New users will receive {user_level.total_points} welcome points automatically")
    
    # Clean up
    print(f"\n6. Cleaning up test user...")
    test_user.delete()
    print("✅ Test user deleted")
    
    return True

if __name__ == '__main__':
    success = test_welcome_points_system()
    sys.exit(0 if success else 1)
