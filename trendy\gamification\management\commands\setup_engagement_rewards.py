from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timed<PERSON>ta
from decimal import Decimal
from gamification.models import PayPalReward, PayPalSettings


class Command(BaseCommand):
    help = 'Set up PayPal rewards focused on user engagement (not content creation)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all PayPal rewards (delete existing)',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting PayPal rewards...')
            PayPalReward.objects.all().delete()
            self.stdout.write('Deleted all existing PayPal rewards')
        
        self.stdout.write('Setting up engagement-focused PayPal reward system...')
        
        # Create PayPal settings
        self.create_paypal_settings()
        
        # Create engagement-focused PayPal rewards
        self.create_engagement_rewards()
        
        # Display summary
        self.display_summary()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up engagement-focused PayPal reward system!')
        )

    def create_paypal_settings(self):
        """Create or update PayPal settings"""
        settings, created = PayPalSettings.objects.get_or_create(
            pk=1,
            defaults={
                'rewards_enabled': True,
                'minimum_payout': Decimal('5.00'),
                'maximum_monthly_payout_per_user': Decimal('150.00'),  # Increased for engagement
                'require_email_verification': True,
                'require_phone_verification': False,
                'minimum_account_age_days': 14,  # Reduced for engagement focus
                'minimum_activity_score': 50,   # Reduced threshold
                'max_claims_per_day': 3,
                'max_claims_per_month': 15,
                'cooldown_period_days': 5,      # Reduced cooldown
                'paypal_mode': 'sandbox',
                'admin_email_notifications': True,
                'user_email_notifications': True,
            }
        )
        
        if created:
            self.stdout.write('Created PayPal settings')
        else:
            self.stdout.write('PayPal settings already exist')

    def create_engagement_rewards(self):
        """Create rewards focused on user engagement, not content creation"""
        rewards = [
            # STARTER REWARDS (Build trust with new users)
            {
                'name': 'Welcome Explorer',
                'description': 'Welcome reward for reading 25 posts and making 5 comments',
                'reward_type': 'milestone',
                'points_required': 300,  # 25*5 + 5*10 = 175 points minimum
                'level_required': 2,
                'requirements': {'min_posts_read': 25, 'min_comments': 5},
                'usd_amount': Decimal('5.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 2000,
                'minimum_account_age_days': 7,
                'is_featured': True,
                'status': 'active',
            },
            
            # READING ENGAGEMENT REWARDS
            {
                'name': 'Avid Reader',
                'description': 'For reading 100 posts and engaging with the community',
                'reward_type': 'milestone',
                'points_required': 800,  # 100*5 + engagement
                'level_required': 4,
                'requirements': {'min_posts_read': 100, 'min_comments': 15, 'min_likes': 50},
                'usd_amount': Decimal('8.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 1000,
                'minimum_account_age_days': 14,
                'is_featured': True,
                'status': 'active',
            },
            
            {
                'name': 'Knowledge Seeker',
                'description': 'For reading 250 posts and active community participation',
                'reward_type': 'milestone',
                'points_required': 1800,  # 250*5 + 50*10 + 100*2 = 1950
                'level_required': 6,
                'requirements': {'min_posts_read': 250, 'min_comments': 50, 'min_likes': 100},
                'usd_amount': Decimal('12.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 600,
                'minimum_account_age_days': 30,
                'is_featured': True,
                'status': 'active',
            },
            
            # COMMUNITY ENGAGEMENT REWARDS
            {
                'name': 'Community Voice',
                'description': 'For making 75 thoughtful comments and engaging with posts',
                'reward_type': 'engagement',
                'points_required': 1500,
                'level_required': 7,
                'requirements': {'min_comments': 75, 'min_likes': 200, 'min_posts_read': 150},
                'usd_amount': Decimal('15.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 400,
                'minimum_account_age_days': 45,
                'is_featured': True,
                'status': 'active',
            },
            
            {
                'name': 'Discussion Leader',
                'description': 'For making 150 comments and building community connections',
                'reward_type': 'engagement',
                'points_required': 3000,
                'level_required': 10,
                'requirements': {'min_comments': 150, 'min_likes': 400, 'min_posts_read': 300},
                'usd_amount': Decimal('25.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 200,
                'minimum_account_age_days': 60,
                'is_featured': True,
                'status': 'active',
            },
            
            # CONSISTENCY REWARDS (Streaks)
            {
                'name': 'Daily Visitor',
                'description': 'For maintaining a 14-day reading streak',
                'reward_type': 'streak',
                'points_required': 1000,
                'level_required': 5,
                'streak_required': 14,
                'requirements': {'min_posts_read': 70},  # ~5 posts per day
                'usd_amount': Decimal('10.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 500,
                'minimum_account_age_days': 21,
                'is_featured': True,
                'status': 'active',
            },
            
            {
                'name': 'Dedicated Member',
                'description': 'For maintaining a 30-day reading streak',
                'reward_type': 'streak',
                'points_required': 2500,
                'level_required': 8,
                'streak_required': 30,
                'requirements': {'min_posts_read': 150, 'min_comments': 30},
                'usd_amount': Decimal('20.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 250,
                'minimum_account_age_days': 45,
                'is_featured': True,
                'status': 'active',
            },
            
            {
                'name': 'Super Streaker',
                'description': 'For maintaining a 60-day reading streak - incredible dedication!',
                'reward_type': 'streak',
                'points_required': 5000,
                'level_required': 12,
                'streak_required': 60,
                'requirements': {'min_posts_read': 300, 'min_comments': 60},
                'usd_amount': Decimal('35.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 100,
                'minimum_account_age_days': 75,
                'is_featured': True,
                'status': 'active',
            },
            
            # HIGH ENGAGEMENT REWARDS
            {
                'name': 'Community Champion',
                'description': 'For exceptional community engagement and interaction',
                'reward_type': 'milestone',
                'points_required': 8000,
                'level_required': 15,
                'requirements': {
                    'min_posts_read': 500,
                    'min_comments': 200,
                    'min_likes': 800,
                    'min_voice_comments': 10  # Special engagement
                },
                'usd_amount': Decimal('50.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 75,
                'minimum_account_age_days': 90,
                'is_featured': True,
                'status': 'active',
            },
            
            # LEGENDARY REWARDS
            {
                'name': 'Trendy Legend',
                'description': 'For legendary community engagement - you are a Trendy superstar!',
                'reward_type': 'milestone',
                'points_required': 15000,
                'level_required': 25,
                'requirements': {
                    'min_posts_read': 1000,
                    'min_comments': 400,
                    'min_likes': 1500,
                    'min_voice_comments': 25
                },
                'usd_amount': Decimal('100.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 25,
                'minimum_account_age_days': 180,
                'is_featured': True,
                'status': 'active',
            },
            
            # SPECIAL EVENT REWARDS
            {
                'name': 'Early Adopter Bonus',
                'description': 'Special reward for early community members who reach level 8',
                'reward_type': 'special',
                'points_required': 2000,
                'level_required': 8,
                'requirements': {'min_posts_read': 100, 'min_comments': 25},
                'usd_amount': Decimal('18.00'),
                'max_claims_per_user': 1,
                'max_total_claims': 150,
                'minimum_account_age_days': 14,
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=90),  # 3 months
                'is_featured': True,
                'status': 'limited',
            },
            
            # RECURRING REWARDS (Monthly)
            {
                'name': 'Monthly Engagement Star',
                'description': 'Monthly reward for consistent high engagement',
                'reward_type': 'milestone',
                'points_required': 800,  # Points gained in current month
                'level_required': 6,
                'requirements': {
                    'monthly_posts_read': 60,
                    'monthly_comments': 15,
                    'monthly_likes': 40
                },
                'usd_amount': Decimal('12.00'),
                'max_claims_per_user': 12,  # Once per month
                'max_total_claims': 1800,  # 150 users * 12 months
                'minimum_account_age_days': 30,
                'is_featured': False,
                'status': 'active',
            },
        ]

        for reward_data in rewards:
            # Set default values
            reward_data.setdefault('currency', 'USD')
            reward_data.setdefault('requires_verification', True)
            reward_data.setdefault('start_date', timezone.now())
            
            reward, created = PayPalReward.objects.get_or_create(
                name=reward_data['name'],
                defaults=reward_data
            )
            
            if created:
                self.stdout.write(f'Created PayPal reward: {reward.name} - ${reward.usd_amount}')
            else:
                self.stdout.write(f'PayPal reward already exists: {reward.name}')

    def display_summary(self):
        """Display a summary of created rewards"""
        rewards = PayPalReward.objects.all().order_by('points_required')
        total_potential_payout = sum(r.usd_amount * (r.max_total_claims or 1) for r in rewards)
        
        self.stdout.write('\n=== ENGAGEMENT-FOCUSED PAYPAL REWARD SUMMARY ===')
        self.stdout.write(f'Total rewards created: {rewards.count()}')
        self.stdout.write(f'Total potential payout: ${total_potential_payout}')
        
        self.stdout.write('\n=== REWARD TIERS (ENGAGEMENT FOCUSED) ===')
        for reward in rewards:
            difficulty = "🟢 Easy" if reward.points_required < 1000 else \
                        "🟡 Medium" if reward.points_required < 3000 else \
                        "🟠 Hard" if reward.points_required < 8000 else \
                        "🔴 Legendary"
            
            # Show key engagement requirements
            req_summary = []
            if 'min_posts_read' in reward.requirements:
                req_summary.append(f"{reward.requirements['min_posts_read']} posts")
            if 'min_comments' in reward.requirements:
                req_summary.append(f"{reward.requirements['min_comments']} comments")
            if 'min_likes' in reward.requirements:
                req_summary.append(f"{reward.requirements['min_likes']} likes")
            
            req_text = ", ".join(req_summary) if req_summary else "Level only"
            
            self.stdout.write(
                f'{difficulty} | ${reward.usd_amount:>6} | {reward.points_required:>6} pts | '
                f'Level {reward.level_required:>2} | {req_text[:30]:<30} | {reward.name}'
            )
