from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    InteractiveBlock, Poll, PollOption, PollVote,
    Quiz, QuizQuestion, QuizAnswer, QuizAttempt,
    CodePlayground
)

User = get_user_model()

class PollOptionSerializer(serializers.ModelSerializer):
    vote_percentage = serializers.ReadOnlyField()
    
    class Meta:
        model = PollOption
        fields = ['id', 'text', 'image_url', 'vote_count', 'vote_percentage', 'position']

class PollVoteSerializer(serializers.ModelSerializer):
    class Meta:
        model = PollVote
        fields = ['id', 'option', 'voted_at']
        read_only_fields = ['id', 'voted_at']

class PollSerializer(serializers.ModelSerializer):
    options = PollOptionSerializer(many=True, read_only=True)
    user_vote = serializers.SerializerMethodField()
    is_expired = serializers.ReadOnlyField()
    is_active = serializers.ReadOnlyField()
    
    class Meta:
        model = Poll
        fields = [
            'id', 'question', 'allow_multiple_choices', 'show_results_immediately',
            'expires_at', 'is_anonymous', 'total_votes', 'unique_voters',
            'options', 'user_vote', 'is_expired', 'is_active', 'created_at'
        ]
        read_only_fields = ['id', 'total_votes', 'unique_voters', 'created_at']
    
    def get_user_vote(self, obj):
        request = self.context.get('request')
        if request:
            if obj.is_anonymous:
                # For anonymous polls, check by IP address
                from .views import get_client_ip
                ip_address = get_client_ip(request)
                votes = obj.votes.filter(ip_address=ip_address)
            elif request.user.is_authenticated:
                # For non-anonymous polls, check by user
                votes = obj.votes.filter(user=request.user)
            else:
                return []

            return PollVoteSerializer(votes, many=True).data
        return []

class QuizAnswerSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuizAnswer
        fields = ['id', 'answer_text', 'is_correct', 'position']
        read_only_fields = ['id']

class QuizQuestionSerializer(serializers.ModelSerializer):
    answers = QuizAnswerSerializer(many=True, read_only=True)
    
    class Meta:
        model = QuizQuestion
        fields = [
            'id', 'question_type', 'question_text', 'explanation',
            'points', 'position', 'answers'
        ]
        read_only_fields = ['id']

class QuizAttemptSerializer(serializers.ModelSerializer):
    passed = serializers.ReadOnlyField()
    
    class Meta:
        model = QuizAttempt
        fields = [
            'id', 'score', 'total_points', 'earned_points',
            'time_taken', 'completed_at', 'answers', 'passed'
        ]
        read_only_fields = ['id', 'completed_at']

class QuizSerializer(serializers.ModelSerializer):
    questions = QuizQuestionSerializer(many=True, read_only=True)
    user_attempts = serializers.SerializerMethodField()
    best_attempt = serializers.SerializerMethodField()
    
    class Meta:
        model = Quiz
        fields = [
            'id', 'instructions', 'time_limit', 'show_correct_answers',
            'randomize_questions', 'passing_score', 'total_attempts',
            'average_score', 'questions', 'user_attempts', 'best_attempt',
            'created_at'
        ]
        read_only_fields = ['id', 'total_attempts', 'average_score', 'created_at']
    
    def get_user_attempts(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            attempts = obj.attempts.filter(user=request.user)[:5]  # Last 5 attempts
            return QuizAttemptSerializer(attempts, many=True).data
        return []
    
    def get_best_attempt(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            best_attempt = obj.attempts.filter(user=request.user).order_by('-score').first()
            if best_attempt:
                return QuizAttemptSerializer(best_attempt).data
        return None

# class CodePlaygroundSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = CodePlayground
#         fields = [
#             'id', 'language', 'initial_code', 'expected_output',
#             'instructions', 'is_editable', 'show_line_numbers',
#             'created_at'
#         ]
#         read_only_fields = ['id', 'created_at']

class InteractiveBlockSerializer(serializers.ModelSerializer):
    poll = PollSerializer(read_only=True)
    quiz = QuizSerializer(read_only=True)
    # code_playground = CodePlaygroundSerializer(read_only=True)
    
    class Meta:
        model = InteractiveBlock
        fields = [
            'id', 'block_type', 'title', 'description', 'position',
            'is_active', 'metadata', 'created_at', 'updated_at',
            'poll', 'quiz'  # , 'code_playground'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

# Simplified serializers for creation
class CreatePollOptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = PollOption
        fields = ['text', 'image_url', 'position']

class CreatePollSerializer(serializers.ModelSerializer):
    options = CreatePollOptionSerializer(many=True)
    
    class Meta:
        model = Poll
        fields = [
            'question', 'allow_multiple_choices', 'show_results_immediately',
            'expires_at', 'is_anonymous', 'options'
        ]
    
    def create(self, validated_data):
        options_data = validated_data.pop('options')
        poll = Poll.objects.create(**validated_data)
        
        for option_data in options_data:
            PollOption.objects.create(poll=poll, **option_data)
        
        return poll

class CreateQuizAnswerSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuizAnswer
        fields = ['answer_text', 'is_correct', 'position']

class CreateQuizQuestionSerializer(serializers.ModelSerializer):
    answers = CreateQuizAnswerSerializer(many=True)
    
    class Meta:
        model = QuizQuestion
        fields = [
            'question_type', 'question_text', 'explanation',
            'points', 'position', 'answers'
        ]

class CreateQuizSerializer(serializers.ModelSerializer):
    questions = CreateQuizQuestionSerializer(many=True)
    
    class Meta:
        model = Quiz
        fields = [
            'instructions', 'time_limit', 'show_correct_answers',
            'randomize_questions', 'passing_score', 'questions'
        ]
    
    def create(self, validated_data):
        questions_data = validated_data.pop('questions')
        quiz = Quiz.objects.create(**validated_data)
        
        for question_data in questions_data:
            answers_data = question_data.pop('answers')
            question = QuizQuestion.objects.create(quiz=quiz, **question_data)
            
            for answer_data in answers_data:
                QuizAnswer.objects.create(question=question, **answer_data)
        
        return quiz

class CreateInteractiveBlockSerializer(serializers.ModelSerializer):
    poll_data = CreatePollSerializer(required=False, allow_null=True)
    quiz_data = CreateQuizSerializer(required=False, allow_null=True)
    # code_data = CodePlaygroundSerializer(required=False, allow_null=True)
    
    class Meta:
        model = InteractiveBlock
        fields = [
            'block_type', 'title', 'description', 'position',
            'metadata', 'poll_data', 'quiz_data'  # , 'code_data'
        ]
    
    def create(self, validated_data):
        poll_data = validated_data.pop('poll_data', None)
        quiz_data = validated_data.pop('quiz_data', None)
        # code_data = validated_data.pop('code_data', None)
        
        block = InteractiveBlock.objects.create(**validated_data)
        
        if poll_data and block.block_type == 'poll':
            poll_serializer = CreatePollSerializer(data=poll_data)
            if poll_serializer.is_valid():
                poll = poll_serializer.save()
                poll.interactive_block = block
                poll.save()
        
        elif quiz_data and block.block_type == 'quiz':
            quiz_serializer = CreateQuizSerializer(data=quiz_data)
            if quiz_serializer.is_valid():
                quiz = quiz_serializer.save()
                quiz.interactive_block = block
                quiz.save()
        
        # elif code_data and block.block_type == 'code':
        #     code_serializer = CodePlaygroundSerializer(data=code_data)
        #     if code_serializer.is_valid():
        #         code = code_serializer.save()
        #         code.interactive_block = block
        #         code.save()
        
        return block
