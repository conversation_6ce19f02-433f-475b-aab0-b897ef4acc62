from django.urls import path
from . import views

app_name = 'voice_features'

urlpatterns = [
    # Voice comments
    path('posts/<int:post_id>/voice-comments/', views.voice_comments_list, name='voice-comments-list'),
    path('voice-comments/<int:comment_id>/', views.voice_comment_detail, name='voice-comment-detail'),
    path('voice-comments/<int:comment_id>/like/', views.toggle_voice_comment_like, name='toggle-voice-comment-like'),
    
    # AI writing assistance
    path('ai-writing/sessions/', views.create_ai_writing_session, name='create-ai-writing-session'),
    path('ai-writing/sessions/<str:session_id>/', views.update_ai_writing_session, name='update-ai-writing-session'),
    
    # Text-to-speech tracking
    path('posts/<int:post_id>/tts/', views.track_tts_request, name='track-tts-request'),
    
    # Statistics
    path('stats/voice-comments/', views.voice_comment_stats, name='voice-comment-stats'),
    path('stats/voice-comments/<int:post_id>/', views.voice_comment_stats, name='post-voice-comment-stats'),
    path('stats/ai-writing/', views.ai_writing_stats, name='ai-writing-stats'),
]
