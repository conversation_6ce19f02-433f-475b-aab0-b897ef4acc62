from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import make_password
from decimal import Decimal
from django.utils import timezone
from datetime import timedelta
import random

# Import all necessary models
from gamification.models import (
    Badge, UserBadge, Challenge, ChallengeParticipation,
    UserLevel, PointTransaction, PayPalReward, PayPalSettings
)
from monetization.models import (
    VirtualItem, UserVirtualItem, PointBoostPurchase,
    PremiumSubscription, MonetizationSettings, ReferralProgram, UserReferralCode
)
from wallet.models import (
    UserWallet, WalletTransaction, WalletSettings,
    WalletDepositRequest, WalletWithdrawalRequest
)
from blog.models import Post, Category, Tag, Comment
from social.models import Follow, Notification
from accounts.models import CustomUser, UserSettings
from blockchain.models import UserWalletAddress, BlockchainNetwork
from advertising.models import (
    AdNetwork, AdPlacement, AdImpression, RewardedAd,
    SponsoredPost, AdSettings
)

User = get_user_model()


class Command(BaseCommand):
    help = 'Populate database with comprehensive sample data for testing all app features'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Delete existing data before creating new data',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Starting comprehensive data population...'))
        self.stdout.write('=' * 60)

        if options['reset']:
            self.stdout.write(self.style.WARNING('🗑️  Resetting existing data...'))
            self.reset_data()

        # Core Data
        self.create_users()
        self.create_categories_and_tags()
        self.create_posts_and_comments()

        # Gamification Data
        self.create_badges()
        self.create_challenges()
        self.create_user_levels_and_points()
        self.create_paypal_rewards()

        # Monetization Data
        self.create_virtual_items()
        self.create_monetization_settings()
        self.create_referral_system()

        # Social Data
        self.create_social_interactions()

        # Wallet Data
        self.create_wallets_and_transactions()

        # Blockchain Data
        self.create_blockchain_accounts()

        # Advertising Data
        self.create_advertising_badges()
        self.create_advertising_challenges()
        self.create_advertising_interactions()

        # Settings and Preferences
        self.create_user_settings()

        self.stdout.write('=' * 60)
        self.stdout.write(self.style.SUCCESS('✅ Successfully populated comprehensive sample data!'))
        self.print_summary()

    def reset_data(self):
        """Reset existing data (use with caution)"""
        models_to_reset = [
            PointTransaction, UserBadge, ChallengeParticipation,
            WalletTransaction, Comment, Post, Follow, Notification,
            UserVirtualItem, PointBoostPurchase, PremiumSubscription,
            UserWalletAddress, UserSettings
        ]

        for model in models_to_reset:
            count = model.objects.count()
            if count > 0:
                model.objects.all().delete()
                self.stdout.write(f'  Deleted {count} {model.__name__} records')

    def create_users(self):
        """Create comprehensive test users"""
        self.stdout.write('👥 Creating test users...')

        users_data = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'User',
                'is_staff': True,
                'is_superuser': True,
                'is_email_verified': True,
                'bio': 'System administrator and content moderator'
            },
            {
                'username': 'sarah_johnson',
                'email': '<EMAIL>',
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'bio': 'Tech enthusiast and blogger. Love writing about AI and machine learning.',
                'location': 'San Francisco, CA'
            },
            {
                'username': 'mike_chen',
                'email': '<EMAIL>',
                'first_name': 'Mike',
                'last_name': 'Chen',
                'bio': 'Software developer passionate about open source and web technologies.',
                'location': 'Seattle, WA'
            },
            {
                'username': 'alex_rivera',
                'email': '<EMAIL>',
                'first_name': 'Alex',
                'last_name': 'Rivera',
                'bio': 'Digital marketing expert and content creator.',
                'location': 'Austin, TX'
            },
            {
                'username': 'emma_watson',
                'email': '<EMAIL>',
                'first_name': 'Emma',
                'last_name': 'Watson',
                'bio': 'UX designer and accessibility advocate.',
                'location': 'New York, NY'
            },
            {
                'username': 'david_kim',
                'email': '<EMAIL>',
                'first_name': 'David',
                'last_name': 'Kim',
                'bio': 'Data scientist and machine learning researcher.',
                'location': 'Boston, MA'
            }
        ]

        for user_data in users_data:
            try:
                user, created = User.objects.get_or_create(
                    username=user_data['username'],
                    defaults={
                        **user_data,
                        'password': make_password('password123'),
                        'is_active': True,
                        'is_email_verified': user_data.get('is_email_verified', True),
                        'date_joined': timezone.now() - timedelta(days=random.randint(1, 365))
                    }
                )
                if created:
                    self.stdout.write(f'  ✅ Created user: {user.username}')
                else:
                    self.stdout.write(f'  ℹ️  User already exists: {user.username}')
            except Exception as e:
                # Try to get existing user by email if username creation fails
                try:
                    user = User.objects.get(email=user_data['email'])
                    self.stdout.write(f'  ℹ️  User already exists with email: {user.username}')
                except User.DoesNotExist:
                    self.stdout.write(f'  ❌ Failed to create user {user_data["username"]}: {e}')

    def create_categories_and_tags(self):
        """Create blog categories and tags"""
        self.stdout.write('📂 Creating categories and tags...')

        categories_data = [
            {'name': 'Technology', 'description': 'Latest tech trends and innovations'},
            {'name': 'Programming', 'description': 'Coding tutorials and best practices'},
            {'name': 'AI & Machine Learning', 'description': 'Artificial intelligence and ML topics'},
            {'name': 'Web Development', 'description': 'Frontend and backend development'},
            {'name': 'Mobile Development', 'description': 'iOS and Android app development'},
            {'name': 'DevOps', 'description': 'Development operations and deployment'},
            {'name': 'Design', 'description': 'UI/UX design and user experience'},
            {'name': 'Business', 'description': 'Tech business and entrepreneurship'},
        ]

        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            if created:
                self.stdout.write(f'  ✅ Created category: {category.name}')

        tags_data = [
            'python', 'javascript', 'react', 'django', 'flutter', 'ai', 'ml',
            'blockchain', 'web3', 'api', 'database', 'cloud', 'aws', 'docker',
            'kubernetes', 'microservices', 'frontend', 'backend', 'fullstack',
            'mobile', 'ios', 'android', 'design', 'ux', 'ui', 'startup', 'saas'
        ]

        for tag_name in tags_data:
            tag, created = Tag.objects.get_or_create(name=tag_name)
            if created:
                self.stdout.write(f'  ✅ Created tag: {tag.name}')

    def create_posts_and_comments(self):
        """Create sample blog posts and comments"""
        self.stdout.write('📝 Creating posts and comments...')

        users = list(User.objects.all())
        categories = list(Category.objects.all())
        tags = list(Tag.objects.all())

        if not users or not categories:
            self.stdout.write('  ⚠️  No users or categories found, skipping posts creation')
            return

        posts_data = [
            {
                'title': 'Getting Started with Django REST Framework',
                'content': 'Django REST Framework is a powerful toolkit for building Web APIs...',
                'status': 'published',
                'is_featured': True
            },
            {
                'title': 'Flutter vs React Native: A Comprehensive Comparison',
                'content': 'When choosing a cross-platform mobile development framework...',
                'status': 'published',
                'is_featured': False
            },
            {
                'title': 'Machine Learning Fundamentals for Beginners',
                'content': 'Machine learning is transforming how we solve complex problems...',
                'status': 'published',
                'is_featured': True
            },
            {
                'title': 'Building Scalable Microservices with Docker',
                'content': 'Microservices architecture has become increasingly popular...',
                'status': 'published',
                'is_featured': False
            },
            {
                'title': 'The Future of Web Development in 2024',
                'content': 'Web development continues to evolve at a rapid pace...',
                'status': 'published',
                'is_featured': True
            }
        ]

        for i, post_data in enumerate(posts_data):
            post, created = Post.objects.get_or_create(
                title=post_data['title'],
                defaults={
                    **post_data,
                    'author': users[i % len(users)],
                    'category': categories[i % len(categories)],
                    'views': random.randint(50, 1000),
                    'created_at': timezone.now() - timedelta(days=random.randint(1, 30))
                }
            )
            if created:
                # Add random tags
                post_tags = random.sample(tags, min(3, len(tags)))
                post.tags.set(post_tags)

                # Add random likes
                likers = random.sample(users, min(random.randint(1, 5), len(users)))
                post.likes.set(likers)

                self.stdout.write(f'  ✅ Created post: {post.title}')

                # Create comments for this post
                for j in range(random.randint(1, 4)):
                    comment_author = users[j % len(users)]
                    comment, comment_created = Comment.objects.get_or_create(
                        post=post,
                        author=comment_author,
                        content=f'Great article! This is comment {j+1} on {post.title}',
                        defaults={
                            'created_at': timezone.now() - timedelta(days=random.randint(0, 10))
                        }
                    )
                    if comment_created:
                        # Add random likes to comments
                        comment_likers = random.sample(users, min(random.randint(0, 3), len(users)))
                        comment.likes.set(comment_likers)

    def create_badges(self):
        """Create achievement badges"""
        self.stdout.write('🏆 Creating badges...')

        badges_data = [
            {
                'name': 'First Post',
                'description': 'Published your first blog post',
                'badge_type': 'writing',
                'rarity': 'common',
                'icon': '✍️',
                'color': '#4CAF50',
                'points_reward': 50,
                'requirements': {'posts_count': 1}
            },
            {
                'name': 'Prolific Writer',
                'description': 'Published 10 blog posts',
                'badge_type': 'writing',
                'rarity': 'uncommon',
                'icon': '📚',
                'color': '#2196F3',
                'points_reward': 200,
                'requirements': {'posts_count': 10}
            },
            {
                'name': 'Community Favorite',
                'description': 'Received 100 likes on your posts',
                'badge_type': 'engagement',
                'rarity': 'rare',
                'icon': '❤️',
                'color': '#E91E63',
                'points_reward': 300,
                'requirements': {'total_likes': 100}
            },
            {
                'name': 'Reading Enthusiast',
                'description': 'Read 50 blog posts',
                'badge_type': 'reading',
                'rarity': 'common',
                'icon': '📖',
                'color': '#FF9800',
                'points_reward': 100,
                'requirements': {'posts_read': 50}
            },
            {
                'name': 'Early Adopter',
                'description': 'One of the first 100 users to join',
                'badge_type': 'special',
                'rarity': 'legendary',
                'icon': '🌟',
                'color': '#9C27B0',
                'points_reward': 500,
                'requirements': {'user_id_max': 100}
            },
            {
                'name': 'Ad Supporter',
                'description': 'Watched your first rewarded advertisement',
                'badge_type': 'advertising',
                'rarity': 'common',
                'icon': '📺',
                'color': '#FF5722',
                'points_reward': 25,
                'requirements': {'rewarded_ads_watched': 1}
            },
            {
                'name': 'Revenue Helper',
                'description': 'Helped generate revenue by watching ads',
                'badge_type': 'advertising',
                'rarity': 'uncommon',
                'icon': '💰',
                'color': '#4CAF50',
                'points_reward': 100,
                'requirements': {'ads_watched': 5}
            }
        ]

        for badge_data in badges_data:
            badge, created = Badge.objects.get_or_create(
                name=badge_data['name'],
                defaults=badge_data
            )
            if created:
                self.stdout.write(f'  ✅ Created badge: {badge.name}')

    def create_challenges(self):
        """Create gamification challenges"""
        self.stdout.write('🎯 Creating challenges...')

        challenges_data = [
            {
                'title': 'Weekly Writer',
                'description': 'Publish 3 posts this week',
                'challenge_type': 'weekly',
                'difficulty': 'easy',
                'points_reward': 150,
                'requirements': {'posts_count': 3, 'timeframe': 'week'},
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=7)
            },
            {
                'title': 'Community Engager',
                'description': 'Leave 10 meaningful comments',
                'challenge_type': 'daily',
                'difficulty': 'medium',
                'points_reward': 100,
                'requirements': {'comments_count': 10, 'timeframe': 'day'},
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=1)
            },
            {
                'title': 'Reading Marathon',
                'description': 'Read 20 articles this month',
                'challenge_type': 'monthly',
                'difficulty': 'hard',
                'points_reward': 500,
                'requirements': {'articles_read': 20, 'timeframe': 'month'},
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=30)
            }
        ]

        for challenge_data in challenges_data:
            challenge, created = Challenge.objects.get_or_create(
                title=challenge_data['title'],
                defaults=challenge_data
            )
            if created:
                self.stdout.write(f'  ✅ Created challenge: {challenge.title}')

    def create_user_levels_and_points(self):
        """Create user levels and point transactions"""
        self.stdout.write('📊 Creating user levels and points...')

        users = User.objects.all()
        for user in users:
            # Create user level
            total_points = random.randint(100, 2000)
            current_level = (total_points // 1000) + 1

            user_level, created = UserLevel.objects.get_or_create(
                user=user,
                defaults={
                    'total_points': total_points,
                    'current_level': current_level,
                    'reading_streak': random.randint(0, 30),
                    'writing_streak': random.randint(0, 15),
                    'engagement_streak': random.randint(0, 20),
                    'total_posts_read': random.randint(10, 100),
                    'total_posts_written': random.randint(1, 20),
                    'total_comments_made': random.randint(5, 50)
                }
            )

            if created:
                self.stdout.write(f'  ✅ Created level for user: {user.username} (Level {current_level})')

                # Create some point transactions
                transaction_types = ['welcome', 'reading', 'writing', 'engagement', 'bonus']
                for i in range(random.randint(3, 8)):
                    PointTransaction.objects.create(
                        user=user,
                        points=random.randint(10, 100),
                        transaction_type=random.choice(transaction_types),
                        description=f'Points earned from {random.choice(transaction_types)} activity',
                        created_at=timezone.now() - timedelta(days=random.randint(0, 30))
                    )

    def create_paypal_rewards(self):
        """Create sample PayPal rewards"""
        self.stdout.write('Creating PayPal rewards...')
        
        rewards_data = [
            {
                'name': 'Welcome Reward',
                'description': 'Perfect for new users to get started',
                'reward_type': 'milestone',
                'points_required': 500,
                'usd_amount': Decimal('5.00'),
                'status': 'active',
            },
            {
                'name': 'Active Reader',
                'description': 'For consistent daily engagement',
                'reward_type': 'milestone',
                'points_required': 800,
                'usd_amount': Decimal('8.00'),
                'status': 'active',
            },
            {
                'name': 'Community Contributor',
                'description': 'For active community participation',
                'reward_type': 'milestone',
                'points_required': 1200,
                'usd_amount': Decimal('12.00'),
                'status': 'active',
            },
            {
                'name': 'Super Engager',
                'description': 'For exceptional engagement levels',
                'reward_type': 'milestone',
                'points_required': 1800,
                'usd_amount': Decimal('18.00'),
                'status': 'active',
            },
            {
                'name': 'Achievement Master',
                'description': 'For completing multiple achievements',
                'reward_type': 'milestone',
                'points_required': 2000,
                'usd_amount': Decimal('20.00'),
                'status': 'active',
            },
            {
                'name': 'Streak Champion',
                'description': 'For maintaining long reading streaks',
                'reward_type': 'streak',
                'points_required': 2500,
                'usd_amount': Decimal('25.00'),
                'status': 'active',
            },
            {
                'name': 'Elite Achiever',
                'description': 'For reaching elite status',
                'reward_type': 'level',
                'points_required': 3500,
                'usd_amount': Decimal('35.00'),
                'status': 'active',
            },
            {
                'name': 'Elite Member',
                'description': 'Exclusive reward for elite members',
                'reward_type': 'level',
                'points_required': 5000,
                'usd_amount': Decimal('50.00'),
                'status': 'active',
            },
            {
                'name': 'VIP Status',
                'description': 'Premium VIP member reward',
                'reward_type': 'special',
                'points_required': 7500,
                'usd_amount': Decimal('75.00'),
                'status': 'active',
            },
            {
                'name': 'Ultimate Reward',
                'description': 'The highest tier reward available',
                'reward_type': 'special',
                'points_required': 10000,
                'usd_amount': Decimal('100.00'),
                'status': 'active',
            },
        ]
        
        for reward_data in rewards_data:
            reward, created = PayPalReward.objects.get_or_create(
                name=reward_data['name'],
                defaults=reward_data
            )
            if created:
                self.stdout.write(f'  Created reward: {reward.name}')
            else:
                self.stdout.write(f'  Reward already exists: {reward.name}')

    def create_virtual_items(self):
        """Create sample virtual items"""
        self.stdout.write('Creating virtual items...')
        
        items_data = [
            {
                'name': 'Golden Badge',
                'description': 'A shiny golden badge to show off your achievements',
                'category': 'cosmetic',
                'price': Decimal('2.99'),
                'currency': 'USD',
                'is_active': True,
                'effects': {'badge_color': 'gold', 'sparkle_effect': True},
            },
            {
                'name': 'Premium Avatar Frame',
                'description': 'Exclusive avatar frame for premium users',
                'category': 'cosmetic',
                'price': Decimal('4.99'),
                'currency': 'USD',
                'is_active': True,
                'effects': {'frame_type': 'premium', 'animated': True},
            },
            {
                'name': 'Double Points Boost',
                'description': 'Double your points for 24 hours',
                'category': 'functional',
                'price': Decimal('1.99'),
                'currency': 'USD',
                'is_active': True,
                'effects': {'point_multiplier': 2.0, 'duration_hours': 24},
            },
            {
                'name': 'Comment Highlighter',
                'description': 'Highlight your comments in discussions',
                'category': 'functional',
                'price': Decimal('3.99'),
                'currency': 'USD',
                'is_active': True,
                'effects': {'highlight_color': '#FFD700', 'duration_days': 30},
            },
            {
                'name': 'Reading Streak Shield',
                'description': 'Protect your reading streak for one day',
                'category': 'functional',
                'price': Decimal('0.99'),
                'currency': 'USD',
                'is_active': True,
                'effects': {'streak_protection': True, 'uses': 1},
            },
        ]
        
        for item_data in items_data:
            item, created = VirtualItem.objects.get_or_create(
                name=item_data['name'],
                defaults=item_data
            )
            if created:
                self.stdout.write(f'  Created item: {item.name}')
            else:
                self.stdout.write(f'  Item already exists: {item.name}')

    def create_monetization_settings(self):
        """Create monetization settings"""
        self.stdout.write('Creating monetization settings...')
        
        settings, created = MonetizationSettings.objects.get_or_create(
            id=1,
            defaults={
                'premium_monthly_price': Decimal('9.99'),
                'premium_point_multiplier': Decimal('2.0'),
                'premium_daily_bonus': 15,
                'engagement_tier_price': Decimal('2.99'),
                'achievement_tier_price': Decimal('4.99'),
                'elite_tier_price': Decimal('9.99'),
                'referral_join_points': 100,
                'referral_level_5_reward': Decimal('2.00'),
                'referral_premium_reward': Decimal('5.00'),
                'monetization_enabled': True,
                'premium_enabled': True,
                'tier_unlocks_enabled': True,
            }
        )
        
        if created:
            self.stdout.write('  Created monetization settings')
        else:
            self.stdout.write('  Monetization settings already exist')

    def create_referral_system(self):
        """Create referral codes and relationships"""
        self.stdout.write('🔗 Creating referral system...')

        users = list(User.objects.all())
        if len(users) < 2:
            self.stdout.write('  ⚠️  Not enough users for referral system')
            return

        # Create referral codes for all users
        for user in users:
            try:
                referral_code, created = UserReferralCode.objects.get_or_create(
                    user=user,
                    defaults={
                        'code': UserReferralCode.generate_unique_code(),
                        'is_active': True,
                        'usage_count': random.randint(0, 5)
                    }
                )
                if created:
                    self.stdout.write(f'  ✅ Created referral code for {user.username}: {referral_code.code}')
            except Exception:
                # Skip if UserReferralCode model doesn't exist
                pass

        # Create some referral relationships
        referrer = users[0]  # First user as main referrer
        for i, user in enumerate(users[1:4]):  # Next 3 users as referees
            if len(users) <= i + 1:
                break
            # Get the referrer's actual referral code
            try:
                referrer_code = UserReferralCode.objects.get(user=referrer).code
            except UserReferralCode.DoesNotExist:
                # Create a short code if it doesn't exist
                referrer_code = UserReferralCode.generate_unique_code()
                UserReferralCode.objects.create(user=referrer, code=referrer_code)

            referral, created = ReferralProgram.objects.get_or_create(
                referrer=referrer,
                referee=user,
                defaults={
                    'referral_code_used': referrer_code,
                    'referee_reached_level_5': i % 2 == 0,
                    'referee_went_premium': i == 0,
                    'referee_made_purchase': i != 1,
                    'total_revenue_generated': Decimal('25.99') if i == 0 else Decimal('0.00'),
                    'join_reward_given': True,
                    'level_5_reward_given': i % 2 == 0,
                    'premium_reward_given': i == 0,
                }
            )
            if created:
                self.stdout.write(f'  ✅ Created referral: {referrer.username} -> {user.username}')

    def create_sample_referrals(self):
        """Create sample users and referral relationships"""
        self.stdout.write('Creating sample referrals...')
        
        # Create sample users if they don't exist
        sample_users = [
            {'username': 'sarah_johnson', 'email': '<EMAIL>', 'first_name': 'Sarah', 'last_name': 'Johnson'},
            {'username': 'mike_chen', 'email': '<EMAIL>', 'first_name': 'Mike', 'last_name': 'Chen'},
            {'username': 'alex_rivera', 'email': '<EMAIL>', 'first_name': 'Alex', 'last_name': 'Rivera'},
        ]
        
        created_users = []
        for user_data in sample_users:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'email': user_data['email'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                }
            )
            if created:
                self.stdout.write(f'  Created user: {user.username}')
                created_users.append(user)
            else:
                self.stdout.write(f'  User already exists: {user.username}')
                created_users.append(user)
        
        # Get or create a referrer user (admin or first user)
        try:
            referrer = User.objects.filter(is_staff=True).first()
            if not referrer:
                referrer = User.objects.first()
                if not referrer:
                    referrer = User.objects.create_user(
                        username='admin',
                        email='<EMAIL>',
                        password='admin123',
                        is_staff=True
                    )
                    self.stdout.write('  Created admin user as referrer')
        except Exception:
            return
        
        # Create referral relationships
        for i, user in enumerate(created_users):
            # Get the referrer's actual referral code
            try:
                referrer_code = UserReferralCode.objects.get(user=referrer).code
            except UserReferralCode.DoesNotExist:
                # Create a short code if it doesn't exist
                referrer_code = UserReferralCode.generate_unique_code()
                UserReferralCode.objects.create(user=referrer, code=referrer_code)

            referral, created = ReferralProgram.objects.get_or_create(
                referrer=referrer,
                referee=user,
                defaults={
                    'referral_code_used': referrer_code,
                    'referee_reached_level_5': i % 2 == 0,  # Alternate
                    'referee_went_premium': i == 0,  # First user went premium
                    'referee_made_purchase': i != 1,  # All except second user made purchase
                    'total_revenue_generated': Decimal('25.99') if i == 0 else Decimal('0.00'),
                    'join_reward_given': True,
                    'level_5_reward_given': i % 2 == 0,
                    'premium_reward_given': i == 0,
                }
            )
            
            if created:
                self.stdout.write(f'  Created referral: {referrer.username} -> {user.username}')
            else:
                self.stdout.write(f'  Referral already exists: {referrer.username} -> {user.username}')

    def create_social_interactions(self):
        """Create social follows and notifications"""
        self.stdout.write('👥 Creating social interactions...')

        users = list(User.objects.all())
        if len(users) < 2:
            self.stdout.write('  ⚠️  Not enough users for social interactions')
            return

        # Create follow relationships
        for user in users:
            # Each user follows 1-3 other users
            potential_follows = [u for u in users if u != user]
            follows_count = min(random.randint(1, 3), len(potential_follows))
            to_follow = random.sample(potential_follows, follows_count)

            for followed_user in to_follow:
                follow, created = Follow.objects.get_or_create(
                    follower=user,
                    following=followed_user
                )
                if created:
                    self.stdout.write(f'  ✅ {user.username} follows {followed_user.username}')

    def create_wallets_and_transactions(self):
        """Create user wallets and transactions"""
        self.stdout.write('💰 Creating wallets and transactions...')

        users = User.objects.all()
        for user in users:
            # Create wallet
            wallet, created = UserWallet.objects.get_or_create(
                user=user,
                defaults={
                    'balance': Decimal(str(random.uniform(0, 100))),
                    'total_deposited': Decimal(str(random.uniform(0, 200))),
                    'total_withdrawn': Decimal(str(random.uniform(0, 50))),
                    'is_active': True
                }
            )

            if created:
                self.stdout.write(f'  ✅ Created wallet for: {user.username}')

                # Create some wallet transactions
                for i in range(random.randint(1, 5)):
                    transaction_type = random.choice(['deposit', 'withdrawal', 'purchase', 'reward'])
                    amount = Decimal(str(random.uniform(5, 50)))

                    WalletTransaction.objects.create(
                        wallet=wallet,
                        transaction_type=transaction_type,
                        amount=amount,
                        description=f'{transaction_type.title()} transaction',
                        status='completed',
                        created_at=timezone.now() - timedelta(days=random.randint(0, 30))
                    )

    def create_blockchain_accounts(self):
        """Create blockchain wallet addresses for users"""
        self.stdout.write('⛓️  Creating blockchain wallet addresses...')

        # Get or create a default network
        network, created = BlockchainNetwork.objects.get_or_create(
            name='polygon_testnet',
            defaults={
                'chain_id': 80001,
                'rpc_url': 'https://rpc-mumbai.maticvigil.com/',
                'explorer_url': 'https://mumbai.polygonscan.com/',
                'native_token': 'MATIC',
                'is_active': True,
                'gas_price_gwei': Decimal('20.00')
            }
        )

        users = User.objects.all()
        for user in users:
            # Create blockchain wallet address
            wallet_address, created = UserWalletAddress.objects.get_or_create(
                user=user,
                network=network,
                defaults={
                    'address': f'0x{random.randint(10**39, 10**40-1):040x}',
                    'private_key_encrypted': 'encrypted_private_key_placeholder',
                    'is_active': random.choice([True, False]),
                    'is_primary': True
                }
            )

            if created:
                self.stdout.write(f'  ✅ Created wallet address for: {user.username}')

    def create_user_settings(self):
        """Create user settings and preferences"""
        self.stdout.write('⚙️  Creating user settings...')

        users = User.objects.all()
        for user in users:
            settings, created = UserSettings.objects.get_or_create(
                user=user,
                defaults={
                    'email_notifications': random.choice([True, False]),
                    'push_notifications': random.choice([True, False]),
                    'marketing_emails': random.choice([True, False]),
                    'show_email': random.choice([True, False]),
                    'content_language': 'en',
                    'theme': random.choice(['light', 'dark', 'auto']),
                    'posts_per_page': random.choice([10, 20, 50]),
                    'auto_play_videos': random.choice([True, False])
                }
            )

            if created:
                self.stdout.write(f'  ✅ Created settings for: {user.username}')

    def create_advertising_badges(self):
        """Create advertising-related badges"""
        self.stdout.write('📺 Creating advertising badges...')

        advertising_badges = [
            {
                'name': 'Ad Watcher',
                'description': 'Watched your first advertisement',
                'badge_type': 'advertising',
                'rarity': 'common',
                'icon': '📺',
                'color': '#FF5722',
                'points_reward': 25,
                'requirements': {'ads_watched': 1}
            },
            {
                'name': 'Frequent Viewer',
                'description': 'Watched 10 advertisements',
                'badge_type': 'advertising',
                'rarity': 'uncommon',
                'icon': '🎬',
                'color': '#FF9800',
                'points_reward': 100,
                'requirements': {'ads_watched': 10}
            },
            {
                'name': 'Ad Enthusiast',
                'description': 'Watched 50 advertisements',
                'badge_type': 'advertising',
                'rarity': 'rare',
                'icon': '🏆',
                'color': '#FFC107',
                'points_reward': 250,
                'requirements': {'ads_watched': 50}
            },
            {
                'name': 'Reward Hunter',
                'description': 'Completed 25 rewarded video ads',
                'badge_type': 'advertising',
                'rarity': 'rare',
                'icon': '💎',
                'color': '#9C27B0',
                'points_reward': 300,
                'requirements': {'rewarded_ads_completed': 25}
            },
            {
                'name': 'Daily Ad Viewer',
                'description': 'Watched ads for 7 consecutive days',
                'badge_type': 'advertising',
                'rarity': 'epic',
                'icon': '🔥',
                'color': '#E91E63',
                'points_reward': 500,
                'requirements': {'daily_ad_streak': 7}
            },
            {
                'name': 'Ad Master',
                'description': 'Watched 100 advertisements',
                'badge_type': 'advertising',
                'rarity': 'legendary',
                'icon': '👑',
                'color': '#673AB7',
                'points_reward': 1000,
                'requirements': {'ads_watched': 100}
            },
            {
                'name': 'Sponsored Content Explorer',
                'description': 'Clicked on 20 sponsored posts',
                'badge_type': 'advertising',
                'rarity': 'uncommon',
                'icon': '🔍',
                'color': '#607D8B',
                'points_reward': 150,
                'requirements': {'sponsored_clicks': 20}
            },
            {
                'name': 'Revenue Generator',
                'description': 'Generated $10 in ad revenue for the platform',
                'badge_type': 'advertising',
                'rarity': 'epic',
                'icon': '💰',
                'color': '#4CAF50',
                'points_reward': 750,
                'requirements': {'revenue_generated': 10.00}
            }
        ]

        for badge_data in advertising_badges:
            badge, created = Badge.objects.get_or_create(
                name=badge_data['name'],
                defaults=badge_data
            )
            if created:
                self.stdout.write(f'  ✅ Created advertising badge: {badge.name}')

    def create_advertising_challenges(self):
        """Create advertising-related challenges"""
        self.stdout.write('🎯 Creating advertising challenges...')

        advertising_challenges = [
            {
                'title': 'Daily Ad Viewer',
                'description': 'Watch 3 advertisements today',
                'challenge_type': 'daily',
                'difficulty': 'easy',
                'points_reward': 50,
                'requirements': {'ads_watched': 3, 'timeframe': 'day'},
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=1)
            },
            {
                'title': 'Weekly Reward Hunter',
                'description': 'Complete 5 rewarded video ads this week',
                'challenge_type': 'weekly',
                'difficulty': 'medium',
                'points_reward': 200,
                'requirements': {'rewarded_ads_completed': 5, 'timeframe': 'week'},
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=7)
            },
            {
                'title': 'Sponsored Content Explorer',
                'description': 'Click on 10 sponsored posts this week',
                'challenge_type': 'weekly',
                'difficulty': 'medium',
                'points_reward': 150,
                'requirements': {'sponsored_clicks': 10, 'timeframe': 'week'},
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=7)
            },
            {
                'title': 'Monthly Ad Champion',
                'description': 'Watch 50 advertisements this month',
                'challenge_type': 'monthly',
                'difficulty': 'hard',
                'points_reward': 1000,
                'requirements': {'ads_watched': 50, 'timeframe': 'month'},
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=30)
            },
            {
                'title': 'Revenue Contributor',
                'description': 'Generate $5 in ad revenue this month',
                'challenge_type': 'monthly',
                'difficulty': 'hard',
                'points_reward': 750,
                'requirements': {'revenue_generated': 5.00, 'timeframe': 'month'},
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=30)
            },
            {
                'title': 'Ad Streak Master',
                'description': 'Watch ads for 14 consecutive days',
                'challenge_type': 'special',
                'difficulty': 'epic',
                'points_reward': 2000,
                'requirements': {'daily_ad_streak': 14, 'timeframe': 'streak'},
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=30)
            }
        ]

        for challenge_data in advertising_challenges:
            challenge, created = Challenge.objects.get_or_create(
                title=challenge_data['title'],
                defaults=challenge_data
            )
            if created:
                self.stdout.write(f'  ✅ Created advertising challenge: {challenge.title}')

    def create_advertising_interactions(self):
        """Create sample advertising interactions for users"""
        self.stdout.write('📊 Creating advertising interactions...')

        users = list(User.objects.all())
        ad_placements = list(AdPlacement.objects.all())
        ad_networks = list(AdNetwork.objects.all())

        if not users or not ad_placements or not ad_networks:
            self.stdout.write('  ⚠️  No users, ad placements, or ad networks found, skipping advertising interactions')
            return

        # Create sample ad impressions for users
        for user in users:
            # Create 5-15 ad impressions per user
            impression_count = random.randint(5, 15)

            for i in range(impression_count):
                placement = random.choice(ad_placements)
                ad_network = random.choice(ad_networks)
                impression_type = random.choice(['shown', 'clicked', 'completed'])

                # Award points for completed ads
                points_awarded = 0
                if impression_type == 'completed' and placement.placement_type == 'rewarded_video':
                    points_awarded = random.randint(5, 20)

                impression, created = AdImpression.objects.get_or_create(
                    user=user,
                    ad_placement=placement,
                    ad_network=ad_network,
                    impression_type=impression_type,
                    session_id=f'session_{user.id}_{i}',
                    defaults={
                        'points_awarded': points_awarded,
                        'revenue_amount': Decimal(str(random.uniform(0.01, 0.50))),
                        'created_at': timezone.now() - timedelta(days=random.randint(0, 30))
                    }
                )

                if created and points_awarded > 0:
                    # Create corresponding point transaction
                    PointTransaction.objects.create(
                        user=user,
                        points=points_awarded,
                        transaction_type='advertising',
                        description=f'Rewarded ad completion: {placement.name}',
                        created_at=impression.created_at
                    )

            self.stdout.write(f'  ✅ Created {impression_count} ad impressions for: {user.username}')

        # Award advertising badges to some users
        self.award_advertising_badges()

    def award_advertising_badges(self):
        """Award advertising badges based on user activity"""
        self.stdout.write('🏆 Awarding advertising badges...')

        users = User.objects.all()
        advertising_badges = Badge.objects.filter(badge_type='advertising')

        for user in users:
            user_impressions = AdImpression.objects.filter(user=user)
            ads_watched = user_impressions.count()
            rewarded_ads_completed = user_impressions.filter(
                impression_type='completed',
                ad_placement__placement_type='rewarded_video'
            ).count()
            sponsored_clicks = user_impressions.filter(
                impression_type='clicked',
                ad_placement__placement_type='sponsored_post'
            ).count()

            # Check each badge requirement
            for badge in advertising_badges:
                requirements = badge.requirements or {}
                should_award = False

                if 'ads_watched' in requirements and ads_watched >= requirements['ads_watched']:
                    should_award = True
                elif 'rewarded_ads_completed' in requirements and rewarded_ads_completed >= requirements['rewarded_ads_completed']:
                    should_award = True
                elif 'sponsored_clicks' in requirements and sponsored_clicks >= requirements['sponsored_clicks']:
                    should_award = True

                if should_award:
                    user_badge, created = UserBadge.objects.get_or_create(
                        user=user,
                        badge=badge,
                        defaults={
                            'earned_at': timezone.now() - timedelta(days=random.randint(0, 10))
                        }
                    )
                    if created:
                        # Award badge points
                        PointTransaction.objects.create(
                            user=user,
                            points=badge.points_reward,
                            transaction_type='badge',
                            description=f'Badge earned: {badge.name}',
                            created_at=user_badge.earned_at
                        )
                        self.stdout.write(f'  🏆 Awarded "{badge.name}" badge to {user.username}')

    def print_summary(self):
        """Print summary of created data"""
        self.stdout.write('\n📊 DATA SUMMARY:')
        self.stdout.write('=' * 40)

        summary_data = [
            ('Users', User.objects.count()),
            ('Categories', Category.objects.count()),
            ('Tags', Tag.objects.count()),
            ('Posts', Post.objects.count()),
            ('Comments', Comment.objects.count()),
            ('Badges', Badge.objects.count()),
            ('User Badges', UserBadge.objects.count()),
            ('Challenges', Challenge.objects.count()),
            ('User Levels', UserLevel.objects.count()),
            ('Point Transactions', PointTransaction.objects.count()),
            ('PayPal Rewards', PayPalReward.objects.count()),
            ('Virtual Items', VirtualItem.objects.count()),
            ('User Wallets', UserWallet.objects.count()),
            ('Wallet Transactions', WalletTransaction.objects.count()),
            ('Blockchain Wallet Addresses', UserWalletAddress.objects.count()),
            ('Follow Relationships', Follow.objects.count()),
            ('User Settings', UserSettings.objects.count()),
            ('Ad Networks', AdNetwork.objects.count()),
            ('Ad Placements', AdPlacement.objects.count()),
            ('Ad Impressions', AdImpression.objects.count()),
            ('Rewarded Ads', RewardedAd.objects.count()),
            ('Sponsored Posts', SponsoredPost.objects.count()),
        ]

        for name, count in summary_data:
            self.stdout.write(f'  {name}: {count}')

        self.stdout.write('=' * 40)
        self.stdout.write('🎉 Ready for comprehensive testing!')
