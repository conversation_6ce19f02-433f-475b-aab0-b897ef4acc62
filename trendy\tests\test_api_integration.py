from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from blog.models import Post, Category
from gamification.models import Badge, Challenge
from social.models import UserProfile

User = get_user_model()

class APIIntegrationTestCase(APITestCase):
    """Comprehensive API integration tests"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create test users
        self.user1 = User.objects.create_user(
            username='testuser1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create user profiles
        UserProfile.objects.create(
            user=self.user1,
            bio='Test user 1 bio',
            is_public=True
        )
        UserProfile.objects.create(
            user=self.user2,
            bio='Test user 2 bio',
            is_public=True
        )
        
        # Create test category
        self.category = Category.objects.create(
            name='Technology',
            description='Tech posts'
        )
        
        # Create test post
        self.post = Post.objects.create(
            title='Test Post',
            content='This is a test post content.',
            author=self.user1,
            category=self.category,
            status='published'
        )
        
        # Create test badge
        self.badge = Badge.objects.create(
            name='Test Badge',
            description='Test badge description',
            badge_type='reading',
            rarity='common',
            requirements={'min_posts_read': 1},
            points_reward=10
        )
        
        # Create test challenge
        from django.utils import timezone
        from datetime import timedelta
        
        self.challenge = Challenge.objects.create(
            title='Test Challenge',
            description='Test challenge description',
            challenge_type='reading',
            difficulty='easy',
            requirements={'posts_to_read': 5},
            points_reward=50,
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=7),
            duration_days=7
        )
    
    def test_blog_api_endpoints(self):
        """Test blog API endpoints"""
        # Test posts list
        response = self.client.get('/api/v1/posts/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test post detail
        response = self.client.get(f'/api/v1/posts/{self.post.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test categories
        response = self.client.get('/api/v1/categories/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_analytics_api_endpoints(self):
        """Test analytics API endpoints"""
        # Test reading session (requires authentication)
        self.client.force_authenticate(user=self.user1)
        
        response = self.client.post('/api/v1/analytics/reading-sessions/', {
            'post': self.post.id,
            'start_time': '2024-01-01T10:00:00Z',
            'end_time': '2024-01-01T10:05:00Z',
            'reading_progress': 100.0
        })
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
    
    def test_interactive_api_endpoints(self):
        """Test interactive content API endpoints"""
        # Test interactive blocks list
        response = self.client.get(f'/api/v1/interactive/posts/{self.post.id}/blocks/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_voice_api_endpoints(self):
        """Test voice features API endpoints"""
        # Test voice comments list
        response = self.client.get(f'/api/v1/voice/posts/{self.post.id}/voice-comments/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_gamification_api_endpoints(self):
        """Test gamification API endpoints"""
        # Test badges list
        response = self.client.get('/api/v1/gamification/badges/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test challenges list
        response = self.client.get('/api/v1/gamification/challenges/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test leaderboard
        response = self.client.get('/api/v1/gamification/leaderboard/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test user level (requires authentication)
        self.client.force_authenticate(user=self.user1)
        response = self.client.get('/api/v1/gamification/user/level/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_social_api_endpoints(self):
        """Test social features API endpoints"""
        # Test search
        response = self.client.get('/api/v1/social/search/?q=test')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Test recommendations
        response = self.client.get('/api/v1/social/recommendations/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Test search suggestions
        response = self.client.get('/api/v1/social/search/suggestions/?q=te')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Test user profile (requires authentication)
        self.client.force_authenticate(user=self.user1)
        response = self.client.get('/api/v1/social/profile/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Test follow user
        response = self.client.post(f'/api/v1/social/follow/{self.user2.username}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Test bookmarks
        response = self.client.get('/api/v1/social/bookmarks/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_cross_feature_integration(self):
        """Test integration between different features"""
        self.client.force_authenticate(user=self.user1)
        
        # Create a reading session
        response = self.client.post('/api/v1/analytics/reading-sessions/', {
            'post': self.post.id,
            'start_time': '2024-01-01T10:00:00Z',
            'end_time': '2024-01-01T10:05:00Z',
            'reading_progress': 100.0
        })
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check if user level was updated
        response = self.client.get('/api/v1/gamification/user/level/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Create a bookmark
        response = self.client.post('/api/v1/social/bookmarks/', {
            'post_id': self.post.id,
            'notes': 'Great article!'
        })
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check bookmarks list
        response = self.client.get('/api/v1/social/bookmarks/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_error_handling(self):
        """Test API error handling"""
        # Test non-existent post
        response = self.client.get('/api/v1/posts/99999/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        # Test unauthorized access
        response = self.client.get('/api/v1/gamification/user/level/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Test invalid data
        self.client.force_authenticate(user=self.user1)
        response = self.client.post('/api/v1/social/bookmarks/', {
            'post_id': 'invalid'
        })
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class PerformanceTestCase(TestCase):
    """Test API performance and optimization"""
    
    def setUp(self):
        self.client = Client()
        
        # Create test data
        self.user = User.objects.create_user(
            username='perftest',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.category = Category.objects.create(
            name='Performance',
            description='Performance test category'
        )
        
        # Create multiple posts for performance testing
        self.posts = []
        for i in range(50):
            post = Post.objects.create(
                title=f'Performance Test Post {i}',
                content=f'Content for performance test post {i}' * 10,
                author=self.user,
                category=self.category,
                status='published'
            )
            self.posts.append(post)
    
    def test_posts_list_performance(self):
        """Test posts list API performance"""
        import time
        
        start_time = time.time()
        response = self.client.get('/api/v1/posts/')
        end_time = time.time()
        
        self.assertEqual(response.status_code, 200)
        self.assertLess(end_time - start_time, 2.0)  # Should complete within 2 seconds
    
    def test_search_performance(self):
        """Test search API performance"""
        import time
        
        start_time = time.time()
        response = self.client.get('/api/v1/social/search/?q=performance')
        end_time = time.time()
        
        self.assertEqual(response.status_code, 200)
        self.assertLess(end_time - start_time, 3.0)  # Should complete within 3 seconds
