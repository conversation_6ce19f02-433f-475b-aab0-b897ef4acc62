#!/usr/bin/env python3
"""
Environment Variables Validation Script for Trendy Blog Platform
This script checks if all required environment variables are properly configured.
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_env_var(var_name, required=False, description=""):
    """Check if an environment variable is set"""
    value = os.getenv(var_name)
    status = "✅" if value else ("❌" if required else "⚠️")
    
    if value:
        # Mask sensitive values
        if any(sensitive in var_name.lower() for sensitive in ['key', 'secret', 'password', 'token']):
            display_value = f"{'*' * (len(value) - 4)}{value[-4:]}" if len(value) > 4 else "***"
        else:
            display_value = value
        print(f"{status} {var_name}: {display_value}")
    else:
        print(f"{status} {var_name}: Not set")
        if description:
            print(f"   Description: {description}")
    
    return bool(value)

def main():
    """Main validation function"""
    print("🔍 Trendy Blog Platform - Environment Variables Check")
    print("=" * 60)
    
    # Track validation results
    required_missing = []
    optional_missing = []
    
    # Basic Configuration (Required)
    print("\n📋 BASIC CONFIGURATION")
    print("-" * 30)
    
    basic_vars = [
        ("DEBUG", True, "Enable Django debug mode"),
        ("SECRET_KEY", True, "Django secret key for cryptographic signing"),
        ("ALLOWED_HOSTS", True, "Comma-separated list of allowed hosts"),
    ]
    
    for var_name, required, desc in basic_vars:
        if not check_env_var(var_name, required, desc):
            if required:
                required_missing.append(var_name)
            else:
                optional_missing.append(var_name)
    
    # Database Configuration
    print("\n🗄️ DATABASE CONFIGURATION")
    print("-" * 30)
    
    db_vars = [
        ("DATABASE_URL", False, "PostgreSQL connection URL (uses SQLite if not set)"),
    ]
    
    for var_name, required, desc in db_vars:
        if not check_env_var(var_name, required, desc):
            if required:
                required_missing.append(var_name)
            else:
                optional_missing.append(var_name)
    
    # Email Configuration
    print("\n📧 EMAIL CONFIGURATION")
    print("-" * 30)
    
    email_vars = [
        ("EMAIL_BACKEND", False, "Django email backend"),
        ("EMAIL_HOST", False, "SMTP server hostname"),
        ("EMAIL_PORT", False, "SMTP server port"),
        ("EMAIL_USE_TLS", False, "Enable TLS for SMTP"),
        ("EMAIL_HOST_USER", False, "SMTP username"),
        ("EMAIL_HOST_PASSWORD", False, "SMTP password/app password"),
        ("DEFAULT_FROM_EMAIL", False, "Default sender email"),
        ("SERVER_EMAIL", False, "Server error email sender"),
        ("FRONTEND_URL", False, "Frontend URL for email links"),
    ]
    
    for var_name, required, desc in email_vars:
        if not check_env_var(var_name, required, desc):
            if required:
                required_missing.append(var_name)
            else:
                optional_missing.append(var_name)
    
    # AI Configuration
    print("\n🤖 AI WRITING ASSISTANT")
    print("-" * 30)
    
    ai_vars = [
        ("OPENAI_API_KEY", False, "OpenAI API key for GPT models"),
        ("ANTHROPIC_API_KEY", False, "Anthropic API key for Claude models"),
    ]
    
    for var_name, required, desc in ai_vars:
        if not check_env_var(var_name, required, desc):
            if required:
                required_missing.append(var_name)
            else:
                optional_missing.append(var_name)
    
    # Blockchain Configuration
    print("\n⛓️ BLOCKCHAIN CONFIGURATION")
    print("-" * 30)
    
    blockchain_vars = [
        ("BLOCKCHAIN_ADMIN_PRIVATE_KEY", False, "Private key for blockchain admin operations"),
        ("WALLET_ENCRYPTION_KEY", False, "Base64 encoded key for wallet encryption"),
        ("DEFAULT_BLOCKCHAIN_NETWORK", False, "Default blockchain network"),
    ]
    
    for var_name, required, desc in blockchain_vars:
        if not check_env_var(var_name, required, desc):
            if required:
                required_missing.append(var_name)
            else:
                optional_missing.append(var_name)
    
    # PayPal Configuration
    print("\n💳 PAYPAL CONFIGURATION")
    print("-" * 30)
    
    paypal_vars = [
        ("PAYPAL_CLIENT_ID", False, "PayPal API client ID"),
        ("PAYPAL_CLIENT_SECRET", False, "PayPal API client secret"),
        ("PAYPAL_MODE", False, "PayPal environment (sandbox/live)"),
        ("PAYPAL_BUSINESS_EMAIL", False, "PayPal business account email"),
        ("PAYPAL_BUSINESS_NAME", False, "PayPal business name"),
    ]
    
    for var_name, required, desc in paypal_vars:
        if not check_env_var(var_name, required, desc):
            if required:
                required_missing.append(var_name)
            else:
                optional_missing.append(var_name)
    
    # Security Settings
    print("\n🔒 SECURITY SETTINGS")
    print("-" * 30)
    
    security_vars = [
        ("SECURE_SSL_REDIRECT", False, "Force HTTPS redirects"),
        ("SESSION_COOKIE_SECURE", False, "Secure session cookies"),
        ("CSRF_COOKIE_SECURE", False, "Secure CSRF cookies"),
    ]
    
    for var_name, required, desc in security_vars:
        if not check_env_var(var_name, required, desc):
            if required:
                required_missing.append(var_name)
            else:
                optional_missing.append(var_name)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    
    if required_missing:
        print(f"❌ Missing Required Variables ({len(required_missing)}):")
        for var in required_missing:
            print(f"   - {var}")
        print("\n⚠️  Your application may not work properly without these variables!")
    else:
        print("✅ All required environment variables are set!")
    
    if optional_missing:
        print(f"\n⚠️  Missing Optional Variables ({len(optional_missing)}):")
        for var in optional_missing:
            print(f"   - {var}")
        print("\n💡 These are optional but may be needed for full functionality.")
    
    # Check .env file existence
    env_file = Path(".env")
    if env_file.exists():
        print(f"\n📄 Environment file: {env_file.absolute()} (exists)")
    else:
        print(f"\n❌ Environment file: {env_file.absolute()} (missing)")
        print("   Copy .env.example to .env and configure your values")
    
    # Exit with appropriate code
    if required_missing:
        print(f"\n❌ Validation failed! {len(required_missing)} required variables missing.")
        sys.exit(1)
    else:
        print(f"\n✅ Validation passed! Environment is properly configured.")
        sys.exit(0)

if __name__ == "__main__":
    main()
