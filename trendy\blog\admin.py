from django.contrib import admin
from django import forms
from django.utils.html import format_html
from .models import Category, Post, Comment, PostMedia, Tag, Newsletter, HeroImage

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug')
    prepopulated_fields = {'slug': ('name',)}
    
@admin.register(HeroImage)
class HeroImageAdmin(admin.ModelAdmin):
    list_display = ('title', 'is_active', 'display_duration', 'created_at')
    list_filter = ('is_active',)
    search_fields = ('title',)

@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug')
    prepopulated_fields = {'slug': ('name',)}

@admin.register(Newsletter)
class NewsletterAdmin(admin.ModelAdmin):
    list_display = ('email', 'created_at')
    search_fields = ('email',)
    
class PostMediaAdmin(admin.ModelAdmin):
    list_display = ('post', 'media_type', 'preview', 'order')
    list_filter = ('media_type',)
    fieldsets = (
        (None, {
            'fields': ('post', 'media_type', 'order')
        }),
        ('Image Configuration', {
            'fields': ('image', 'image_url', 'caption'),
            'classes': ('collapse',)
        }),
        ('Video Configuration', {
            'fields': ('video', 'thumbnail', 'title', 'description'),
            'classes': ('collapse',)
        })
    )
    readonly_fields = ('preview',)
    
class PostMediaInline(admin.TabularInline):
    model = PostMedia
    extra = 1
    fields = ('media_type', 'preview', 'order', 'image', 'image_url', 'caption', 
             'video', 'thumbnail', 'title', 'description')
    readonly_fields = ('preview',)
    
    class Media:
        js = ('js/admin/post_media.js',)

    def preview(self, obj):
        if obj.preview_image:
            return format_html(f'<img src="{obj.preview_image}" style="max-height: 100px;" />')
        return "-"
    preview.short_description = 'Preview'

    def preview(self, obj):
        if obj.preview_image:
            return format_html(f'<img src="{obj.preview_image}" style="max-height: 100px;" />')
        return "-"
    preview.short_description = 'Preview'


class PostAdminForm(forms.ModelForm):
    tags_input = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'vTextField',
            'placeholder': 'Enter tags separated by commas'
        }),
        help_text='Enter tags separated by commas'
    )

    class Meta:
        model = Post
        fields = ('title', 'slug', 'content', 'category', 'author', 'status', 'is_featured', 'reference')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            self.fields['tags_input'].initial = ', '.join(tag.name for tag in self.instance.tags.all())

    def save(self, commit=True):
        post = super().save(commit=False)
        if commit:
            post.save()
            # Handle tags
            tags_input = self.cleaned_data.get('tags_input', '')
            if tags_input:
                tag_names = [tag.strip() for tag in tags_input.split(',') if tag.strip()]
                tags = []
                for tag_name in tag_names:
                    tag, created = Tag.objects.get_or_create(name=tag_name)
                    tags.append(tag)
                post.tags.set(tags)
            else:
                post.tags.clear()
        return post

# class PostImageInline(admin.TabularInline):
#     model = PostImage
#     extra = 1

# class PostVideoInline(admin.TabularInline):
#     model = PostVideo
#     extra = 1

@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    form = PostAdminForm
    inlines = [PostMediaInline]  # Replace old inlines with the new unified one
    list_display = ('title', 'author', 'category', 'status', 'created_at', 'is_featured')
    list_filter = ('status', 'category', 'is_featured', 'created_at')
    search_fields = ('title', 'content')
    prepopulated_fields = {'slug': ('title',)}
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)

@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = ('author', 'post', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('content', 'author__username', 'post__title')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
