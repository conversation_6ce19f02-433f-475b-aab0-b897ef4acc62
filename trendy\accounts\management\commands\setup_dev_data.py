from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import transaction
import os


class Command(BaseCommand):
    help = 'Setup development data from fixtures'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset database before loading data',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write(
                self.style.WARNING('Resetting database...')
            )
            # Flush the database
            call_command('flush', '--noinput')
            
            # Run migrations
            call_command('migrate')

        self.stdout.write('Loading development data...')
        
        fixtures_dir = 'fixtures'
        fixture_files = [
            'users_data.json',
            'wallet_data.json', 
            'blog_data.json',
        ]
        
        with transaction.atomic():
            for fixture_file in fixture_files:
                fixture_path = os.path.join(fixtures_dir, fixture_file)
                if os.path.exists(fixture_path):
                    self.stdout.write(f'Loading {fixture_file}...')
                    try:
                        call_command('loaddata', fixture_path)
                        self.stdout.write(
                            self.style.SUCCESS(f'✅ Loaded {fixture_file}')
                        )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'❌ Failed to load {fixture_file}: {e}')
                        )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️ Fixture file {fixture_file} not found')
                    )

        self.stdout.write(
            self.style.SUCCESS('✅ Development data setup completed!')
        )
