from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from blog.models import Post

User = get_user_model()

class InteractiveBlock(models.Model):
    """Base model for all interactive content blocks"""
    BLOCK_TYPES = [
        ('poll', 'Poll'),
        ('quiz', 'Quiz'),
        ('code', 'Code Playground'),
        ('chart', 'Data Visualization'),
        ('timeline', 'Timeline'),
        ('comparison', 'Comparison'),
        ('embed', 'Rich Embed'),
    ]

    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='interactive_blocks')
    block_type = models.CharField(max_length=20, choices=BLOCK_TYPES)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    position = models.PositiveIntegerField(default=0)  # Position within the post
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Metadata for different block types
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['position', 'created_at']
        indexes = [
            models.Index(fields=['post', 'block_type']),
            models.Index(fields=['post', 'position']),
        ]

    def __str__(self):
        return f"{self.get_block_type_display()}: {self.title}"

class Poll(models.Model):
    """Interactive poll system"""
    interactive_block = models.OneToOneField(InteractiveBlock, on_delete=models.CASCADE, related_name='poll')
    question = models.CharField(max_length=500)
    allow_multiple_choices = models.BooleanField(default=False)
    show_results_immediately = models.BooleanField(default=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    is_anonymous = models.BooleanField(default=False)

    # Analytics
    total_votes = models.PositiveIntegerField(default=0)
    unique_voters = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.question

    @property
    def is_expired(self):
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    @property
    def is_active(self):
        return self.interactive_block.is_active and not self.is_expired

class PollOption(models.Model):
    """Poll options/choices"""
    poll = models.ForeignKey(Poll, on_delete=models.CASCADE, related_name='options')
    text = models.CharField(max_length=200)
    image_url = models.URLField(blank=True)  # Optional image for the option
    vote_count = models.PositiveIntegerField(default=0)
    position = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['position', 'id']
        unique_together = ['poll', 'position']

    def __str__(self):
        return f"{self.poll.question} - {self.text}"

    @property
    def vote_percentage(self):
        if self.poll.total_votes == 0:
            return 0
        return (self.vote_count / self.poll.total_votes) * 100

class PollVote(models.Model):
    """Track poll votes"""
    poll = models.ForeignKey(Poll, on_delete=models.CASCADE, related_name='votes')
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)  # Null for anonymous votes
    option = models.ForeignKey(PollOption, on_delete=models.CASCADE, related_name='votes')
    ip_address = models.GenericIPAddressField()  # For anonymous vote tracking
    user_agent = models.TextField(blank=True)
    voted_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['poll', 'user']),
            models.Index(fields=['poll', 'ip_address']),
            models.Index(fields=['voted_at']),
        ]

    def __str__(self):
        voter = self.user.username if self.user else f"Anonymous ({self.ip_address})"
        return f"{voter} voted for {self.option.text}"

class Quiz(models.Model):
    """Interactive quiz system"""
    interactive_block = models.OneToOneField(InteractiveBlock, on_delete=models.CASCADE, related_name='quiz')
    instructions = models.TextField(blank=True)
    time_limit = models.PositiveIntegerField(null=True, blank=True)  # in seconds
    show_correct_answers = models.BooleanField(default=True)
    randomize_questions = models.BooleanField(default=False)
    passing_score = models.PositiveIntegerField(
        default=70,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )

    # Analytics
    total_attempts = models.PositiveIntegerField(default=0)
    average_score = models.FloatField(default=0.0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Quiz: {self.interactive_block.title}"

class QuizQuestion(models.Model):
    """Quiz questions"""
    QUESTION_TYPES = [
        ('multiple_choice', 'Multiple Choice'),
        ('true_false', 'True/False'),
        ('text_input', 'Text Input'),
        ('number_input', 'Number Input'),
    ]

    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='questions')
    question_type = models.CharField(max_length=20, choices=QUESTION_TYPES, default='multiple_choice')
    question_text = models.TextField()
    explanation = models.TextField(blank=True)  # Explanation for the correct answer
    points = models.PositiveIntegerField(default=1)
    position = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['position', 'id']

    def __str__(self):
        return f"Q{self.position + 1}: {self.question_text[:50]}..."

class QuizAnswer(models.Model):
    """Quiz answer options"""
    question = models.ForeignKey(QuizQuestion, on_delete=models.CASCADE, related_name='answers')
    answer_text = models.CharField(max_length=500)
    is_correct = models.BooleanField(default=False)
    position = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['position', 'id']

    def __str__(self):
        return f"{self.answer_text} ({'Correct' if self.is_correct else 'Incorrect'})"

class QuizAttempt(models.Model):
    """Track quiz attempts"""
    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='attempts')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='quiz_attempts')
    score = models.FloatField(default=0.0)  # Percentage score
    total_points = models.PositiveIntegerField(default=0)
    earned_points = models.PositiveIntegerField(default=0)
    time_taken = models.PositiveIntegerField(default=0)  # in seconds
    completed_at = models.DateTimeField(auto_now_add=True)

    # Store user's answers
    answers = models.JSONField(default=dict)  # {question_id: [answer_ids]}

    class Meta:
        ordering = ['-completed_at']
        indexes = [
            models.Index(fields=['quiz', 'user']),
            models.Index(fields=['completed_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.quiz.interactive_block.title} ({self.score}%)"

    @property
    def passed(self):
        return self.score >= self.quiz.passing_score

class CodePlayground(models.Model):
    """Interactive code playground"""
    LANGUAGES = [
        ('python', 'Python'),
        ('javascript', 'JavaScript'),
        ('html', 'HTML'),
        ('css', 'CSS'),
        ('sql', 'SQL'),
        ('json', 'JSON'),
        ('markdown', 'Markdown'),
    ]

    interactive_block = models.OneToOneField(InteractiveBlock, on_delete=models.CASCADE, related_name='code_playground')
    language = models.CharField(max_length=20, choices=LANGUAGES, default='python')
    initial_code = models.TextField(blank=True)
    expected_output = models.TextField(blank=True)
    instructions = models.TextField(blank=True)
    is_editable = models.BooleanField(default=True)
    show_line_numbers = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Code: {self.interactive_block.title} ({self.language})"
