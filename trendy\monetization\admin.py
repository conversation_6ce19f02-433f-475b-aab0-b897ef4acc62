from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    PremiumSubscription, PurchaseTransaction, RewardTierUnlock,
    PointBoostPurchase, StreakProtection, VirtualItem, UserVirtualItem,
    ReferralProgram, MonetizationSettings, UserReferralCode
)


@admin.register(PremiumSubscription)
class PremiumSubscriptionAdmin(admin.ModelAdmin):
    list_display = ['user', 'plan', 'status', 'start_date', 'end_date', 'days_remaining', 'total_paid']
    list_filter = ['plan', 'status', 'start_date']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['created_at', 'updated_at', 'days_remaining']
    actions = ['activate_subscriptions', 'cancel_subscriptions']
    
    fieldsets = [
        ('User Information', {
            'fields': ['user', 'plan', 'status']
        }),
        ('Subscription Period', {
            'fields': ['start_date', 'end_date', 'last_payment_date', 'next_payment_date']
        }),
        ('Payment Details', {
            'fields': ['monthly_price', 'total_paid']
        }),
        ('Premium Features', {
            'fields': ['point_multiplier', 'daily_streak_bonus', 'voice_comments_limit']
        }),
        ('Metadata', {
            'fields': ['created_at', 'updated_at', 'days_remaining'],
            'classes': ['collapse']
        }),
    ]
    
    def activate_subscriptions(self, request, queryset):
        updated = queryset.update(status='active')
        self.message_user(request, f"Activated {updated} subscriptions")
    activate_subscriptions.short_description = "Activate selected subscriptions"
    
    def cancel_subscriptions(self, request, queryset):
        updated = queryset.update(status='cancelled')
        self.message_user(request, f"Cancelled {updated} subscriptions")
    cancel_subscriptions.short_description = "Cancel selected subscriptions"


@admin.register(PurchaseTransaction)
class PurchaseTransactionAdmin(admin.ModelAdmin):
    list_display = ['user', 'transaction_type', 'item_name', 'amount', 'status', 'created_at']
    list_filter = ['transaction_type', 'status', 'created_at']
    search_fields = ['user__username', 'item_name', 'payment_transaction_id']
    readonly_fields = ['created_at', 'completed_at']
    actions = ['mark_completed', 'mark_failed', 'refund_transactions']
    
    fieldsets = [
        ('Transaction Details', {
            'fields': ['user', 'transaction_type', 'status']
        }),
        ('Purchase Information', {
            'fields': ['item_name', 'item_description', 'amount', 'currency']
        }),
        ('Payment Processing', {
            'fields': ['payment_method', 'payment_transaction_id']
        }),
        ('Timestamps', {
            'fields': ['created_at', 'completed_at']
        }),
    ]
    
    def mark_completed(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(status='completed', completed_at=timezone.now())
        self.message_user(request, f"Marked {updated} transactions as completed")
    mark_completed.short_description = "Mark as completed"
    
    def mark_failed(self, request, queryset):
        updated = queryset.update(status='failed')
        self.message_user(request, f"Marked {updated} transactions as failed")
    mark_failed.short_description = "Mark as failed"
    
    def refund_transactions(self, request, queryset):
        updated = queryset.update(status='refunded')
        self.message_user(request, f"Refunded {updated} transactions")
    refund_transactions.short_description = "Process refunds"


@admin.register(RewardTierUnlock)
class RewardTierUnlockAdmin(admin.ModelAdmin):
    list_display = ['user', 'tier', 'unlock_price', 'unlocked_at']
    list_filter = ['tier', 'unlocked_at']
    search_fields = ['user__username']
    readonly_fields = ['unlocked_at']
    
    fieldsets = [
        ('Unlock Details', {
            'fields': ['user', 'tier', 'unlock_price']
        }),
        ('Purchase Reference', {
            'fields': ['purchase_transaction']
        }),
        ('Metadata', {
            'fields': ['unlocked_at']
        }),
    ]


@admin.register(PointBoostPurchase)
class PointBoostPurchaseAdmin(admin.ModelAdmin):
    list_display = ['user', 'package', 'total_points', 'points_awarded', 'created_at']
    list_filter = ['package', 'points_awarded', 'created_at']
    search_fields = ['user__username']
    readonly_fields = ['created_at', 'awarded_at']
    actions = ['award_points']
    
    fieldsets = [
        ('Purchase Details', {
            'fields': ['user', 'package', 'purchase_transaction']
        }),
        ('Points Information', {
            'fields': ['base_points', 'bonus_points', 'total_points']
        }),
        ('Award Status', {
            'fields': ['points_awarded', 'awarded_at']
        }),
        ('Metadata', {
            'fields': ['created_at']
        }),
    ]
    
    def award_points(self, request, queryset):
        from .services import MonetizationService
        from django.utils import timezone
        
        awarded_count = 0
        for boost in queryset.filter(points_awarded=False):
            # Award points through gamification service
            from gamification.services import GamificationService
            GamificationService.award_points(
                user=boost.user,
                points=boost.total_points,
                transaction_type='purchase',
                description=f'Point boost package: {boost.package}'
            )
            
            boost.points_awarded = True
            boost.awarded_at = timezone.now()
            boost.save()
            awarded_count += 1
        
        self.message_user(request, f"Awarded points for {awarded_count} purchases")
    award_points.short_description = "Award points for selected purchases"


@admin.register(StreakProtection)
class StreakProtectionAdmin(admin.ModelAdmin):
    list_display = ['user', 'protection_type', 'protection_days', 'days_remaining', 'is_active', 'created_at']
    list_filter = ['protection_type', 'is_active', 'created_at']
    search_fields = ['user__username']
    readonly_fields = ['created_at']
    actions = ['activate_protections', 'deactivate_protections']
    
    fieldsets = [
        ('Protection Details', {
            'fields': ['user', 'protection_type', 'purchase_transaction']
        }),
        ('Protection Settings', {
            'fields': ['protection_days', 'days_remaining', 'is_active']
        }),
        ('Usage Tracking', {
            'fields': ['used_at']
        }),
        ('Metadata', {
            'fields': ['created_at', 'expires_at']
        }),
    ]
    
    def activate_protections(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f"Activated {updated} streak protections")
    activate_protections.short_description = "Activate selected protections"
    
    def deactivate_protections(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {updated} streak protections")
    deactivate_protections.short_description = "Deactivate selected protections"


@admin.register(VirtualItem)
class VirtualItemAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'price', 'is_active', 'is_limited_time', 'max_purchases_per_user']
    list_filter = ['category', 'is_active', 'is_limited_time']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    actions = ['activate_items', 'deactivate_items']
    
    fieldsets = [
        ('Item Information', {
            'fields': ['name', 'description', 'category']
        }),
        ('Pricing', {
            'fields': ['price', 'currency']
        }),
        ('Availability', {
            'fields': ['is_active', 'is_limited_time', 'max_purchases_per_user']
        }),
        ('Item Effects', {
            'fields': ['effects']
        }),
        ('Metadata', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    def activate_items(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f"Activated {updated} virtual items")
    activate_items.short_description = "Activate selected items"
    
    def deactivate_items(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {updated} virtual items")
    deactivate_items.short_description = "Deactivate selected items"


@admin.register(UserVirtualItem)
class UserVirtualItemAdmin(admin.ModelAdmin):
    list_display = ['user', 'item', 'is_active', 'uses_remaining', 'purchased_at', 'expires_at']
    list_filter = ['is_active', 'purchased_at', 'item__category']
    search_fields = ['user__username', 'item__name']
    readonly_fields = ['purchased_at']
    actions = ['activate_items', 'deactivate_items']
    
    fieldsets = [
        ('Ownership Details', {
            'fields': ['user', 'item', 'purchase_transaction']
        }),
        ('Usage Tracking', {
            'fields': ['is_active', 'uses_remaining']
        }),
        ('Timestamps', {
            'fields': ['purchased_at', 'expires_at']
        }),
    ]
    
    def activate_items(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f"Activated {updated} user items")
    activate_items.short_description = "Activate selected items"
    
    def deactivate_items(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {updated} user items")
    deactivate_items.short_description = "Deactivate selected items"


@admin.register(UserReferralCode)
class UserReferralCodeAdmin(admin.ModelAdmin):
    list_display = ['user', 'code', 'usage_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['user__username', 'code']
    readonly_fields = ['code', 'created_at', 'usage_count']


@admin.register(ReferralProgram)
class ReferralProgramAdmin(admin.ModelAdmin):
    list_display = ['referrer', 'referee', 'referral_code_used', 'referee_reached_level_5', 'referee_went_premium', 'total_revenue_generated']
    list_filter = ['referee_reached_level_5', 'referee_went_premium', 'referee_made_purchase', 'referee_joined_at']
    search_fields = ['referrer__username', 'referee__username', 'referral_code_used']
    readonly_fields = ['referee_joined_at', 'total_revenue_generated']
    actions = ['process_level_5_rewards', 'process_premium_rewards']
    
    fieldsets = [
        ('Referral Details', {
            'fields': ['referrer', 'referee', 'referral_code']
        }),
        ('Milestone Tracking', {
            'fields': ['referee_reached_level_5', 'referee_went_premium', 'referee_made_purchase']
        }),
        ('Rewards Given', {
            'fields': ['join_reward_given', 'level_5_reward_given', 'premium_reward_given']
        }),
        ('Revenue Tracking', {
            'fields': ['total_revenue_generated']
        }),
        ('Metadata', {
            'fields': ['referee_joined_at']
        }),
    ]
    
    def process_level_5_rewards(self, request, queryset):
        from .services import MonetizationService
        
        processed_count = 0
        for referral in queryset.filter(referee_reached_level_5=True, level_5_reward_given=False):
            success, message = MonetizationService.process_referral_milestone(
                referral.referrer, referral.referee, 'level_5'
            )
            if success:
                processed_count += 1
        
        self.message_user(request, f"Processed level 5 rewards for {processed_count} referrals")
    process_level_5_rewards.short_description = "Process level 5 rewards"
    
    def process_premium_rewards(self, request, queryset):
        from .services import MonetizationService
        
        processed_count = 0
        for referral in queryset.filter(referee_went_premium=True, premium_reward_given=False):
            success, message = MonetizationService.process_referral_milestone(
                referral.referrer, referral.referee, 'premium'
            )
            if success:
                processed_count += 1
        
        self.message_user(request, f"Processed premium rewards for {processed_count} referrals")
    process_premium_rewards.short_description = "Process premium rewards"


@admin.register(MonetizationSettings)
class MonetizationSettingsAdmin(admin.ModelAdmin):
    fieldsets = [
        ('Premium Subscription', {
            'fields': ['premium_monthly_price', 'premium_point_multiplier', 'premium_daily_bonus']
        }),
        ('Tier Unlock Pricing', {
            'fields': ['engagement_tier_price', 'achievement_tier_price', 'elite_tier_price']
        }),
        ('Referral Rewards', {
            'fields': ['referral_join_points', 'referral_level_5_reward', 'referral_premium_reward']
        }),
        ('System Controls', {
            'fields': ['monetization_enabled', 'premium_enabled', 'tier_unlocks_enabled']
        }),
        ('Metadata', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['created_at', 'updated_at']
    
    def has_add_permission(self, request):
        # Only allow one settings instance
        return not MonetizationSettings.objects.exists()
    
    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of settings
        return False
