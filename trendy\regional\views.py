"""
Regional content filtering views
"""

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.contrib.auth import get_user_model

from .models import Country, Region
from .services import LocationDetectionService, RegionalContentService
from .serializers import (
    CountrySerializer, CountrySimpleSerializer, RegionSerializer,
    RegionalPreferencesSerializer, SetPreferredCountrySerializer,
    RegionalStatsSerializer, UserLocationHistorySerializer
)

User = get_user_model()


@api_view(['GET'])
@permission_classes([AllowAny])
def get_countries(request):
    """Get list of available countries"""
    try:
        countries = LocationDetectionService.get_available_countries()
        serializer = CountrySerializer(countries, many=True)

        return Response({
            'success': True,
            'countries': serializer.data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_regions(request):
    """Get list of available regions with countries"""
    try:
        regions = Region.objects.filter(is_active=True).prefetch_related('countries')
        serializer = RegionSerializer(regions, many=True)
        
        return Response({
            'success': True,
            'regions': serializer.data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_regional_preferences(request):
    """Get user's regional preferences and detected location"""
    try:
        user = request.user
        
        # Attempt location detection if needed
        LocationDetectionService.get_or_detect_user_country(user, request)
        
        # Refresh user from database to get updated detected_country
        user.refresh_from_db()
        
        serializer = RegionalPreferencesSerializer(user)
        
        return Response({
            'success': True,
            'preferences': serializer.data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def set_preferred_country(request):
    """Set user's preferred country"""
    try:
        serializer = SetPreferredCountrySerializer(data=request.data)
        if serializer.is_valid():
            country_code = serializer.validated_data.get('country_code')
            success = LocationDetectionService.set_user_preferred_country(
                request.user, country_code
            )

            if success:
                message = 'Preferred country cleared successfully' if country_code is None else 'Preferred country updated successfully'
                return Response({
                    'success': True,
                    'message': message
                })
            else:
                return Response({
                    'success': False,
                    'error': 'Failed to update preferred country'
                }, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({
                'success': False,
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_regional_settings(request):
    """Update user's regional content settings"""
    try:
        user = request.user
        
        # Update settings
        if 'show_global_content' in request.data:
            user.show_global_content = request.data['show_global_content']
        
        if 'auto_detect_location' in request.data:
            user.auto_detect_location = request.data['auto_detect_location']
        
        user.save(update_fields=['show_global_content', 'auto_detect_location'])
        
        return Response({
            'success': True,
            'message': 'Regional settings updated successfully'
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def detect_location(request):
    """Manually trigger location detection"""
    try:
        user = request.user

        # Check if a test IP is provided (for development/testing)
        test_ip = request.data.get('test_ip') if hasattr(request, 'data') else None

        if test_ip:
            # Use provided test IP for location detection
            location_data = LocationDetectionService.detect_country_from_ip(test_ip)
            if location_data:
                try:
                    from .models import Country
                    country = Country.objects.get(
                        code=location_data['country_code'],
                        is_active=True
                    )

                    # Update user's detected country
                    user.detected_country = country
                    user.save(update_fields=['detected_country'])

                    return Response({
                        'success': True,
                        'detected_country': CountrySimpleSerializer(country).data,
                        'message': f'Location detected: {country.name} (using test IP: {test_ip})'
                    })
                except Country.DoesNotExist:
                    pass

        # Attempt normal location detection
        detected_country = LocationDetectionService.detect_and_save_location(user, request)

        if detected_country:
            return Response({
                'success': True,
                'detected_country': CountrySimpleSerializer(detected_country).data,
                'message': f'Location detected: {detected_country.name}'
            })
        else:
            return Response({
                'success': False,
                'message': 'Could not detect location from your IP address. You can manually select a country or use global content.',
                'debug_info': {
                    'ip_detected': LocationDetectionService.get_client_ip(request),
                    'suggestion': 'Try using a VPN or mobile data, or manually select your country'
                }
            }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def detect_location_with_fallback(request):
    """Enhanced location detection with multiple fallback methods"""
    try:
        user = request.user

        # Method 1: Try with request IP
        detected_country = LocationDetectionService.detect_and_save_location(user, request)
        if detected_country:
            return Response({
                'success': True,
                'detected_country': CountrySimpleSerializer(detected_country).data,
                'message': f'Location detected: {detected_country.name}',
                'method': 'request_ip'
            })

        # Method 2: Try with public IP service
        public_ip = LocationDetectionService.get_public_ip()
        if public_ip:
            location_data = LocationDetectionService.detect_country_from_ip(public_ip)
            if location_data:
                try:
                    country = Country.objects.get(
                        code=location_data['country_code'],
                        is_active=True
                    )

                    # Update user's detected country
                    user.detected_country = country
                    user.save(update_fields=['detected_country'])

                    # Save location history
                    from .models import UserLocationHistory
                    UserLocationHistory.objects.create(
                        user=user,
                        detected_country=country,
                        ip_address=public_ip,
                        detection_method='public_ip_service',
                        confidence_score=location_data.get('confidence', 0.7)
                    )

                    return Response({
                        'success': True,
                        'detected_country': CountrySimpleSerializer(country).data,
                        'message': f'Location detected: {country.name} (using public IP service)',
                        'method': 'public_ip_service'
                    })
                except Country.DoesNotExist:
                    pass

        # Method 3: Allow manual IP input from client
        client_ip = request.data.get('client_ip') if hasattr(request, 'data') else None
        if client_ip:
            location_data = LocationDetectionService.detect_country_from_ip(client_ip)
            if location_data:
                try:
                    country = Country.objects.get(
                        code=location_data['country_code'],
                        is_active=True
                    )

                    # Update user's detected country
                    user.detected_country = country
                    user.save(update_fields=['detected_country'])

                    return Response({
                        'success': True,
                        'detected_country': CountrySimpleSerializer(country).data,
                        'message': f'Location detected: {country.name} (using client IP: {client_ip})',
                        'method': 'client_provided_ip'
                    })
                except Country.DoesNotExist:
                    pass

        # All methods failed
        return Response({
            'success': False,
            'message': 'Could not detect location using any available method. Please manually select your country.',
            'debug_info': {
                'request_ip': LocationDetectionService.get_client_ip(request),
                'public_ip': public_ip,
                'client_ip': client_ip,
                'suggestion': 'Try using mobile data instead of WiFi, or manually select your country'
            }
        }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_regional_stats(request):
    """Get regional content statistics for user"""
    try:
        user = request.user
        stats = RegionalContentService.get_regional_stats(user)
        serializer = RegionalStatsSerializer(stats)
        
        return Response({
            'success': True,
            'stats': serializer.data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_location_history(request):
    """Get user's location detection history"""
    try:
        user = request.user
        history = user.location_history.all()[:10]  # Last 10 detections
        serializer = UserLocationHistorySerializer(history, many=True)
        
        return Response({
            'success': True,
            'history': serializer.data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
