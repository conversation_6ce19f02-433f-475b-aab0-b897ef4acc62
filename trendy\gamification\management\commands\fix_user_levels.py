from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from gamification.models import UserLevel
from gamification.services import GamificationService

User = get_user_model()


class Command(BaseCommand):
    help = 'Create UserLevel records for users who don\'t have them'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating it',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # Get all users without UserLevel records
        users_without_levels = User.objects.filter(level__isnull=True)
        
        if not users_without_levels.exists():
            self.stdout.write(
                self.style.SUCCESS('All users already have UserLevel records!')
            )
            return
        
        self.stdout.write(
            f'Found {users_without_levels.count()} users without UserLevel records'
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN - No changes will be made')
            )
            for user in users_without_levels:
                self.stdout.write(f'Would create UserLevel for: {user.username}')
            return
        
        created_count = 0
        for user in users_without_levels:
            try:
                user_level = GamificationService.get_or_create_user_level(user)
                if user_level:
                    created_count += 1
                    self.stdout.write(
                        f'Created UserLevel for: {user.username}'
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error creating UserLevel for {user.username}: {e}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_count} UserLevel records!'
            )
        )
