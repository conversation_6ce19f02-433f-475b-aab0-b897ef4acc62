# Generated by Django 5.1.7 on 2025-06-24 22:23

import django.db.models.deletion
import django.utils.timezone
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AdNetwork',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('network_type', models.CharField(choices=[('google_admob', 'Google AdMob'), ('facebook_audience', 'Facebook Audience Network'), ('unity_ads', 'Unity Ads'), ('applovin', 'AppLovin'), ('ironSource', 'ironSource'), ('custom', 'Custom Network')], max_length=20)),
                ('app_id', models.CharField(blank=True, max_length=200)),
                ('api_key', models.CharField(blank=True, max_length=200)),
                ('secret_key', models.CharField(blank=True, max_length=200)),
                ('revenue_share_percentage', models.DecimalField(decimal_places=2, default=Decimal('70.00'), max_digits=5)),
                ('is_active', models.BooleanField(default=True)),
                ('priority', models.PositiveIntegerField(default=1)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['priority', 'name'],
            },
        ),
        migrations.CreateModel(
            name='AdSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ads_enabled', models.BooleanField(default=True)),
                ('show_ads_to_premium', models.BooleanField(default=False)),
                ('max_ads_per_session', models.PositiveIntegerField(default=10)),
                ('min_time_between_ads', models.PositiveIntegerField(default=30)),
                ('rewarded_ads_enabled', models.BooleanField(default=True)),
                ('max_rewarded_ads_per_day', models.PositiveIntegerField(default=5)),
                ('rewarded_ad_cooldown', models.PositiveIntegerField(default=300)),
                ('revenue_share_with_users', models.DecimalField(decimal_places=2, default=Decimal('10.00'), max_digits=5)),
                ('minimum_payout_threshold', models.DecimalField(decimal_places=2, default=Decimal('1.00'), max_digits=10)),
                ('sponsored_posts_enabled', models.BooleanField(default=True)),
                ('sponsored_post_frequency', models.PositiveIntegerField(default=5)),
                ('enable_user_targeting', models.BooleanField(default=True)),
                ('enable_behavioral_targeting', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Ad Settings',
                'verbose_name_plural': 'Ad Settings',
            },
        ),
        migrations.CreateModel(
            name='AdNetworkPlacement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ad_unit_id', models.CharField(max_length=200)),
                ('weight', models.PositiveIntegerField(default=100)),
                ('total_requests', models.PositiveIntegerField(default=0)),
                ('total_impressions', models.PositiveIntegerField(default=0)),
                ('total_clicks', models.PositiveIntegerField(default=0)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('ad_network', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='advertising.adnetwork')),
            ],
        ),
        migrations.CreateModel(
            name='AdPlacement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('placement_type', models.CharField(choices=[('banner', 'Banner Ad'), ('interstitial', 'Interstitial (Full Screen)'), ('rewarded_video', 'Rewarded Video'), ('native', 'Native Ad'), ('sponsored_post', 'Sponsored Post')], max_length=20)),
                ('location', models.CharField(choices=[('home_feed_top', 'Home Feed - Top'), ('home_feed_middle', 'Home Feed - Between Posts'), ('home_feed_bottom', 'Home Feed - Bottom'), ('post_detail_top', 'Post Detail - Top'), ('post_detail_bottom', 'Post Detail - Bottom'), ('comments_section', 'Comments Section'), ('profile_page', 'Profile Page'), ('rewards_page', 'Rewards Page'), ('before_reward_claim', 'Before Reward Claim'), ('app_launch', 'App Launch'), ('level_up', 'Level Up Screen'), ('daily_bonus', 'Daily Bonus Screen')], max_length=30)),
                ('is_active', models.BooleanField(default=True)),
                ('show_to_premium_users', models.BooleanField(default=False)),
                ('frequency_cap', models.PositiveIntegerField(default=3)),
                ('estimated_cpm', models.DecimalField(decimal_places=2, default=Decimal('2.00'), max_digits=10)),
                ('estimated_ctr', models.DecimalField(decimal_places=2, default=Decimal('1.50'), max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('ad_networks', models.ManyToManyField(through='advertising.AdNetworkPlacement', to='advertising.adnetwork')),
            ],
            options={
                'ordering': ['location', 'placement_type'],
                'unique_together': {('placement_type', 'location')},
            },
        ),
        migrations.AddField(
            model_name='adnetworkplacement',
            name='ad_placement',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='advertising.adplacement'),
        ),
        migrations.CreateModel(
            name='RewardedAd',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('reward_type', models.CharField(choices=[('points', 'Points Reward'), ('streak_protection', 'Streak Protection'), ('premium_trial', 'Premium Trial'), ('tier_unlock_discount', 'Tier Unlock Discount')], max_length=30)),
                ('points_reward', models.PositiveIntegerField(default=10)),
                ('premium_trial_days', models.PositiveIntegerField(default=0)),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('video_duration_seconds', models.PositiveIntegerField(default=30)),
                ('max_views_per_user_per_day', models.PositiveIntegerField(default=5)),
                ('max_total_views_per_day', models.PositiveIntegerField(default=1000)),
                ('revenue_per_view', models.DecimalField(decimal_places=4, default=Decimal('0.05'), max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('ad_placement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='advertising.adplacement')),
            ],
            options={
                'ordering': ['-points_reward'],
            },
        ),
        migrations.CreateModel(
            name='SponsoredPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sponsor_name', models.CharField(max_length=200)),
                ('sponsor_type', models.CharField(choices=[('brand', 'Brand Sponsorship'), ('app', 'App Promotion'), ('service', 'Service Promotion'), ('affiliate', 'Affiliate Marketing')], max_length=20)),
                ('sponsor_logo', models.URLField(blank=True)),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('image_url', models.URLField(blank=True)),
                ('video_url', models.URLField(blank=True)),
                ('call_to_action', models.CharField(default='Learn More', max_length=100)),
                ('target_url', models.URLField()),
                ('target_user_levels', models.JSONField(default=list)),
                ('target_premium_users', models.BooleanField(default=False)),
                ('target_locations', models.JSONField(default=list)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('paused', 'Paused'), ('completed', 'Completed'), ('expired', 'Expired')], default='draft', max_length=20)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField()),
                ('total_budget', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cost_per_impression', models.DecimalField(decimal_places=4, default=Decimal('0.01'), max_digits=10)),
                ('cost_per_click', models.DecimalField(decimal_places=4, default=Decimal('0.50'), max_digits=10)),
                ('total_impressions', models.PositiveIntegerField(default=0)),
                ('total_clicks', models.PositiveIntegerField(default=0)),
                ('total_spent', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('max_impressions_per_user', models.PositiveIntegerField(default=3)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['status', 'start_date'], name='advertising_status_629e47_idx'), models.Index(fields=['sponsor_type'], name='advertising_sponsor_50e043_idx')],
            },
        ),
        migrations.AlterUniqueTogether(
            name='adnetworkplacement',
            unique_together={('ad_network', 'ad_placement')},
        ),
        migrations.CreateModel(
            name='AdImpression',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('impression_type', models.CharField(choices=[('shown', 'Ad Shown'), ('clicked', 'Ad Clicked'), ('completed', 'Video Completed'), ('rewarded', 'Reward Given'), ('failed', 'Ad Failed')], max_length=20)),
                ('impression_id', models.CharField(default=uuid.uuid4, max_length=100, unique=True)),
                ('revenue_amount', models.DecimalField(decimal_places=4, default=Decimal('0.00'), max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('points_awarded', models.PositiveIntegerField(default=0)),
                ('session_id', models.CharField(blank=True, max_length=100)),
                ('device_info', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ad_impressions', to=settings.AUTH_USER_MODEL)),
                ('ad_network', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='advertising.adnetwork')),
                ('ad_placement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='advertising.adplacement')),
                ('rewarded_ad', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='advertising.rewardedad')),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'created_at'], name='advertising_user_id_2a1a46_idx'), models.Index(fields=['ad_placement', 'impression_type'], name='advertising_ad_plac_e2d9e3_idx'), models.Index(fields=['created_at'], name='advertising_created_8c08e1_idx')],
            },
        ),
    ]
