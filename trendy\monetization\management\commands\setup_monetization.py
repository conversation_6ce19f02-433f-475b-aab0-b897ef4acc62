from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
from monetization.models import MonetizationSettings, VirtualItem


class Command(BaseCommand):
    help = 'Set up monetization system with premium features and virtual items'

    def handle(self, *args, **options):
        self.stdout.write('Setting up monetization system...')
        
        # Create monetization settings
        self.create_monetization_settings()
        
        # Create virtual items
        self.create_virtual_items()
        
        # Display summary
        self.display_summary()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up monetization system!')
        )

    def create_monetization_settings(self):
        """Create or update monetization settings"""
        settings, created = MonetizationSettings.objects.get_or_create(
            pk=1,
            defaults={
                # Premium subscription
                'premium_monthly_price': Decimal('9.99'),
                'premium_point_multiplier': Decimal('2.0'),
                'premium_daily_bonus': 15,
                
                # Tier unlock prices
                'engagement_tier_price': Decimal('2.99'),
                'achievement_tier_price': Decimal('4.99'),
                'elite_tier_price': Decimal('9.99'),
                
                # Referral rewards
                'referral_join_points': 100,
                'referral_level_5_reward': Decimal('2.00'),
                'referral_premium_reward': Decimal('5.00'),
                
                # System controls
                'monetization_enabled': True,
                'premium_enabled': True,
                'tier_unlocks_enabled': True,
            }
        )
        
        if created:
            self.stdout.write('Created monetization settings')
        else:
            self.stdout.write('Monetization settings already exist')

    def create_virtual_items(self):
        """Create virtual items for purchase"""
        items = [
            # COSMETIC ITEMS
            {
                'name': 'Golden Profile Theme',
                'description': 'Luxurious golden background for your profile',
                'category': 'cosmetic',
                'price': Decimal('1.99'),
                'effects': {
                    'profile_theme': 'golden',
                    'theme_color': '#FFD700'
                },
                'is_active': True,
            },
            {
                'name': 'Diamond Achievement Frame',
                'description': 'Sparkling diamond border for your badges',
                'category': 'cosmetic',
                'price': Decimal('2.99'),
                'effects': {
                    'badge_frame': 'diamond',
                    'frame_animation': 'sparkle'
                },
                'is_active': True,
            },
            {
                'name': 'Premium Emoji Pack',
                'description': 'Exclusive emoji reactions for comments',
                'category': 'cosmetic',
                'price': Decimal('0.99'),
                'effects': {
                    'emoji_pack': 'premium',
                    'emoji_count': 50
                },
                'is_active': True,
            },
            {
                'name': 'Rainbow Username',
                'description': 'Colorful rainbow effect for your username',
                'category': 'cosmetic',
                'price': Decimal('1.99'),
                'effects': {
                    'username_color': 'rainbow',
                    'color_animation': 'gradient'
                },
                'is_active': True,
            },
            {
                'name': 'VIP Profile Badge',
                'description': 'Exclusive VIP badge shown on your profile',
                'category': 'cosmetic',
                'price': Decimal('4.99'),
                'effects': {
                    'profile_badge': 'vip',
                    'badge_glow': True
                },
                'is_active': True,
            },
            
            # FUNCTIONAL ITEMS
            {
                'name': 'Double XP Weekend',
                'description': '2x points for all activities for 48 hours',
                'category': 'temporary',
                'price': Decimal('3.99'),
                'effects': {
                    'point_multiplier': 2.0,
                    'duration_hours': 48,
                    'applies_to': 'all_activities'
                },
                'is_active': True,
            },
            {
                'name': 'Instant Level Up',
                'description': 'Skip directly to the next level',
                'category': 'functional',
                'price': Decimal('4.99'),
                'effects': {
                    'level_boost': 1,
                    'instant': True
                },
                'is_active': True,
                'max_purchases_per_user': 5,  # Limit to prevent abuse
            },
            {
                'name': 'Reward Progress Hint',
                'description': 'See exactly how close you are to your next reward',
                'category': 'functional',
                'price': Decimal('0.99'),
                'effects': {
                    'show_progress': True,
                    'detailed_requirements': True,
                    'duration_days': 7
                },
                'is_active': True,
            },
            {
                'name': 'Priority Reward Processing',
                'description': 'Get your PayPal rewards processed within 24 hours',
                'category': 'functional',
                'price': Decimal('1.99'),
                'effects': {
                    'priority_processing': True,
                    'processing_time_hours': 24
                },
                'is_active': True,
            },
            {
                'name': 'Comment Boost',
                'description': '3x points for comments for 24 hours',
                'category': 'temporary',
                'price': Decimal('2.99'),
                'effects': {
                    'comment_multiplier': 3.0,
                    'duration_hours': 24,
                    'applies_to': 'comments'
                },
                'is_active': True,
            },
            
            # LIMITED TIME ITEMS
            {
                'name': 'Launch Week Special Badge',
                'description': 'Exclusive badge for early supporters',
                'category': 'cosmetic',
                'price': Decimal('2.99'),
                'effects': {
                    'special_badge': 'launch_week',
                    'badge_text': 'Early Supporter',
                    'badge_color': '#FF6B35'
                },
                'is_active': True,
                'is_limited_time': True,
                'max_purchases_per_user': 1,
            },
            {
                'name': 'Mega Point Multiplier',
                'description': '5x points for all activities for 2 hours',
                'category': 'temporary',
                'price': Decimal('7.99'),
                'effects': {
                    'point_multiplier': 5.0,
                    'duration_hours': 2,
                    'applies_to': 'all_activities'
                },
                'is_active': True,
                'is_limited_time': True,
            },
        ]

        for item_data in items:
            item, created = VirtualItem.objects.get_or_create(
                name=item_data['name'],
                defaults=item_data
            )
            
            if created:
                self.stdout.write(f'Created virtual item: {item.name} - ${item.price}')
            else:
                self.stdout.write(f'Virtual item already exists: {item.name}')

    def display_summary(self):
        """Display monetization system summary"""
        settings = MonetizationSettings.get_settings()
        items = VirtualItem.objects.filter(is_active=True)
        
        self.stdout.write('\n=== MONETIZATION SYSTEM SUMMARY ===')
        
        # Premium subscription
        self.stdout.write(f'\n💎 PREMIUM SUBSCRIPTION')
        self.stdout.write(f'Monthly Price: ${settings.premium_monthly_price}')
        self.stdout.write(f'Point Multiplier: {settings.premium_point_multiplier}x')
        self.stdout.write(f'Daily Bonus: {settings.premium_daily_bonus} points')
        
        # Tier unlocks
        self.stdout.write(f'\n🔓 TIER UNLOCK PRICES')
        self.stdout.write(f'Engagement Tier: ${settings.engagement_tier_price}')
        self.stdout.write(f'Achievement Tier: ${settings.achievement_tier_price}')
        self.stdout.write(f'Elite Tier: ${settings.elite_tier_price}')
        
        # Virtual items
        self.stdout.write(f'\n🛍️ VIRTUAL ITEMS ({items.count()} items)')
        
        cosmetic_items = items.filter(category='cosmetic')
        functional_items = items.filter(category='functional')
        temporary_items = items.filter(category='temporary')
        
        self.stdout.write(f'Cosmetic Items: {cosmetic_items.count()}')
        for item in cosmetic_items:
            self.stdout.write(f'  • {item.name} - ${item.price}')
        
        self.stdout.write(f'Functional Items: {functional_items.count()}')
        for item in functional_items:
            self.stdout.write(f'  • {item.name} - ${item.price}')
        
        self.stdout.write(f'Temporary Boosts: {temporary_items.count()}')
        for item in temporary_items:
            self.stdout.write(f'  • {item.name} - ${item.price}')
        
        # Revenue potential
        total_item_value = sum(item.price for item in items)
        self.stdout.write(f'\n💰 REVENUE POTENTIAL')
        self.stdout.write(f'Total Virtual Items Value: ${total_item_value}')
        self.stdout.write(f'Premium Monthly Revenue (500 users): ${settings.premium_monthly_price * 500}')
        self.stdout.write(f'Tier Unlocks Revenue (200 users avg $5): ${200 * 5}')
        
        estimated_monthly = (settings.premium_monthly_price * 500) + (200 * 5) + (total_item_value * 10)
        self.stdout.write(f'Estimated Monthly Revenue: ${estimated_monthly}')
        
        self.stdout.write(f'\n✅ Monetization system ready for launch!')

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset-items',
            action='store_true',
            help='Reset all virtual items (delete existing)',
        )
        
    def handle(self, *args, **options):
        if options['reset_items']:
            self.stdout.write('Resetting virtual items...')
            VirtualItem.objects.all().delete()
            self.stdout.write('Deleted all existing virtual items')
        
        self.stdout.write('Setting up monetization system...')
        
        # Create monetization settings
        self.create_monetization_settings()
        
        # Create virtual items
        self.create_virtual_items()
        
        # Display summary
        self.display_summary()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up monetization system!')
        )
