from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Conversation, Message, MessageReadStatus, ConversationMembership

User = get_user_model()


class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user serializer for messaging"""
    avatar_url = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'avatar_url']

    def get_avatar_url(self, obj):
        """Get user avatar URL"""
        if hasattr(obj, 'avatar') and obj.avatar:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.avatar.url)
            return obj.avatar.url
        return None


class MessageSerializer(serializers.ModelSerializer):
    """Serializer for messages"""
    sender = UserBasicSerializer(read_only=True)
    is_own_message = serializers.SerializerMethodField()

    class Meta:
        model = Message
        fields = [
            'id', 'content', 'sender', 'created_at', 'updated_at',
            'is_read', 'is_deleted', 'is_own_message'
        ]
        read_only_fields = ['id', 'sender', 'created_at', 'updated_at', 'is_read']

    def get_is_own_message(self, obj):
        """Check if message belongs to current user"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.sender.id == request.user.id
        return False

    def create(self, validated_data):
        """Create a new message"""
        request = self.context.get('request')
        validated_data['sender'] = request.user
        return super().create(validated_data)


class ConversationSerializer(serializers.ModelSerializer):
    """Serializer for conversations"""
    participants = UserBasicSerializer(many=True, read_only=True)
    last_message = MessageSerializer(read_only=True)
    unread_count = serializers.SerializerMethodField()
    other_participant = serializers.SerializerMethodField()

    class Meta:
        model = Conversation
        fields = [
            'id', 'participants', 'created_at', 'updated_at',
            'is_active', 'last_message', 'unread_count', 'other_participant'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_unread_count(self, obj):
        """Get unread message count for current user"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                membership = obj.memberships.get(user=request.user)
                return membership.unread_count
            except ConversationMembership.DoesNotExist:
                return 0
        return 0

    def get_other_participant(self, obj):
        """Get the other participant in a 2-person conversation"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            other_user = obj.get_other_participant(request.user)
            if other_user:
                return UserBasicSerializer(other_user, context=self.context).data
        return None


class ConversationCreateSerializer(serializers.Serializer):
    """Serializer for creating conversations"""
    participant_username = serializers.CharField()
    initial_message = serializers.CharField(required=False)

    def validate_participant_username(self, value):
        """Validate that the participant exists"""
        try:
            user = User.objects.get(username=value)
            request = self.context.get('request')
            if request and request.user == user:
                raise serializers.ValidationError("You cannot start a conversation with yourself")
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found")

    def create(self, validated_data):
        """Create a new conversation"""
        request = self.context.get('request')
        participant_username = validated_data['participant_username']
        initial_message = validated_data.get('initial_message')

        # Get the other user
        other_user = User.objects.get(username=participant_username)

        # Check if conversation already exists between these users
        existing_conversation = Conversation.objects.filter(
            participants=request.user
        ).filter(
            participants=other_user
        ).first()

        if existing_conversation:
            conversation = existing_conversation
        else:
            # Create new conversation
            conversation = Conversation.objects.create()
            conversation.participants.add(request.user, other_user)

            # Create memberships
            ConversationMembership.objects.get_or_create(
                conversation=conversation,
                user=request.user
            )
            ConversationMembership.objects.get_or_create(
                conversation=conversation,
                user=other_user
            )

        # Send initial message if provided
        if initial_message:
            Message.objects.create(
                conversation=conversation,
                sender=request.user,
                content=initial_message
            )

        return conversation


class MessageReadStatusSerializer(serializers.ModelSerializer):
    """Serializer for message read status"""
    user = UserBasicSerializer(read_only=True)

    class Meta:
        model = MessageReadStatus
        fields = ['id', 'user', 'read_at']
        read_only_fields = ['id', 'read_at']


class ConversationMembershipSerializer(serializers.ModelSerializer):
    """Serializer for conversation membership"""
    user = UserBasicSerializer(read_only=True)
    conversation = ConversationSerializer(read_only=True)

    class Meta:
        model = ConversationMembership
        fields = [
            'id', 'user', 'conversation', 'joined_at', 'last_read_at',
            'is_muted', 'is_archived', 'unread_count'
        ]
        read_only_fields = ['id', 'user', 'conversation', 'joined_at', 'unread_count']
