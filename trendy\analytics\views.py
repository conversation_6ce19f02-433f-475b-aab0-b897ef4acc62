from django.shortcuts import render, get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.db.models import Avg, Count, Sum
from blog.models import Post
from .models import ReadingSession, ContentAnalytics
from .serializers import ReadingSessionSerializer, ContentAnalyticsSerializer
import json

@api_view(['POST'])
@permission_classes([AllowAny])
def start_reading_session(request):
    """Start a new reading session for a post"""
    try:
        post_id = request.data.get('post_id')
        device_type = request.data.get('device_type', 'unknown')

        post = get_object_or_404(Post, id=post_id)

        # Only create sessions for authenticated users
        if not request.user.is_authenticated:
            return Response({
                'message': 'Reading session tracking requires authentication',
                'session_id': None
            }, status=status.HTTP_200_OK)

        # End any existing active sessions for this user and post
        ReadingSession.objects.filter(
            user=request.user,
            post=post,
            end_time__isnull=True
        ).update(end_time=timezone.now())

        # Create new session
        session = ReadingSession.objects.create(
            user=request.user,
            post=post,
            device_type=device_type
        )

        return Response({
            'session_id': session.id,
            'message': 'Reading session started'
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([AllowAny])
def update_reading_progress(request):
    """Update reading progress for an active session"""
    try:
        session_id = request.data.get('session_id')

        # Return success for anonymous users without session_id
        if not request.user.is_authenticated or not session_id:
            return Response({
                'message': 'Progress tracking requires authentication and session_id'
            }, status=status.HTTP_200_OK)

        progress_percentage = request.data.get('progress_percentage', 0)
        scroll_depth = request.data.get('scroll_depth', 0)

        session = get_object_or_404(ReadingSession, id=session_id, user=request.user)

        # Update session progress
        session.progress_percentage = min(100, max(0, progress_percentage))
        session.scroll_depth = max(session.scroll_depth, scroll_depth)
        session.session_duration = int((timezone.now() - session.start_time).total_seconds())

        # Mark as completed if progress is 90% or more
        if session.progress_percentage >= 90:
            session.is_completed = True

        # Calculate reading speed
        session.calculate_reading_speed()
        session.save()

        return Response({
            'message': 'Progress updated',
            'progress': session.progress_percentage,
            'is_completed': session.is_completed
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([AllowAny])
def end_reading_session(request):
    """End a reading session"""
    try:
        session_id = request.data.get('session_id')

        # Return success for anonymous users without session_id
        if not request.user.is_authenticated or not session_id:
            return Response({
                'message': 'Session ending requires authentication and session_id'
            }, status=status.HTTP_200_OK)

        session = get_object_or_404(ReadingSession, id=session_id, user=request.user)

        if not session.end_time:
            session.end_time = timezone.now()
            session.session_duration = int((session.end_time - session.start_time).total_seconds())
            session.calculate_reading_speed()
            session.save()

            # Update post analytics
            update_post_analytics(session.post)

        return Response({
            'message': 'Reading session ended',
            'duration': session.session_duration,
            'progress': session.progress_percentage
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def get_content_analytics(request, post_id):
    """Get content analytics for a post"""
    try:
        post = get_object_or_404(Post, id=post_id)

        # Get or create content analytics
        analytics, created = ContentAnalytics.objects.get_or_create(
            post=post,
            defaults={'estimated_reading_time': 0}
        )

        if created or not analytics.word_count:
            # Analyze content if not done yet
            analytics = ContentAnalytics.analyze_content(post)

        serializer = ContentAnalyticsSerializer(analytics)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_reading_stats(request):
    """Get reading statistics for the current user"""
    try:
        sessions = ReadingSession.objects.filter(user=request.user)

        stats = {
            'total_sessions': sessions.count(),
            'completed_sessions': sessions.filter(is_completed=True).count(),
            'total_reading_time': sessions.aggregate(
                total=Sum('session_duration')
            )['total'] or 0,
            'average_reading_speed': sessions.filter(
                reading_speed_wpm__isnull=False
            ).aggregate(
                avg=Avg('reading_speed_wpm')
            )['avg'] or 0,
            'completion_rate': 0
        }

        if stats['total_sessions'] > 0:
            stats['completion_rate'] = (stats['completed_sessions'] / stats['total_sessions']) * 100

        # Recent reading activity
        recent_sessions = sessions.order_by('-start_time')[:10]
        stats['recent_activity'] = ReadingSessionSerializer(recent_sessions, many=True).data

        return Response(stats)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

def update_post_analytics(post):
    """Update aggregated analytics for a post"""
    try:
        analytics = post.content_analytics
        sessions = post.reading_sessions.all()

        if sessions.exists():
            analytics.total_reading_time = sessions.aggregate(
                total=Sum('session_duration')
            )['total'] or 0

            analytics.completion_rate = (
                sessions.filter(is_completed=True).count() / sessions.count()
            ) * 100

            analytics.average_session_duration = sessions.aggregate(
                avg=Avg('session_duration')
            )['avg'] or 0

            analytics.save()

    except ContentAnalytics.DoesNotExist:
        # Create analytics if they don't exist
        ContentAnalytics.analyze_content(post)
