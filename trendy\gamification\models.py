from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone

User = get_user_model()

class Badge(models.Model):
    """Achievement badges that users can earn"""
    BADGE_TYPES = [
        ('reading', 'Reading Achievement'),
        ('writing', 'Writing Achievement'),
        ('engagement', 'Engagement Achievement'),
        ('community', 'Community Achievement'),
        ('special', 'Special Achievement'),
        ('milestone', 'Milestone Achievement'),
    ]

    RARITY_LEVELS = [
        ('common', 'Common'),
        ('uncommon', 'Uncommon'),
        ('rare', 'Rare'),
        ('epic', 'Epic'),
        ('legendary', 'Legendary'),
    ]

    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    badge_type = models.CharField(max_length=20, choices=BADGE_TYPES)
    rarity = models.Char<PERSON>ield(max_length=20, choices=RARITY_LEVELS, default='common')

    # Visual
    icon = models.CharField(max_length=50, default='star')  # Icon name
    color = models.CharField(max_length=7, default='#FFD700')  # Hex color
    image_url = models.URLField(blank=True)

    # Requirements
    requirements = models.JSONField(default=dict)  # Flexible requirements structure
    points_reward = models.PositiveIntegerField(default=10)

    # Metadata
    is_active = models.BooleanField(default=True)
    is_secret = models.BooleanField(default=False)  # Hidden until earned
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['rarity', 'name']
        indexes = [
            models.Index(fields=['badge_type', 'is_active']),
            models.Index(fields=['rarity']),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_rarity_display()})"

class UserBadge(models.Model):
    """Badges earned by users"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='earned_badges')
    badge = models.ForeignKey(Badge, on_delete=models.CASCADE, related_name='earned_by')
    earned_at = models.DateTimeField(auto_now_add=True)

    # Progress tracking
    progress_data = models.JSONField(default=dict)  # Store progress details

    class Meta:
        unique_together = ['user', 'badge']
        ordering = ['-earned_at']
        indexes = [
            models.Index(fields=['user', '-earned_at']),
            models.Index(fields=['badge']),
        ]

    def __str__(self):
        return f"{self.user.username} earned {self.badge.name}"

class Challenge(models.Model):
    """Reading and writing challenges"""
    CHALLENGE_TYPES = [
        ('reading', 'Reading Challenge'),
        ('writing', 'Writing Challenge'),
        ('engagement', 'Engagement Challenge'),
        ('community', 'Community Challenge'),
        ('quiz', 'Quiz Challenge'),
        ('poll', 'Poll Challenge'),
        ('interactive', 'Interactive Challenge'),
    ]

    DIFFICULTY_LEVELS = [
        ('easy', 'Easy'),
        ('medium', 'Medium'),
        ('hard', 'Hard'),
        ('expert', 'Expert'),
    ]

    title = models.CharField(max_length=200)
    description = models.TextField()
    challenge_type = models.CharField(max_length=20, choices=CHALLENGE_TYPES)
    difficulty = models.CharField(max_length=20, choices=DIFFICULTY_LEVELS)

    # Requirements and rewards
    requirements = models.JSONField(default=dict)  # What needs to be accomplished
    points_reward = models.PositiveIntegerField(default=50)
    badge_reward = models.ForeignKey(Badge, on_delete=models.SET_NULL, null=True, blank=True)

    # Timing
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    duration_days = models.PositiveIntegerField(default=7)

    # Participation
    max_participants = models.PositiveIntegerField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-is_featured', '-start_date']
        indexes = [
            models.Index(fields=['challenge_type', 'is_active']),
            models.Index(fields=['start_date', 'end_date']),
            models.Index(fields=['is_featured']),
        ]

    def __str__(self):
        return f"{self.title} ({self.get_difficulty_display()})"

    @property
    def is_ongoing(self):
        now = timezone.now()
        return self.start_date <= now <= self.end_date

    @property
    def participant_count(self):
        return self.participants.filter(is_active=True).count()

class ChallengeParticipation(models.Model):
    """User participation in challenges"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='challenge_participations')
    challenge = models.ForeignKey(Challenge, on_delete=models.CASCADE, related_name='participants')

    # Progress tracking
    progress = models.JSONField(default=dict)  # Flexible progress structure
    completion_percentage = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)]
    )

    # Status
    is_completed = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    joined_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'challenge']
        ordering = ['-joined_at']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['challenge', 'is_completed']),
        ]

    def __str__(self):
        return f"{self.user.username} in {self.challenge.title}"

class UserLevel(models.Model):
    """User leveling system"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='level')

    # Experience and level
    total_points = models.PositiveIntegerField(default=0)
    current_level = models.PositiveIntegerField(default=1)
    points_to_next_level = models.PositiveIntegerField(default=100)

    # Activity streaks
    reading_streak = models.PositiveIntegerField(default=0)
    writing_streak = models.PositiveIntegerField(default=0)
    engagement_streak = models.PositiveIntegerField(default=0)

    # Last activity dates
    last_reading_date = models.DateField(null=True, blank=True)
    last_writing_date = models.DateField(null=True, blank=True)
    last_engagement_date = models.DateField(null=True, blank=True)

    # Statistics
    total_posts_read = models.PositiveIntegerField(default=0)
    total_posts_written = models.PositiveIntegerField(default=0)
    total_comments_made = models.PositiveIntegerField(default=0)
    total_likes_given = models.PositiveIntegerField(default=0)
    total_voice_comments = models.PositiveIntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-current_level', '-total_points']
        indexes = [
            models.Index(fields=['current_level', '-total_points']),
            models.Index(fields=['-total_points']),
        ]

    def __str__(self):
        return f"{self.user.username} - Level {self.current_level}"

    @property
    def level_progress_percentage(self):
        """Calculate progress to next level as percentage"""
        if self.points_to_next_level == 0:
            return 100.0

        level_points = self.get_points_for_level(self.current_level + 1) - self.get_points_for_level(self.current_level)
        current_progress = level_points - self.points_to_next_level
        return (current_progress / level_points) * 100

    @staticmethod
    def get_points_for_level(level):
        """Calculate total points needed for a specific level"""
        return (level - 1) * 100 + ((level - 1) * (level - 2) * 25)

class PointTransaction(models.Model):
    """Track all point transactions"""
    TRANSACTION_TYPES = [
        ('reading', 'Reading Activity'),
        ('writing', 'Writing Activity'),
        ('engagement', 'Engagement Activity'),
        ('badge', 'Badge Earned'),
        ('challenge', 'Challenge Completed'),
        ('bonus', 'Bonus Points'),
        ('penalty', 'Point Penalty'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='point_transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    points = models.IntegerField()  # Can be negative for penalties

    # Context
    description = models.CharField(max_length=200)
    related_object_type = models.CharField(max_length=50, blank=True)  # 'post', 'comment', etc.
    related_object_id = models.PositiveIntegerField(null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['transaction_type']),
        ]

    def __str__(self):
        sign = '+' if self.points >= 0 else ''
        return f"{self.user.username}: {sign}{self.points} points - {self.description}"


class PayPalReward(models.Model):
    """PayPal monetary rewards for point achievements"""
    REWARD_TYPES = [
        ('milestone', 'Point Milestone'),
        ('level', 'Level Achievement'),
        ('challenge', 'Challenge Completion'),
        ('streak', 'Streak Achievement'),
        ('special', 'Special Event'),
    ]

    REWARD_STATUS = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('limited', 'Limited Time'),
    ]

    name = models.CharField(max_length=100)
    description = models.TextField()
    reward_type = models.CharField(max_length=20, choices=REWARD_TYPES)

    # Requirements
    points_required = models.PositiveIntegerField(null=True, blank=True)
    level_required = models.PositiveIntegerField(null=True, blank=True)
    streak_required = models.PositiveIntegerField(null=True, blank=True)
    requirements = models.JSONField(default=dict)  # Flexible requirements

    # Reward details
    usd_amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')

    # Limits and controls
    max_claims_per_user = models.PositiveIntegerField(default=1)
    max_total_claims = models.PositiveIntegerField(null=True, blank=True)
    current_claims = models.PositiveIntegerField(default=0)

    # Status and timing
    status = models.CharField(max_length=20, choices=REWARD_STATUS, default='active')
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(null=True, blank=True)

    # Business controls
    is_featured = models.BooleanField(default=False)
    minimum_account_age_days = models.PositiveIntegerField(default=30)
    requires_verification = models.BooleanField(default=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-usd_amount', '-points_required']
        indexes = [
            models.Index(fields=['status', 'reward_type']),
            models.Index(fields=['points_required']),
            models.Index(fields=['level_required']),
        ]

    def __str__(self):
        return f"{self.name} - ${self.usd_amount}"

    @property
    def is_available(self):
        """Check if reward is currently available"""
        now = timezone.now()
        if self.status != 'active':
            return False
        if self.start_date > now:
            return False
        if self.end_date and self.end_date < now:
            return False
        if self.max_total_claims and self.current_claims >= self.max_total_claims:
            return False
        return True

    @property
    def claims_remaining(self):
        """Get remaining claims for this reward"""
        if not self.max_total_claims:
            return float('inf')
        return max(0, self.max_total_claims - self.current_claims)


class UserPayPalReward(models.Model):
    """Track PayPal rewards claimed by users"""
    CLAIM_STATUS = [
        ('pending', 'Pending Verification'),
        ('approved', 'Approved for Payment'),
        ('processing', 'Processing Payment'),
        ('paid', 'Payment Sent'),
        ('failed', 'Payment Failed'),
        ('cancelled', 'Cancelled'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='paypal_rewards')
    reward = models.ForeignKey(PayPalReward, on_delete=models.CASCADE, related_name='user_claims')

    # PayPal details
    paypal_email = models.EmailField()
    usd_amount = models.DecimalField(max_digits=10, decimal_places=2)

    # Status tracking
    status = models.CharField(max_length=20, choices=CLAIM_STATUS, default='pending')
    claim_date = models.DateTimeField(auto_now_add=True)
    approved_date = models.DateTimeField(null=True, blank=True)
    paid_date = models.DateTimeField(null=True, blank=True)

    # PayPal transaction details
    paypal_transaction_id = models.CharField(max_length=100, blank=True)
    paypal_batch_id = models.CharField(max_length=100, blank=True)

    # User verification
    user_points_at_claim = models.PositiveIntegerField()
    user_level_at_claim = models.PositiveIntegerField()
    verification_notes = models.TextField(blank=True)

    # Admin fields
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_paypal_rewards'
    )
    admin_notes = models.TextField(blank=True)

    class Meta:
        unique_together = ['user', 'reward']  # Prevent duplicate claims
        ordering = ['-claim_date']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['status', 'claim_date']),
            models.Index(fields=['reward', 'status']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.reward.name} - ${self.usd_amount}"


class PayPalSettings(models.Model):
    """Global PayPal reward system settings"""
    # Business controls
    rewards_enabled = models.BooleanField(default=True)
    minimum_payout = models.DecimalField(max_digits=10, decimal_places=2, default=5.00)
    maximum_monthly_payout_per_user = models.DecimalField(max_digits=10, decimal_places=2, default=100.00)

    # Verification requirements
    require_email_verification = models.BooleanField(default=True)
    require_phone_verification = models.BooleanField(default=True)
    minimum_account_age_days = models.PositiveIntegerField(default=30)
    minimum_activity_score = models.PositiveIntegerField(default=100)

    # Fraud prevention
    max_claims_per_day = models.PositiveIntegerField(default=10)
    max_claims_per_month = models.PositiveIntegerField(default=50)
    cooldown_period_days = models.PositiveIntegerField(default=7)

    # PayPal API settings
    paypal_client_id = models.CharField(max_length=200, blank=True)
    paypal_client_secret = models.CharField(max_length=200, blank=True)
    paypal_mode = models.CharField(
        max_length=10,
        choices=[('sandbox', 'Sandbox'), ('live', 'Live')],
        default='sandbox'
    )

    # Notifications
    admin_email_notifications = models.BooleanField(default=True)
    user_email_notifications = models.BooleanField(default=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "PayPal Settings"
        verbose_name_plural = "PayPal Settings"

    def save(self, *args, **kwargs):
        # Ensure only one settings instance exists
        if not self.pk and PayPalSettings.objects.exists():
            raise ValueError("Only one PayPalSettings instance is allowed")
        super().save(*args, **kwargs)

    @classmethod
    def get_settings(cls):
        """Get or create the single settings instance"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings


class EngagementSettings(models.Model):
    """Global settings for engagement rules and fraud prevention"""
    # Daily limits
    max_posts_read_per_day = models.PositiveIntegerField(default=50)
    max_comments_per_day = models.PositiveIntegerField(default=20)
    max_likes_per_day = models.PositiveIntegerField(default=100)
    max_actions_per_minute = models.PositiveIntegerField(default=30)

    # Reading requirements
    min_reading_time_seconds = models.PositiveIntegerField(default=10)
    min_scroll_percentage = models.FloatField(default=30.0)

    # Cooldown periods (in seconds)
    like_cooldown = models.PositiveIntegerField(default=2)
    comment_cooldown = models.PositiveIntegerField(default=30)

    # Fraud detection
    enable_fraud_detection = models.BooleanField(default=True)
    auto_flag_suspicious = models.BooleanField(default=True)

    # Point values
    reading_points = models.PositiveIntegerField(default=5)
    comment_points = models.PositiveIntegerField(default=10)
    like_points = models.PositiveIntegerField(default=2)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Engagement Settings"
        verbose_name_plural = "Engagement Settings"

    def save(self, *args, **kwargs):
        # Ensure only one settings instance exists
        if not self.pk and EngagementSettings.objects.exists():
            raise ValueError("Only one EngagementSettings instance is allowed")
        super().save(*args, **kwargs)

    @classmethod
    def get_settings(cls):
        """Get or create the single settings instance"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings


class PostReadingHistory(models.Model):
    """Track user's reading history for each post to prevent duplicate rewards"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reading_history')
    post_id = models.PositiveIntegerField()  # Using ID instead of FK for flexibility

    # Reading tracking
    first_read_at = models.DateTimeField(auto_now_add=True)
    last_read_at = models.DateTimeField(auto_now=True)
    read_count = models.PositiveIntegerField(default=1)

    # Reward tracking
    points_awarded = models.PositiveIntegerField(default=0)
    reward_given = models.BooleanField(default=False)

    # Quality metrics
    time_spent_seconds = models.PositiveIntegerField(default=0)
    scroll_percentage = models.FloatField(default=0.0)

    class Meta:
        unique_together = ['user', 'post_id']
        indexes = [
            models.Index(fields=['user', 'post_id']),
            models.Index(fields=['user', 'reward_given']),
            models.Index(fields=['first_read_at']),
        ]

    def __str__(self):
        return f"{self.user.username} read post {self.post_id}"


class EngagementHistory(models.Model):
    """Track all user engagement activities to prevent duplicate rewards"""
    ENGAGEMENT_TYPES = [
        ('like', 'Like'),
        ('comment', 'Comment'),
        ('share', 'Share'),
        ('follow', 'Follow'),
        ('voice_comment', 'Voice Comment'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='engagement_history')
    engagement_type = models.CharField(max_length=20, choices=ENGAGEMENT_TYPES)
    target_type = models.CharField(max_length=50)  # 'post', 'comment', etc.
    target_id = models.PositiveIntegerField()

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True)
    points_awarded = models.PositiveIntegerField(default=0)
    is_valid = models.BooleanField(default=True)
    time_since_last_action = models.DurationField(null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['user', 'engagement_type', 'created_at']),
            models.Index(fields=['target_type', 'target_id']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} {self.engagement_type} {self.target_type} {self.target_id}"


class UserEngagementTracker(models.Model):
    """Track daily engagement limits and activity patterns for each user"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='engagement_tracker')

    # Daily counters
    posts_read_today = models.PositiveIntegerField(default=0)
    last_reading_reset = models.DateField(auto_now_add=True)
    comments_today = models.PositiveIntegerField(default=0)
    likes_today = models.PositiveIntegerField(default=0)
    last_engagement_reset = models.DateField(auto_now_add=True)

    # Fraud detection
    is_flagged = models.BooleanField(default=False)
    flag_reason = models.CharField(max_length=200, blank=True)
    flagged_at = models.DateTimeField(null=True, blank=True)

    # Rate limiting
    last_activity_timestamp = models.DateTimeField(auto_now=True)
    activity_count_last_minute = models.PositiveIntegerField(default=0)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['user', 'last_reading_reset']),
            models.Index(fields=['is_flagged']),
        ]

    def __str__(self):
        return f"{self.user.username} engagement tracker"

    def reset_daily_counters_if_needed(self):
        """Reset daily counters if it's a new day"""
        from django.utils import timezone
        today = timezone.now().date()

        if self.last_reading_reset < today:
            self.posts_read_today = 0
            self.last_reading_reset = today

        if self.last_engagement_reset < today:
            self.comments_today = 0
            self.likes_today = 0
            self.last_engagement_reset = today

        self.save()


class SuspiciousActivityLog(models.Model):
    """Log suspicious activities for admin review"""
    ACTIVITY_TYPES = [
        ('rapid_reading', 'Rapid Reading'),
        ('excessive_likes', 'Excessive Likes'),
        ('spam_comments', 'Spam Comments'),
        ('bot_behavior', 'Bot-like Behavior'),
        ('duplicate_rewards', 'Duplicate Reward Attempts'),
        ('rate_limit_exceeded', 'Rate Limit Exceeded'),
    ]

    SEVERITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='suspicious_activities')
    activity_type = models.CharField(max_length=30, choices=ACTIVITY_TYPES)
    description = models.TextField()

    # Context
    related_object_type = models.CharField(max_length=50, blank=True)
    related_object_id = models.PositiveIntegerField(null=True, blank=True)

    # Severity and resolution
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, default='medium')
    is_resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_suspicious_activities'
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'activity_type']),
            models.Index(fields=['severity', 'is_resolved']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.activity_type} ({self.severity})"


class PointConversionSettings(models.Model):
    """Settings for gamification to store points conversion"""
    # Conversion rates (how many gamification points needed for 1 store point)
    base_conversion_rate = models.PositiveIntegerField(default=10)  # 10 gamification points = 1 store point

    # Level-based bonuses (better rates for higher levels)
    level_bonus_enabled = models.BooleanField(default=True)
    level_bonus_threshold = models.PositiveIntegerField(default=5)  # Start bonus at level 5
    level_bonus_reduction = models.FloatField(default=0.1)  # 10% reduction per level tier
    max_level_bonus = models.FloatField(default=0.5)  # Maximum 50% reduction

    # Premium user bonuses
    premium_bonus_enabled = models.BooleanField(default=True)
    premium_conversion_bonus = models.FloatField(default=0.2)  # 20% better rate for premium users

    # Daily limits and restrictions
    daily_conversion_limit = models.PositiveIntegerField(default=500)  # Max 500 store points per day
    minimum_conversion_amount = models.PositiveIntegerField(default=50)  # Min 50 gamification points
    maximum_conversion_amount = models.PositiveIntegerField(default=5000)  # Max 5000 gamification points per transaction

    # Conversion fees (additional cost to benefit developer)
    conversion_fee_percentage = models.FloatField(default=0.15)  # 15% fee
    conversion_fee_fixed = models.PositiveIntegerField(default=5)  # Fixed 5 point fee

    # System controls
    conversion_enabled = models.BooleanField(default=True)
    maintenance_mode = models.BooleanField(default=False)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Point Conversion Settings"
        verbose_name_plural = "Point Conversion Settings"

    def save(self, *args, **kwargs):
        # Ensure only one settings instance exists
        if not self.pk and PointConversionSettings.objects.exists():
            raise ValueError("Only one PointConversionSettings instance is allowed")
        super().save(*args, **kwargs)

    @classmethod
    def get_settings(cls):
        """Get or create the single settings instance"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings

    def calculate_conversion_rate(self, user):
        """Calculate the actual conversion rate for a specific user"""
        rate = self.base_conversion_rate

        # Apply level bonus
        if self.level_bonus_enabled:
            try:
                user_level = UserLevel.objects.get(user=user)
            except UserLevel.DoesNotExist:
                user_level = UserLevel.objects.create(
                    user=user,
                    total_points=0,
                    current_level=1,
                    points_to_next_level=100
                )

            if user_level.current_level >= self.level_bonus_threshold:
                level_tiers = (user_level.current_level - self.level_bonus_threshold) // 5
                bonus_reduction = min(level_tiers * self.level_bonus_reduction, self.max_level_bonus)
                rate = rate * (1 - bonus_reduction)

        # Apply premium bonus
        if self.premium_bonus_enabled:
            try:
                from monetization.services import MonetizationService
                premium_status = MonetizationService.get_user_premium_status(user)
                if premium_status.get('is_premium', False):
                    rate = rate * (1 - self.premium_conversion_bonus)
            except:
                pass  # If monetization service not available

        return max(1, int(rate))  # Ensure rate is at least 1

    def calculate_total_cost(self, gamification_points, user):
        """Calculate total cost including fees"""
        # Base cost
        conversion_rate = self.calculate_conversion_rate(user)
        base_cost = gamification_points

        # Add percentage fee
        percentage_fee = int(gamification_points * self.conversion_fee_percentage)

        # Add fixed fee
        fixed_fee = self.conversion_fee_fixed

        total_cost = base_cost + percentage_fee + fixed_fee
        store_points = gamification_points // conversion_rate

        return {
            'total_cost': total_cost,
            'store_points': store_points,
            'conversion_rate': conversion_rate,
            'percentage_fee': percentage_fee,
            'fixed_fee': fixed_fee,
            'base_cost': base_cost
        }


class UserStorePoints(models.Model):
    """Track user's store points balance"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='store_points')
    balance = models.PositiveIntegerField(default=0)

    # Statistics
    total_earned = models.PositiveIntegerField(default=0)
    total_spent = models.PositiveIntegerField(default=0)
    total_converted = models.PositiveIntegerField(default=0)  # From gamification points

    # Daily tracking
    daily_conversions_today = models.PositiveIntegerField(default=0)
    last_conversion_reset = models.DateField(auto_now_add=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['user', 'balance']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.balance} store points"

    def reset_daily_conversions_if_needed(self):
        """Reset daily conversion counter if it's a new day"""
        from django.utils import timezone
        today = timezone.now().date()

        if self.last_conversion_reset < today:
            self.daily_conversions_today = 0
            self.last_conversion_reset = today
            self.save()

    def can_convert_today(self, store_points_amount):
        """Check if user can convert the specified amount today"""
        self.reset_daily_conversions_if_needed()
        settings = PointConversionSettings.get_settings()
        return (self.daily_conversions_today + store_points_amount) <= settings.daily_conversion_limit

    def add_points(self, amount, source='conversion'):
        """Add store points to user's balance"""
        self.balance += amount
        if source == 'conversion':
            self.total_converted += amount
            self.daily_conversions_today += amount
        else:
            self.total_earned += amount
        self.save()

    def spend_points(self, amount):
        """Spend store points (returns True if successful)"""
        if self.balance >= amount:
            self.balance -= amount
            self.total_spent += amount
            self.save()
            return True
        return False


class PointConversionTransaction(models.Model):
    """Track all point conversion transactions"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversion_transactions')

    # Conversion details
    gamification_points_spent = models.PositiveIntegerField()
    store_points_received = models.PositiveIntegerField()
    conversion_rate = models.PositiveIntegerField()  # Rate used at time of conversion

    # Fees applied
    percentage_fee = models.PositiveIntegerField(default=0)
    fixed_fee = models.PositiveIntegerField(default=0)
    total_fee = models.PositiveIntegerField(default=0)

    # User context at time of conversion
    user_level_at_conversion = models.PositiveIntegerField()
    user_total_points_at_conversion = models.PositiveIntegerField()
    was_premium = models.BooleanField(default=False)

    # Status
    status = models.CharField(max_length=20, choices=[
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ], default='pending')

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.gamification_points_spent}→{self.store_points_received} ({self.status})"
