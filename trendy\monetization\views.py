from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Count, Sum, Avg, Q
from django.db import transaction
from django.utils import timezone
from .models import (
    PremiumSubscription, VirtualItem, UserVirtualItem, PointBoostPurchase,
    ReferralProgram, MonetizationSettings
)
from .services import MonetizationService


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def premium_status(request):
    """Get user's premium subscription status"""
    try:
        user = request.user
        premium_status = MonetizationService.get_user_premium_status(user)
        
        return Response({
            'success': True,
            'premium_status': premium_status
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error fetching premium status: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def premium_subscribe(request):
    """Subscribe to premium"""
    try:
        user = request.user
        plan = request.data.get('plan', 'monthly')
        payment_method = request.data.get('payment_method', 'wallet')  # Default to wallet

        # Check if user already has active premium
        existing_subscription = PremiumSubscription.objects.filter(
            user=user,
            status='active'
        ).first()

        if existing_subscription:
            return Response({
                'success': False,
                'message': 'User already has an active premium subscription'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create premium subscription using service
        success, result = MonetizationService.subscribe_to_premium(
            user=user,
            plan=plan,
            payment_method=payment_method
        )

        if success:
            return Response({
                'success': True,
                'data': result
            })
        else:
            return Response({
                'success': False,
                'message': result
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error creating premium subscription: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def confirm_subscription_payment(request):
    """Confirm subscription payment and activate premium"""
    try:
        transaction_id = request.data.get('transaction_id')
        payment_status = request.data.get('status')

        if not transaction_id or not payment_status:
            return Response({
                'success': False,
                'message': 'Transaction ID and status are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Find the transaction (could be PaymentTransaction or PurchaseTransaction)
        transaction = None

        # First try PaymentTransaction (from payment system)
        try:
            from payments.models import PaymentTransaction
            transaction = PaymentTransaction.objects.get(
                id=transaction_id,
                user=request.user,
                payment_purpose='premium_subscription',
                status__in=['pending', 'completed']
            )
        except PaymentTransaction.DoesNotExist:
            # Then try PurchaseTransaction (from monetization system)
            try:
                from .models import PurchaseTransaction
                transaction = PurchaseTransaction.objects.get(
                    id=transaction_id,
                    user=request.user,
                    transaction_type='premium_subscription',
                    status='pending'
                )
            except PurchaseTransaction.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Transaction not found'
                }, status=status.HTTP_404_NOT_FOUND)

        if payment_status == 'COMPLETED':
            # Create a compatible transaction object for the service
            if hasattr(transaction, 'payment_purpose'):
                # This is a PaymentTransaction - create a compatible wrapper
                class TransactionWrapper:
                    def __init__(self, payment_transaction):
                        self.user = payment_transaction.user
                        self.amount = payment_transaction.amount
                        self.status = payment_transaction.status
                        self.completed_at = payment_transaction.completed_at
                        self._payment_transaction = payment_transaction

                    def save(self):
                        self._payment_transaction.save()

                wrapped_transaction = TransactionWrapper(transaction)
                success, message = MonetizationService.activate_premium_from_payment(wrapped_transaction)
            else:
                # This is already a PurchaseTransaction
                success, message = MonetizationService.activate_premium_from_payment(transaction)

            if success:
                return Response({
                    'success': True,
                    'message': 'Premium subscription activated successfully'
                })
            else:
                return Response({
                    'success': False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)
        else:
            # Payment failed
            transaction.status = 'failed'
            transaction.save()

            return Response({
                'success': False,
                'message': 'Payment was not completed'
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error confirming payment: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def cancel_pending_subscription(request):
    """Cancel a pending subscription"""
    try:
        subscription_id = request.data.get('subscription_id')

        if not subscription_id:
            return Response({
                'success': False,
                'message': 'Subscription ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Find and cancel the subscription
        try:
            from .models import PremiumSubscription
            subscription = PremiumSubscription.objects.get(
                id=subscription_id,
                user=request.user,
                status='pending'
            )

            subscription.status = 'cancelled'
            subscription.save()

            return Response({
                'success': True,
                'message': 'Subscription cancelled successfully'
            })

        except PremiumSubscription.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Pending subscription not found'
            }, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error cancelling subscription: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def virtual_items_list(request):
    """Get available virtual items"""
    try:
        items = VirtualItem.objects.filter(is_active=True)
        
        items_data = []
        for item in items:
            items_data.append({
                'id': item.id,
                'name': item.name,
                'description': item.description,
                'category': item.category,
                'price': str(item.price),
                'currency': item.currency,
                'is_limited_time': item.is_limited_time,
                'max_purchases_per_user': item.max_purchases_per_user,
                'effects': item.effects,
            })
        
        return Response({
            'success': True,
            'items': items_data
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error fetching virtual items: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def purchase_virtual_item(request, item_id):
    """Purchase a virtual item with store points"""
    try:
        user = request.user
        payment_method = request.data.get('payment_method', 'store_points')  # Default to store points

        # Get the item
        try:
            item = VirtualItem.objects.get(id=item_id, is_active=True)
        except VirtualItem.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Virtual item not found or not available'
            }, status=status.HTTP_404_NOT_FOUND)

        # Check purchase limits
        user_purchases = UserVirtualItem.objects.filter(user=user, item=item).count()
        if item.max_purchases_per_user and user_purchases >= item.max_purchases_per_user:
            return Response({
                'success': False,
                'message': 'Purchase limit reached for this item'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Handle store points payment
        if payment_method == 'store_points':
            from gamification.models import UserStorePoints

            # Get or create user store points
            store_points, created = UserStorePoints.objects.get_or_create(user=user)

            # Convert item price to store points (1:1 ratio for now)
            store_points_cost = int(item.price)

            # Check if user has enough store points
            if store_points.balance < store_points_cost:
                return Response({
                    'success': False,
                    'message': f'Insufficient store points. You need {store_points_cost} but have {store_points.balance}.',
                    'required_points': store_points_cost,
                    'current_points': store_points.balance
                }, status=status.HTTP_400_BAD_REQUEST)

            # Deduct store points
            if store_points.spend_points(store_points_cost):
                # Create purchase record
                purchase = UserVirtualItem.objects.create(
                    user=user,
                    item=item,
                    purchase_price=item.price,
                    status='completed',
                    payment_method='store_points'
                )

                # Create transaction record for store points
                from gamification.models import PointTransaction
                PointTransaction.objects.create(
                    user=user,
                    transaction_type='spend',
                    points=-store_points_cost,  # Negative for spending
                    description=f'Purchased {item.name}',
                    related_object_type='virtual_item',
                    related_object_id=item.id
                )

                return Response({
                    'success': True,
                    'message': f'Virtual item purchased successfully with {store_points_cost} store points',
                    'purchase_id': purchase.id,
                    'points_spent': store_points_cost,
                    'remaining_points': store_points.balance
                })
            else:
                return Response({
                    'success': False,
                    'message': 'Failed to deduct store points'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Handle real money payment (for items that require cash)
        elif payment_method == 'real_money':
            if item.price > 0:
                return Response({
                    'success': False,
                    'payment_required': True,
                    'item_id': item_id,
                    'item_name': item.name,
                    'price': float(item.price),
                    'message': f'Payment of ${item.price:.2f} required to purchase {item.name}'
                }, status=status.HTTP_402_PAYMENT_REQUIRED)
            else:
                # Free item - process immediately
                purchase = UserVirtualItem.objects.create(
                    user=user,
                    item=item,
                    purchase_price=item.price,
                    status='completed',
                    payment_method='free'
                )

                return Response({
                    'success': True,
                    'message': 'Virtual item purchased successfully',
                    'purchase_id': purchase.id,
                })
        else:
            return Response({
                'success': False,
                'message': 'Invalid payment method. Use "store_points" or "real_money".'
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error purchasing virtual item: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_virtual_items(request):
    """Get user's purchased virtual items"""
    try:
        user = request.user
        purchases = UserVirtualItem.objects.filter(user=user).select_related('item')
        
        items_data = []
        for purchase in purchases:
            items_data.append({
                'id': purchase.id,
                'item_name': purchase.item.name,
                'item_category': purchase.item.category,
                'purchase_price': str(purchase.purchase_price),
                'purchase_date': purchase.purchase_date.isoformat(),
                'status': purchase.status,
                'effects': purchase.item.effects,
            })
        
        return Response({
            'success': True,
            'items': items_data
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error fetching user virtual items: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def point_boosts_list(request):
    """Get available point boost packages"""
    try:
        # Return mock point boost packages since they're not in the models yet
        packages = [
            {
                'id': 1,
                'name': 'Quick Start',
                'description': 'Perfect for getting started quickly',
                'base_points': 200,
                'bonus_points': 50,
                'total_points': 250,
                'price': '1.99',
                'currency': 'USD',
                'is_active': True,
                'is_popular': False,
            },
            {
                'id': 2,
                'name': 'Power Boost',
                'description': 'Most popular choice for regular users',
                'base_points': 700,
                'bonus_points': 100,
                'total_points': 800,
                'price': '4.99',
                'currency': 'USD',
                'is_active': True,
                'is_popular': True,
            },
            {
                'id': 3,
                'name': 'Mega Boost',
                'description': 'Great value for serious earners',
                'base_points': 1800,
                'bonus_points': 200,
                'total_points': 2000,
                'price': '9.99',
                'currency': 'USD',
                'is_active': True,
                'is_popular': False,
            },
            {
                'id': 4,
                'name': 'Ultimate Boost',
                'description': 'Maximum points for power users',
                'base_points': 4500,
                'bonus_points': 500,
                'total_points': 5000,
                'price': '19.99',
                'currency': 'USD',
                'is_active': True,
                'is_popular': False,
            },
        ]
        
        return Response({
            'success': True,
            'packages': packages
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error fetching point boosts: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def purchase_point_boost(request, boost_id):
    """Purchase a point boost package with wallet or external payment"""
    try:
        user = request.user
        payment_method = request.data.get('payment_method', 'wallet')  # Default to wallet

        # Map package IDs to service package names
        package_mapping = {
            1: 'quick_start',
            2: 'power_boost',
            3: 'mega_boost',
            4: 'ultimate_boost',
        }

        if boost_id not in package_mapping:
            return Response({
                'success': False,
                'message': 'Point boost package not found'
            }, status=status.HTTP_404_NOT_FOUND)

        service_package_name = package_mapping[boost_id]

        # Use the monetization service to purchase the point boost
        success, result = MonetizationService.purchase_point_boost(
            user=user,
            package=service_package_name,
            payment_method=payment_method
        )

        if success:
            if isinstance(result, str):
                # Wallet payment successful - points awarded immediately
                return Response({
                    'success': True,
                    'message': result,
                    'payment_method': 'wallet',
                    'points_awarded': True
                })
            else:
                # External payment required - create payment order
                try:
                    from payments.views import create_payment_order
                    from rest_framework.test import APIRequestFactory

                    # Create a request for the payment system
                    factory = APIRequestFactory()
                    payment_request = factory.post('/api/v1/payments/create-order/', {
                        'amount': result['amount'],
                        'purpose': 'point_boost',
                        'description': f'Point boost package - {result["total_points"]} points'
                    }, format='json')
                    payment_request.user = user

                    # Create the payment order
                    payment_response = create_payment_order(payment_request)

                    if payment_response.status_code == 200 and payment_response.data.get('success'):
                        return Response({
                            'success': False,
                            'payment_required': True,
                            'package_id': boost_id,
                            'package_name': service_package_name.replace('_', ' ').title(),
                            'price': result['amount'],
                            'points': result['total_points'],
                            'order_id': payment_response.data.get('order_id'),
                            'approval_url': payment_response.data.get('approval_url'),
                            'transaction_id': payment_response.data.get('transaction_id'),
                            'message': f'Payment of ${result["amount"]:.2f} required to purchase {result["total_points"]} points'
                        }, status=status.HTTP_402_PAYMENT_REQUIRED)
                    else:
                        return Response({
                            'success': False,
                            'message': 'Failed to create payment order'
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                except Exception as e:
                    return Response({
                        'success': False,
                        'message': f'Error creating payment: {str(e)}'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            # Error or insufficient wallet balance
            if 'wallet balance' in result.lower():
                return Response({
                    'success': False,
                    'wallet_insufficient': True,
                    'message': result,
                    'suggested_action': 'Add money to wallet or use external payment'
                }, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({
                    'success': False,
                    'message': result
                }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error purchasing point boost: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def confirm_point_boost_payment(request):
    """Confirm point boost payment and award points"""
    try:
        transaction_id = request.data.get('transaction_id')
        payment_status = request.data.get('status')
        package_id = request.data.get('package_id')

        if not transaction_id or not payment_status or not package_id:
            return Response({
                'success': False,
                'message': 'Transaction ID, status, and package ID are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Find the transaction
        transaction = None

        # Try PaymentTransaction first
        try:
            from payments.models import PaymentTransaction
            transaction = PaymentTransaction.objects.get(
                id=transaction_id,
                user=request.user,
                payment_purpose='point_boost',
                status__in=['pending', 'completed']
            )
        except PaymentTransaction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Transaction not found'
            }, status=status.HTTP_404_NOT_FOUND)

        if payment_status == 'COMPLETED':
            # Map package IDs to service package names
            package_mapping = {
                1: 'quick_start',
                2: 'power_boost',
                3: 'mega_boost',
                4: 'ultimate_boost',
            }

            package_id_int = int(package_id)
            if package_id_int not in package_mapping:
                return Response({
                    'success': False,
                    'message': 'Invalid package ID'
                }, status=status.HTTP_400_BAD_REQUEST)

            service_package_name = package_mapping[package_id_int]

            # Use the monetization service to purchase the point boost
            success, message = MonetizationService.purchase_point_boost(
                request.user,
                service_package_name,
                'paypal'
            )

            if success:
                # Mark transaction as completed
                transaction.status = 'completed'
                transaction.completed_at = timezone.now()
                transaction.save()

                # Get points from service response message
                points_awarded = message.split()[-2] if 'points' in message else 'some'

                return Response({
                    'success': True,
                    'message': message,
                    'points_awarded': points_awarded
                })
            else:
                return Response({
                    'success': False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)
        else:
            # Payment failed
            transaction.status = 'failed'
            transaction.save()

            return Response({
                'success': False,
                'message': 'Payment was not completed'
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error confirming point boost payment: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def referral_data(request):
    """Get user's referral data"""
    try:
        user = request.user
        
        # Get user's referrals
        referrals = ReferralProgram.objects.filter(referrer=user)
        
        referrals_data = []
        total_earned = 0
        
        for referral in referrals:
            earned_amount = 0
            
            # Calculate earnings based on milestones
            if referral.join_reward_given:
                earned_amount += 2.0  # Join bonus
            if referral.level_5_reward_given:
                earned_amount += 2.0  # Level 5 bonus
            if referral.premium_reward_given:
                earned_amount += 5.0  # Premium bonus
            
            total_earned += earned_amount
            
            referrals_data.append({
                'id': str(referral.id),
                'referrer_id': str(referral.referrer.id),
                'referee_id': str(referral.referee.id),
                'referral_code': referral.referral_code,
                'friend_name': referral.referee.get_full_name() or referral.referee.username,
                'friend_level': 5 if referral.referee_reached_level_5 else 3,  # Mock level
                'joined_at': referral.referee_joined_at,
                'earned_amount': earned_amount,
                'went_premium': referral.referee_went_premium,
                'reached_level_5': referral.referee_reached_level_5,
                'made_purchase': referral.referee_made_purchase,
                'total_revenue': float(referral.total_revenue_generated),
            })
        
        # Get or create referral code for user
        from .models import UserReferralCode
        referral_code_obj = UserReferralCode.get_or_create_for_user(user)
        referral_code = referral_code_obj.code
        
        stats = {
            'total_referrals': len(referrals_data),
            'total_earned': total_earned,
            'premium_referrals': sum(1 for r in referrals_data if r['went_premium']),
            'active_referrals': len(referrals_data),
            'level_5_referrals': sum(1 for r in referrals_data if r['reached_level_5']),
            'referral_code': referral_code,
        }
        
        return Response({
            'success': True,
            'referrals': referrals_data,
            'stats': stats,
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error fetching referral data: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_referral_code(request):
    """Get user's referral code"""
    try:
        user = request.user
        from .models import UserReferralCode

        # Get or create referral code for user
        referral_code_obj = UserReferralCode.get_or_create_for_user(user)

        return Response({
            'success': True,
            'referral_code': referral_code_obj.code,
            'usage_count': referral_code_obj.usage_count,
            'created_at': referral_code_obj.created_at,
            'is_active': referral_code_obj.is_active
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error getting referral code: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def validate_referral_code(request):
    """Validate a referral code"""
    try:
        code = request.data.get('code', '').strip().upper()

        if not code:
            return Response({
                'success': False,
                'valid': False,
                'message': 'Referral code is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        from .models import UserReferralCode

        try:
            referral_code_obj = UserReferralCode.objects.get(code=code, is_active=True)
            referrer = referral_code_obj.user

            return Response({
                'success': True,
                'valid': True,
                'referrer_username': referrer.username,
                'referrer_id': referrer.id,
                'usage_count': referral_code_obj.usage_count,
                'message': f'Valid referral code from {referrer.username}'
            })

        except UserReferralCode.DoesNotExist:
            return Response({
                'success': True,
                'valid': False,
                'message': 'Invalid or inactive referral code'
            })

    except Exception as e:
        return Response({
            'success': False,
            'valid': False,
            'message': f'Error validating referral code: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def monetization_settings(request):
    """Get monetization settings"""
    try:
        settings = MonetizationSettings.objects.first()
        
        if not settings:
            # Return default settings
            settings_data = {
                'premium_monthly_price': '9.99',
                'premium_point_multiplier': '2.0',
                'premium_daily_bonus': 15,
                'monetization_enabled': True,
                'premium_enabled': True,
            }
        else:
            settings_data = {
                'premium_monthly_price': str(settings.premium_monthly_price),
                'premium_point_multiplier': str(settings.premium_point_multiplier),
                'premium_daily_bonus': settings.premium_daily_bonus,
                'monetization_enabled': settings.monetization_enabled,
                'premium_enabled': settings.premium_enabled,
            }
        
        return Response({
            'success': True,
            'settings': settings_data
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error fetching monetization settings: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Alias for the subscribe endpoint
subscribe_to_premium = premium_subscribe
