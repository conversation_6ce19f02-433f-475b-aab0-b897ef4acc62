"""
Management command to populate regional data (countries and regions)
"""

from django.core.management.base import BaseCommand
from regional.models import Region, Country


class Command(BaseCommand):
    help = 'Populate database with countries and regions data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Delete existing regional data before creating new data',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting existing regional data...')
            Country.objects.all().delete()
            Region.objects.all().delete()

        self.stdout.write('Creating regions and countries...')
        
        # Create regions
        regions_data = [
            {'name': 'Africa', 'code': 'AF', 'description': 'African continent'},
            {'name': 'Asia', 'code': 'AS', 'description': 'Asian continent'},
            {'name': 'Europe', 'code': 'EU', 'description': 'European continent'},
            {'name': 'North America', 'code': 'NA', 'description': 'North American continent'},
            {'name': 'South America', 'code': 'SA', 'description': 'South American continent'},
            {'name': 'Oceania', 'code': 'OC', 'description': 'Oceania region'},
        ]
        
        regions = {}
        for region_data in regions_data:
            region, created = Region.objects.get_or_create(
                code=region_data['code'],
                defaults=region_data
            )
            regions[region_data['code']] = region
            if created:
                self.stdout.write(f'  Created region: {region.name}')

        # Create countries (focusing on African countries and some global ones)
        countries_data = [
            # African countries
            {'name': 'Uganda', 'code': 'UG', 'code_3': 'UGA', 'region': 'AF', 'currency_code': 'UGX', 'currency_name': 'Ugandan Shilling', 'phone_code': '+256', 'flag_emoji': '🇺🇬', 'priority': 100},
            {'name': 'Kenya', 'code': 'KE', 'code_3': 'KEN', 'region': 'AF', 'currency_code': 'KES', 'currency_name': 'Kenyan Shilling', 'phone_code': '+254', 'flag_emoji': '🇰🇪', 'priority': 90},
            {'name': 'Tanzania', 'code': 'TZ', 'code_3': 'TZA', 'region': 'AF', 'currency_code': 'TZS', 'currency_name': 'Tanzanian Shilling', 'phone_code': '+255', 'flag_emoji': '🇹🇿', 'priority': 85},
            {'name': 'Rwanda', 'code': 'RW', 'code_3': 'RWA', 'region': 'AF', 'currency_code': 'RWF', 'currency_name': 'Rwandan Franc', 'phone_code': '+250', 'flag_emoji': '🇷🇼', 'priority': 80},
            {'name': 'Nigeria', 'code': 'NG', 'code_3': 'NGA', 'region': 'AF', 'currency_code': 'NGN', 'currency_name': 'Nigerian Naira', 'phone_code': '+234', 'flag_emoji': '🇳🇬', 'priority': 95},
            {'name': 'Ghana', 'code': 'GH', 'code_3': 'GHA', 'region': 'AF', 'currency_code': 'GHS', 'currency_name': 'Ghanaian Cedi', 'phone_code': '+233', 'flag_emoji': '🇬🇭', 'priority': 75},
            {'name': 'South Africa', 'code': 'ZA', 'code_3': 'ZAF', 'region': 'AF', 'currency_code': 'ZAR', 'currency_name': 'South African Rand', 'phone_code': '+27', 'flag_emoji': '🇿🇦', 'priority': 85},
            {'name': 'Ethiopia', 'code': 'ET', 'code_3': 'ETH', 'region': 'AF', 'currency_code': 'ETB', 'currency_name': 'Ethiopian Birr', 'phone_code': '+251', 'flag_emoji': '🇪🇹', 'priority': 70},
            {'name': 'Morocco', 'code': 'MA', 'code_3': 'MAR', 'region': 'AF', 'currency_code': 'MAD', 'currency_name': 'Moroccan Dirham', 'phone_code': '+212', 'flag_emoji': '🇲🇦', 'priority': 65},
            {'name': 'Egypt', 'code': 'EG', 'code_3': 'EGY', 'region': 'AF', 'currency_code': 'EGP', 'currency_name': 'Egyptian Pound', 'phone_code': '+20', 'flag_emoji': '🇪🇬', 'priority': 70},
            
            # Major global countries
            {'name': 'United States', 'code': 'US', 'code_3': 'USA', 'region': 'NA', 'currency_code': 'USD', 'currency_name': 'US Dollar', 'phone_code': '+1', 'flag_emoji': '🇺🇸', 'priority': 60},
            {'name': 'United Kingdom', 'code': 'GB', 'code_3': 'GBR', 'region': 'EU', 'currency_code': 'GBP', 'currency_name': 'British Pound', 'phone_code': '+44', 'flag_emoji': '🇬🇧', 'priority': 55},
            {'name': 'Canada', 'code': 'CA', 'code_3': 'CAN', 'region': 'NA', 'currency_code': 'CAD', 'currency_name': 'Canadian Dollar', 'phone_code': '+1', 'flag_emoji': '🇨🇦', 'priority': 50},
            {'name': 'Australia', 'code': 'AU', 'code_3': 'AUS', 'region': 'OC', 'currency_code': 'AUD', 'currency_name': 'Australian Dollar', 'phone_code': '+61', 'flag_emoji': '🇦🇺', 'priority': 45},
            {'name': 'Germany', 'code': 'DE', 'code_3': 'DEU', 'region': 'EU', 'currency_code': 'EUR', 'currency_name': 'Euro', 'phone_code': '+49', 'flag_emoji': '🇩🇪', 'priority': 40},
            {'name': 'France', 'code': 'FR', 'code_3': 'FRA', 'region': 'EU', 'currency_code': 'EUR', 'currency_name': 'Euro', 'phone_code': '+33', 'flag_emoji': '🇫🇷', 'priority': 35},
            {'name': 'India', 'code': 'IN', 'code_3': 'IND', 'region': 'AS', 'currency_code': 'INR', 'currency_name': 'Indian Rupee', 'phone_code': '+91', 'flag_emoji': '🇮🇳', 'priority': 30},
            {'name': 'China', 'code': 'CN', 'code_3': 'CHN', 'region': 'AS', 'currency_code': 'CNY', 'currency_name': 'Chinese Yuan', 'phone_code': '+86', 'flag_emoji': '🇨🇳', 'priority': 25},
            {'name': 'Japan', 'code': 'JP', 'code_3': 'JPN', 'region': 'AS', 'currency_code': 'JPY', 'currency_name': 'Japanese Yen', 'phone_code': '+81', 'flag_emoji': '🇯🇵', 'priority': 20},
            {'name': 'Brazil', 'code': 'BR', 'code_3': 'BRA', 'region': 'SA', 'currency_code': 'BRL', 'currency_name': 'Brazilian Real', 'phone_code': '+55', 'flag_emoji': '🇧🇷', 'priority': 15},
        ]
        
        created_count = 0
        for country_data in countries_data:
            region_code = country_data.pop('region')
            country_data['region'] = regions[region_code]
            
            country, created = Country.objects.get_or_create(
                code=country_data['code'],
                defaults=country_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(f'  Created country: {country.display_name}')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {len(regions_data)} regions and {created_count} countries'
            )
        )
