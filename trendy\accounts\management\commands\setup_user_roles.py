from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import get_user_model
from blog.models import Post, Comment
from interactive.models import Poll, Quiz

User = get_user_model()


class Command(BaseCommand):
    help = 'Set up user roles and permissions for the Trendy application'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing groups and permissions',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('🔄 Resetting existing groups and permissions...')
            self.reset_groups_and_permissions()

        self.stdout.write('🚀 Setting up user roles and permissions...')
        
        # Create groups
        self.create_groups()
        
        # Create custom permissions
        self.create_custom_permissions()
        
        # Assign permissions to groups
        self.assign_permissions_to_groups()
        
        # Update existing users
        self.update_existing_users()
        
        self.stdout.write(
            self.style.SUCCESS('✅ User roles and permissions setup completed!')
        )

    def reset_groups_and_permissions(self):
        """Reset existing groups and permissions"""
        # Remove users from groups
        for group in Group.objects.filter(name__in=['Content Creators', 'Regular Users']):
            group.user_set.clear()
            
        # Delete groups
        Group.objects.filter(name__in=['Content Creators', 'Regular Users']).delete()
        
        self.stdout.write('  Existing groups and permissions reset')

    def create_groups(self):
        """Create user groups"""
        self.stdout.write('👥 Creating user groups...')
        
        # Content Creators Group
        content_creators, created = Group.objects.get_or_create(name='Content Creators')
        if created:
            self.stdout.write('  ✅ Created "Content Creators" group')
        else:
            self.stdout.write('  ℹ️  "Content Creators" group already exists')
            
        # Regular Users Group
        regular_users, created = Group.objects.get_or_create(name='Regular Users')
        if created:
            self.stdout.write('  ✅ Created "Regular Users" group')
        else:
            self.stdout.write('  ℹ️  "Regular Users" group already exists')

    def create_custom_permissions(self):
        """Create custom permissions for content management"""
        self.stdout.write('🔐 Creating custom permissions...')
        
        # Get content types
        post_ct = ContentType.objects.get_for_model(Post)
        comment_ct = ContentType.objects.get_for_model(Comment)
        poll_ct = ContentType.objects.get_for_model(Poll)
        quiz_ct = ContentType.objects.get_for_model(Quiz)
        
        # Custom permissions for posts
        post_permissions = [
            ('can_create_post', 'Can create blog posts'),
            ('can_publish_post', 'Can publish blog posts'),
            ('can_edit_own_post', 'Can edit own blog posts'),
            ('can_delete_own_post', 'Can delete own blog posts'),
            ('can_moderate_posts', 'Can moderate all posts'),
        ]
        
        # Custom permissions for interactive content
        interactive_permissions = [
            ('can_create_poll', 'Can create polls'),
            ('can_create_quiz', 'Can create quizzes'),
            ('can_moderate_interactive', 'Can moderate interactive content'),
        ]
        
        # Create post permissions
        for codename, name in post_permissions:
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                content_type=post_ct,
                defaults={'name': name}
            )
            if created:
                self.stdout.write(f'  ✅ Created permission: {name}')
        
        # Create interactive content permissions
        for codename, name in interactive_permissions:
            # Use poll content type for interactive permissions
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                content_type=poll_ct,
                defaults={'name': name}
            )
            if created:
                self.stdout.write(f'  ✅ Created permission: {name}')

    def assign_permissions_to_groups(self):
        """Assign permissions to user groups"""
        self.stdout.write('🎯 Assigning permissions to groups...')
        
        # Get groups
        content_creators = Group.objects.get(name='Content Creators')
        regular_users = Group.objects.get(name='Regular Users')
        
        # Get content types
        post_ct = ContentType.objects.get_for_model(Post)
        comment_ct = ContentType.objects.get_for_model(Comment)
        poll_ct = ContentType.objects.get_for_model(Poll)
        
        # Content Creators permissions
        content_creator_permissions = [
            # Post permissions
            'can_create_post',
            'can_publish_post', 
            'can_edit_own_post',
            'can_delete_own_post',
            # Interactive content permissions
            'can_create_poll',
            'can_create_quiz',
            # Default Django permissions for their own content
            'add_post',
            'change_post',
            'delete_post',
            'add_comment',
            'change_comment',
            'delete_comment',
        ]
        
        # Regular Users permissions (engagement only)
        regular_user_permissions = [
            # Comment permissions
            'add_comment',
            'change_comment',  # Only their own comments
            'delete_comment',  # Only their own comments
        ]
        
        # Assign permissions to Content Creators
        for perm_codename in content_creator_permissions:
            try:
                if perm_codename.startswith('can_'):
                    # Custom permissions
                    if 'post' in perm_codename:
                        permission = Permission.objects.get(codename=perm_codename, content_type=post_ct)
                    else:
                        permission = Permission.objects.get(codename=perm_codename, content_type=poll_ct)
                else:
                    # Default Django permissions
                    if 'post' in perm_codename:
                        permission = Permission.objects.get(codename=perm_codename, content_type=post_ct)
                    else:
                        permission = Permission.objects.get(codename=perm_codename, content_type=comment_ct)
                
                content_creators.permissions.add(permission)
                
            except Permission.DoesNotExist:
                self.stdout.write(f'  ⚠️  Permission not found: {perm_codename}')
        
        # Assign permissions to Regular Users
        for perm_codename in regular_user_permissions:
            try:
                permission = Permission.objects.get(codename=perm_codename, content_type=comment_ct)
                regular_users.permissions.add(permission)
            except Permission.DoesNotExist:
                self.stdout.write(f'  ⚠️  Permission not found: {perm_codename}')
        
        self.stdout.write('  ✅ Permissions assigned to groups')

    def update_existing_users(self):
        """Update existing users with appropriate groups"""
        self.stdout.write('👤 Updating existing users...')
        
        # Get groups
        content_creators = Group.objects.get(name='Content Creators')
        regular_users = Group.objects.get(name='Regular Users')
        
        # Update all existing users
        for user in User.objects.all():
            if user.is_staff or user.is_superuser:
                # Admin users don't need groups (they have all permissions)
                self.stdout.write(f'  👑 Admin user: {user.username} (no group needed)')
                continue
            
            # Check if user has created any posts
            has_posts = Post.objects.filter(author=user).exists()
            
            if has_posts:
                # User has created content, make them a content creator
                user.groups.add(content_creators)
                self.stdout.write(f'  ✍️  Added {user.username} to Content Creators (has posts)')
            else:
                # Regular user
                user.groups.add(regular_users)
                self.stdout.write(f'  👤 Added {user.username} to Regular Users')
