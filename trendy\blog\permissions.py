from rest_framework import permissions


class IsAuthorOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow authors of an object to edit it.
    Staff users can edit any object.
    """
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the author of the object or staff.
        return obj.author == request.user or request.user.is_staff


class CanCreateContent(permissions.BasePermission):
    """
    Custom permission to check if user can create content.
    Only content creators, staff, and superusers can create posts.
    """
    def has_permission(self, request, view):
        # Allow read operations for everyone
        if request.method in permissions.SAFE_METHODS:
            return True

        # For write operations, check if user is authenticated
        if not request.user.is_authenticated:
            return False

        # Check if user can create content
        return request.user.can_create_content()


class CanModerateContent(permissions.BasePermission):
    """
    Custom permission for content moderation.
    Only staff and users with moderation permissions can moderate.
    """
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        return request.user.can_moderate_content()


class IsContentCreatorOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow content creators to create posts.
    Everyone can read, only content creators can write.
    """
    def has_permission(self, request, view):
        # Read permissions for everyone
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions only for authenticated users
        if not request.user.is_authenticated:
            return False

        # Check if user is content creator, staff, or superuser
        return (
            request.user.is_content_creator or
            request.user.is_staff or
            request.user.is_superuser
        )

    def has_object_permission(self, request, view, obj):
        # Read permissions for everyone
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions only for the author or staff
        return obj.author == request.user or request.user.is_staff


class CanCreateInteractiveContent(permissions.BasePermission):
    """
    Custom permission for creating interactive content (polls, quizzes).
    Only content creators and staff can create interactive content.
    """
    def has_permission(self, request, view):
        # Allow read operations for everyone
        if request.method in permissions.SAFE_METHODS:
            return True

        # For write operations, check if user is authenticated
        if not request.user.is_authenticated:
            return False

        # Check if user has permission to create interactive content
        return (
            request.user.has_perm('interactive.can_create_poll') or
            request.user.has_perm('interactive.can_create_quiz') or
            request.user.is_staff or
            request.user.is_superuser
        )