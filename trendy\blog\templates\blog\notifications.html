{% extends 'blog/base.html' %}

{% block title %}Notifications - Trendy Blog{% endblock %}

{% block content %}
<div class="notifications-page">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="notifications-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-bell me-2"></i>Notifications</h2>
                    <div class="notification-actions">
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="markAllAsRead()">
                            <i class="fas fa-check-double me-1"></i>Mark all read
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="refreshNotifications()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                </div>
            </div>

            <div class="notifications-container">
                <!-- Sample notifications - in a real app, these would come from the backend -->
                <div class="notification-item unread">
                    <div class="notification-icon">
                        <i class="fas fa-heart text-danger"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-text">
                            <strong>John Doe</strong> liked your post "Getting Started with Django"
                        </div>
                        <div class="notification-time">2 minutes ago</div>
                    </div>
                    <div class="notification-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(this)">
                            <i class="fas fa-check"></i>
                        </button>
                    </div>
                </div>

                <div class="notification-item unread">
                    <div class="notification-icon">
                        <i class="fas fa-comment text-primary"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-text">
                            <strong>Jane Smith</strong> commented on your post "Web Development Tips"
                        </div>
                        <div class="notification-time">15 minutes ago</div>
                    </div>
                    <div class="notification-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(this)">
                            <i class="fas fa-check"></i>
                        </button>
                    </div>
                </div>

                <div class="notification-item">
                    <div class="notification-icon">
                        <i class="fas fa-user-plus text-success"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-text">
                            <strong>Mike Johnson</strong> started following you
                        </div>
                        <div class="notification-time">1 hour ago</div>
                    </div>
                </div>

                <div class="notification-item">
                    <div class="notification-icon">
                        <i class="fas fa-star text-warning"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-text">
                            Your post <strong>"React Best Practices"</strong> has reached 100 likes!
                        </div>
                        <div class="notification-time">3 hours ago</div>
                    </div>
                </div>

                <div class="notification-item">
                    <div class="notification-icon">
                        <i class="fas fa-bell text-info"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-text">
                            Welcome to Trendy! Complete your profile to get started.
                        </div>
                        <div class="notification-time">1 day ago</div>
                    </div>
                </div>

                <!-- Empty state (hidden by default) -->
                <div class="empty-notifications" style="display: none;">
                    <div class="text-center py-5">
                        <i class="fas fa-bell-slash text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                        <h4 class="mt-3 text-muted">No notifications yet</h4>
                        <p class="text-muted">When you get notifications, they'll appear here.</p>
                    </div>
                </div>
            </div>

            <!-- Load more button -->
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary" onclick="loadMoreNotifications()">
                    <i class="fas fa-chevron-down me-2"></i>Load More
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.notifications-page {
    min-height: 70vh;
}

.notifications-header h2 {
    color: var(--text-color);
    font-weight: 600;
}

.notification-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.notification-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.notification-item.unread {
    border-left-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.02);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-text {
    color: var(--text-color);
    line-height: 1.5;
    margin-bottom: 5px;
}

.notification-time {
    color: #6c757d;
    font-size: 0.875rem;
}

.notification-actions {
    flex-shrink: 0;
}

.notification-actions .btn {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-item:hover .notification-actions .btn {
    opacity: 1;
}

.notification-item.unread .notification-actions .btn {
    opacity: 1;
}

.empty-notifications {
    background: white;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
}

@media (max-width: 768px) {
    .notifications-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 15px;
    }
    
    .notification-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .notification-item {
        padding: 15px;
    }
    
    .notification-icon {
        width: 35px;
        height: 35px;
    }
}
</style>

<script>
function markAsRead(button) {
    const notificationItem = button.closest('.notification-item');
    notificationItem.classList.remove('unread');
    button.style.display = 'none';
    
    // In a real app, you would make an API call here
    console.log('Marking notification as read');
}

function markAllAsRead() {
    const unreadNotifications = document.querySelectorAll('.notification-item.unread');
    unreadNotifications.forEach(item => {
        item.classList.remove('unread');
        const button = item.querySelector('.notification-actions .btn');
        if (button) {
            button.style.display = 'none';
        }
    });
    
    // In a real app, you would make an API call here
    console.log('Marking all notifications as read');
    
    // Show success message
    showToast('All notifications marked as read', 'success');
}

function refreshNotifications() {
    // In a real app, you would fetch new notifications from the API
    console.log('Refreshing notifications');
    showToast('Notifications refreshed', 'info');
}

function loadMoreNotifications() {
    // In a real app, you would load more notifications from the API
    console.log('Loading more notifications');
    showToast('Loading more notifications...', 'info');
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 3000);
}

// Check if there are no notifications and show empty state
document.addEventListener('DOMContentLoaded', function() {
    const notifications = document.querySelectorAll('.notification-item');
    const emptyState = document.querySelector('.empty-notifications');
    
    if (notifications.length === 0) {
        emptyState.style.display = 'block';
    }
});
</script>
{% endblock %}
