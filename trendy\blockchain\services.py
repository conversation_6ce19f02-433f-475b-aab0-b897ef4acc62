"""
Blockchain services for Trendy app
"""

from web3 import Web3
from eth_account import Account
from decimal import Decimal
import json
import logging
from django.conf import settings
from django.utils import timezone
from cryptography.fernet import Fernet
from .models import (
    BlockchainNetwork, SmartContract, UserWalletAddress, 
    BlockchainTransaction, TokenBalance, NFTAsset, StakingPool, UserStake
)

logger = logging.getLogger(__name__)

class BlockchainService:
    """Main blockchain service for interacting with smart contracts"""
    
    def __init__(self, network_name='polygon_testnet'):
        self.network = BlockchainNetwork.objects.get(name=network_name, is_active=True)
        self.w3 = Web3(Web3.HTTPProvider(self.network.rpc_url))
        self.admin_private_key = settings.BLOCKCHAIN_ADMIN_PRIVATE_KEY
        self.admin_address = Account.from_key(self.admin_private_key).address
        self.encryption_key = settings.WALLET_ENCRYPTION_KEY.encode()
        self.cipher = Fernet(self.encryption_key)
        
    def create_user_wallet(self, user, is_active=False):
        """Create a new blockchain wallet for user"""
        try:
            # Generate new account
            account = Account.create()

            # Encrypt private key
            encrypted_private_key = self.cipher.encrypt(account.key.hex().encode())

            # Save to database
            wallet_address = UserWalletAddress.objects.create(
                user=user,
                network=self.network,
                address=account.address,
                private_key_encrypted=encrypted_private_key.decode(),
                is_primary=True,
                is_active=is_active  # Wallet starts inactive by default
            )

            # Generate activation code if wallet is inactive
            if not is_active:
                activation_code = wallet_address.generate_activation_code()
                logger.info(f"Created inactive wallet {account.address} for user {user.username} with activation code {activation_code}")
            else:
                logger.info(f"Created active wallet {account.address} for user {user.username}")

            return wallet_address

        except Exception as e:
            logger.error(f"Error creating wallet for user {user.id}: {str(e)}")
            return None

    def send_activation_code(self, user):
        """Send activation code to user (via email or other method)"""
        try:
            wallet = UserWalletAddress.objects.filter(
                user=user,
                network=self.network,
                is_active=False
            ).first()

            if not wallet:
                return None, "No inactive wallet found"

            # Generate new activation code
            activation_code = wallet.generate_activation_code()

            # Here you would send the code via email, SMS, etc.
            # For now, we'll just log it (in production, implement proper sending)
            logger.info(f"Activation code for user {user.username}: {activation_code}")

            # TODO: Implement actual email/SMS sending
            # Example:
            # send_activation_email(user.email, activation_code)

            return activation_code, "Activation code sent successfully"

        except Exception as e:
            logger.error(f"Error sending activation code for user {user.id}: {str(e)}")
            return None, f"Error sending activation code: {str(e)}"

    def activate_user_wallet(self, user, activation_code):
        """Activate user's wallet with the provided code"""
        try:
            wallet = UserWalletAddress.objects.filter(
                user=user,
                network=self.network,
                is_active=False
            ).first()

            if not wallet:
                return False, "No inactive wallet found"

            if wallet.activate_wallet(activation_code):
                logger.info(f"Wallet activated for user {user.username}")
                return True, "Wallet activated successfully"
            else:
                return False, "Invalid or expired activation code"

        except Exception as e:
            logger.error(f"Error activating wallet for user {user.id}: {str(e)}")
            return False, f"Error activating wallet: {str(e)}"
    
    def get_user_wallet(self, user):
        """Get user's primary wallet address"""
        try:
            return UserWalletAddress.objects.get(
                user=user,
                network=self.network,
                is_primary=True
            )
        except UserWalletAddress.DoesNotExist:
            return self.create_user_wallet(user)
    
    def decrypt_private_key(self, wallet_address):
        """Decrypt user's private key"""
        try:
            encrypted_key = wallet_address.private_key_encrypted.encode()
            decrypted_key = self.cipher.decrypt(encrypted_key)
            return decrypted_key.decode()
        except Exception as e:
            logger.error(f"Error decrypting private key: {str(e)}")
            return None
    
    def get_contract(self, contract_type):
        """Get smart contract instance"""
        try:
            contract_obj = SmartContract.objects.get(
                network=self.network,
                contract_type=contract_type,
                is_active=True
            )
            
            contract = self.w3.eth.contract(
                address=contract_obj.address,
                abi=contract_obj.abi
            )
            
            return contract, contract_obj
        except SmartContract.DoesNotExist:
            logger.error(f"Contract {contract_type} not found for network {self.network.name}")
            return None, None
    
    def send_transaction(self, contract_function, user_wallet=None, gas_limit=200000):
        """Send a transaction to the blockchain"""
        try:
            # Use admin wallet if no user wallet specified
            if user_wallet:
                private_key = self.decrypt_private_key(user_wallet)
                from_address = user_wallet.address
            else:
                private_key = self.admin_private_key
                from_address = self.admin_address
            
            # Build transaction
            transaction = contract_function.build_transaction({
                'from': from_address,
                'gas': gas_limit,
                'gasPrice': self.w3.to_wei(str(self.network.gas_price_gwei), 'gwei'),
                'nonce': self.w3.eth.get_transaction_count(from_address),
                'chainId': self.network.chain_id
            })
            
            # Sign transaction
            signed_txn = self.w3.eth.account.sign_transaction(transaction, private_key)
            
            # Send transaction
            tx_hash = self.w3.eth.send_raw_transaction(signed_txn.rawTransaction)
            
            return tx_hash.hex()
            
        except Exception as e:
            logger.error(f"Error sending transaction: {str(e)}")
            return None
    
    def mint_reward_tokens(self, user, amount, description="Achievement reward"):
        """Mint TRD tokens as rewards to user"""
        try:
            user_wallet = self.get_user_wallet(user)
            if not user_wallet:
                return False, "User wallet not found"
            
            # Get token contract
            contract, contract_obj = self.get_contract('token')
            if not contract:
                return False, "Token contract not found"
            
            # Convert amount to wei (18 decimals)
            amount_wei = int(amount * 10**18)
            
            # Send tokens from admin to user
            tx_hash = self.send_transaction(
                contract.functions.transfer(user_wallet.address, amount_wei)
            )
            
            if tx_hash:
                # Record transaction
                blockchain_tx = BlockchainTransaction.objects.create(
                    user=user,
                    network=self.network,
                    contract=contract_obj,
                    transaction_type='token_reward',
                    blockchain_hash=tx_hash,
                    from_address=self.admin_address,
                    to_address=user_wallet.address,
                    amount=amount,
                    gas_limit=200000,
                    gas_price=int(self.w3.to_wei(str(self.network.gas_price_gwei), 'gwei')),
                    transaction_fee=Decimal('0.001'),  # Estimated fee
                    description=description
                )
                
                # Update user token balance
                self.update_token_balance(user, contract_obj, amount)
                
                logger.info(f"Minted {amount} TRD tokens to {user.username}")
                return True, tx_hash
            
            return False, "Transaction failed"
            
        except Exception as e:
            logger.error(f"Error minting tokens for user {user.id}: {str(e)}")
            return False, str(e)
    
    def mint_achievement_nft(self, user, achievement_name, rarity, image_url, description=""):
        """Mint NFT achievement badge for user"""
        try:
            user_wallet = self.get_user_wallet(user)
            if not user_wallet:
                return False, "User wallet not found"
            
            # Get NFT contract
            contract, contract_obj = self.get_contract('nft')
            if not contract:
                return False, "NFT contract not found"
            
            # Generate token ID (simple incrementing)
            last_nft = NFTAsset.objects.filter(contract=contract_obj).order_by('-token_id').first()
            token_id = (last_nft.token_id + 1) if last_nft else 1
            
            # Mint NFT
            tx_hash = self.send_transaction(
                contract.functions.mintAchievement(
                    user_wallet.address,
                    token_id,
                    achievement_name,
                    rarity
                )
            )
            
            if tx_hash:
                # Record transaction
                blockchain_tx = BlockchainTransaction.objects.create(
                    user=user,
                    network=self.network,
                    contract=contract_obj,
                    transaction_type='nft_mint',
                    blockchain_hash=tx_hash,
                    from_address=self.admin_address,
                    to_address=user_wallet.address,
                    token_id=token_id,
                    gas_limit=300000,
                    gas_price=int(self.w3.to_wei(str(self.network.gas_price_gwei), 'gwei')),
                    transaction_fee=Decimal('0.002'),  # Estimated fee
                    description=f"Achievement NFT: {achievement_name}"
                )
                
                # Create NFT asset record
                nft_asset = NFTAsset.objects.create(
                    user=user,
                    network=self.network,
                    contract=contract_obj,
                    token_id=token_id,
                    name=achievement_name,
                    description=description,
                    image_url=image_url,
                    rarity=rarity,
                    mint_transaction=blockchain_tx
                )
                
                logger.info(f"Minted NFT {achievement_name} (#{token_id}) to {user.username}")
                return True, tx_hash, nft_asset
            
            return False, "Transaction failed", None
            
        except Exception as e:
            logger.error(f"Error minting NFT for user {user.id}: {str(e)}")
            return False, str(e), None
    
    def update_token_balance(self, user, contract_obj, amount_change):
        """Update user's token balance in database"""
        try:
            balance, created = TokenBalance.objects.get_or_create(
                user=user,
                network=self.network,
                contract=contract_obj,
                defaults={'balance': Decimal('0')}
            )
            
            balance.balance += Decimal(str(amount_change))
            balance.save()
            
            return balance
            
        except Exception as e:
            logger.error(f"Error updating token balance: {str(e)}")
            return None
    
    def get_token_balance_from_blockchain(self, user_address, contract_address):
        """Get actual token balance from blockchain"""
        try:
            contract, _ = self.get_contract('token')
            if not contract:
                return 0
            
            balance_wei = contract.functions.balanceOf(user_address).call()
            balance = balance_wei / 10**18  # Convert from wei
            
            return balance
            
        except Exception as e:
            logger.error(f"Error getting blockchain balance: {str(e)}")
            return 0
    
    def verify_transaction(self, tx_hash):
        """Verify transaction status on blockchain"""
        try:
            receipt = self.w3.eth.get_transaction_receipt(tx_hash)
            
            # Update transaction record
            try:
                blockchain_tx = BlockchainTransaction.objects.get(blockchain_hash=tx_hash)
                blockchain_tx.status = 'confirmed' if receipt.status == 1 else 'failed'
                blockchain_tx.block_number = receipt.blockNumber
                blockchain_tx.block_hash = receipt.blockHash.hex()
                blockchain_tx.gas_used = receipt.gasUsed
                blockchain_tx.confirmations = self.w3.eth.block_number - receipt.blockNumber
                blockchain_tx.confirmed_at = timezone.now()
                blockchain_tx.save()
                
                return receipt.status == 1
                
            except BlockchainTransaction.DoesNotExist:
                logger.warning(f"Transaction {tx_hash} not found in database")
                return receipt.status == 1
            
        except Exception as e:
            logger.error(f"Error verifying transaction {tx_hash}: {str(e)}")
            return False

class StakingService:
    """Service for token staking operations"""
    
    def __init__(self, blockchain_service):
        self.blockchain_service = blockchain_service
    
    def stake_tokens(self, user, pool_id, amount):
        """Stake tokens in a staking pool"""
        try:
            pool = StakingPool.objects.get(id=pool_id, is_active=True)
            user_wallet = self.blockchain_service.get_user_wallet(user)
            
            if not user_wallet:
                return False, "User wallet not found"
            
            # Check minimum stake
            if amount < pool.minimum_stake:
                return False, f"Minimum stake is {pool.minimum_stake}"
            
            # Check maximum stake
            if pool.maximum_stake and amount > pool.maximum_stake:
                return False, f"Maximum stake is {pool.maximum_stake}"
            
            # Get staking contract
            contract, contract_obj = self.blockchain_service.get_contract('staking')
            if not contract:
                return False, "Staking contract not found"
            
            # Convert amount to wei
            amount_wei = int(amount * 10**18)
            
            # Send staking transaction
            tx_hash = self.blockchain_service.send_transaction(
                contract.functions.stake(pool_id, amount_wei),
                user_wallet=user_wallet
            )
            
            if tx_hash:
                # Record transaction
                blockchain_tx = BlockchainTransaction.objects.create(
                    user=user,
                    network=self.blockchain_service.network,
                    contract=contract_obj,
                    transaction_type='staking_deposit',
                    blockchain_hash=tx_hash,
                    from_address=user_wallet.address,
                    to_address=contract_obj.address,
                    amount=amount,
                    gas_limit=250000,
                    gas_price=int(self.blockchain_service.w3.to_wei(str(self.blockchain_service.network.gas_price_gwei), 'gwei')),
                    transaction_fee=Decimal('0.0015'),
                    description=f"Stake {amount} tokens in {pool.name}"
                )
                
                # Create user stake record
                user_stake = UserStake.objects.create(
                    user=user,
                    pool=pool,
                    amount_staked=amount,
                    stake_transaction=blockchain_tx
                )
                
                # Update pool stats
                pool.total_staked += amount
                pool.active_stakers += 1
                pool.save()
                
                logger.info(f"User {user.username} staked {amount} tokens in {pool.name}")
                return True, tx_hash, user_stake
            
            return False, "Transaction failed", None
            
        except Exception as e:
            logger.error(f"Error staking tokens: {str(e)}")
            return False, str(e), None


class NFTService:
    """Service for NFT operations"""

    def __init__(self, blockchain_service):
        self.blockchain_service = blockchain_service

    def mint_achievement_nft(self, user, achievement_data):
        """Mint achievement NFT for user"""
        try:
            success, tx_hash, nft_asset = self.blockchain_service.mint_achievement_nft(
                user=user,
                achievement_name=achievement_data['name'],
                rarity=achievement_data['rarity'],
                image_url=achievement_data['image_url'],
                description=achievement_data.get('description', '')
            )

            if success:
                logger.info(f"Successfully minted NFT {achievement_data['name']} for {user.username}")
                return True, nft_asset, tx_hash

            return False, None, None

        except Exception as e:
            logger.error(f"Error minting NFT: {str(e)}")
            return False, None, None


class RewardService:
    """Service for automated blockchain rewards"""

    def __init__(self, blockchain_service):
        self.blockchain_service = blockchain_service
        self.nft_service = NFTService(blockchain_service)

    def process_achievement_reward(self, user, achievement_type, achievement_data):
        """Process achievement reward with both tokens and NFT"""
        try:
            results = {
                'token_reward': None,
                'nft_reward': None,
                'success': False
            }

            # Calculate token reward based on achievement rarity
            token_amounts = {
                1: 10,    # Common: 10 TRD
                2: 25,    # Uncommon: 25 TRD
                3: 50,    # Rare: 50 TRD
                4: 100,   # Epic: 100 TRD
                5: 250,   # Legendary: 250 TRD
            }

            rarity = achievement_data.get('rarity', 1)
            token_amount = token_amounts.get(rarity, 10)

            # Mint token reward
            token_success, token_tx = self.blockchain_service.mint_reward_tokens(
                user=user,
                amount=token_amount,
                description=f"Achievement reward: {achievement_data['name']}"
            )

            if token_success:
                results['token_reward'] = {
                    'amount': token_amount,
                    'tx_hash': token_tx
                }

            # Mint NFT achievement
            nft_success, nft_asset, nft_tx = self.nft_service.mint_achievement_nft(
                user=user,
                achievement_data=achievement_data
            )

            if nft_success:
                results['nft_reward'] = {
                    'nft_asset': nft_asset,
                    'tx_hash': nft_tx
                }

            results['success'] = token_success or nft_success

            if results['success']:
                logger.info(f"Processed achievement reward for {user.username}: {achievement_data['name']}")

            return results

        except Exception as e:
            logger.error(f"Error processing achievement reward: {str(e)}")
            return {'success': False, 'error': str(e)}

    def process_engagement_reward(self, user, engagement_type, amount=None):
        """Process engagement-based token rewards"""
        try:
            # Define engagement rewards
            engagement_rewards = {
                'daily_login': 1,
                'post_created': 5,
                'post_liked': 0.5,
                'comment_made': 1,
                'comment_received': 0.5,
                'share_content': 2,
                'profile_completed': 10,
                'friend_invited': 25,
            }

            reward_amount = amount or engagement_rewards.get(engagement_type, 1)

            success, tx_hash = self.blockchain_service.mint_reward_tokens(
                user=user,
                amount=reward_amount,
                description=f"Engagement reward: {engagement_type}"
            )

            if success:
                logger.info(f"Processed engagement reward for {user.username}: {engagement_type} - {reward_amount} TRD")
                return True, reward_amount, tx_hash

            return False, 0, None

        except Exception as e:
            logger.error(f"Error processing engagement reward: {str(e)}")
            return False, 0, None
