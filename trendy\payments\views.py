from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.http import HttpResponse
from django.utils import timezone
import json

from .models import PaymentTransaction, UserPayPalProfile, PaymentSettings
from .paypal_service import PayPalService
from monetization.services import MonetizationService


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_payment_order(request):
    """Create PayPal payment order for user to pay admin"""
    try:
        user = request.user
        amount = request.data.get('amount')
        purpose = request.data.get('purpose')
        description = request.data.get('description', '')
        
        # Validate input
        if not amount or not purpose:
            return Response({
                'success': False,
                'error': 'Amount and purpose are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate amount
        try:
            amount = float(amount)
            if amount <= 0:
                raise ValueError("Amount must be positive")
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'Invalid amount'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Check payment settings
        settings = PaymentSettings.get_settings()
        if not settings.payments_enabled:
            return Response({
                'success': False,
                'error': 'Payments are currently disabled'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        
        if amount < float(settings.minimum_payment) or amount > float(settings.maximum_payment):
            return Response({
                'success': False,
                'error': f'Amount must be between ${settings.minimum_payment} and ${settings.maximum_payment}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create PayPal order (with fallback for development)
        try:
            paypal_service = PayPalService()
            result = paypal_service.create_payment_order(user, amount, purpose, description)
        except Exception as e:
            # Fallback for development when PayPal is not configured
            if 'No active PayPal account configured' in str(e):
                result = {'success': False, 'error': str(e), 'demo_mode': True}
            else:
                raise

        # Handle demo mode or failed PayPal result
        if not result.get('success') and result.get('demo_mode'):
            # Create mock payment for development
            from .models import PaymentTransaction
            import uuid

            reference_id = f"TRENDY_{purpose.upper()}_{uuid.uuid4().hex[:8]}"

            payment_transaction = PaymentTransaction.objects.create(
                user=user,
                transaction_type='incoming',
                payment_purpose=purpose,
                amount=amount,
                description=description,
                reference_id=reference_id,
                payee_email='<EMAIL>',
                status='pending'
            )

            result = {
                'success': True,
                'order_id': f'DEMO_ORDER_{payment_transaction.id}',
                'approval_url': f'https://demo.paypal.com/approve/{payment_transaction.id}',
                'transaction_id': payment_transaction.id,
                'demo_mode': True
            }
        
        if result['success']:
            return Response({
                'success': True,
                'order_id': result['order_id'],
                'approval_url': result['approval_url'],
                'transaction_id': result['transaction_id']
            })
        else:
            return Response({
                'success': False,
                'error': result['error']
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error creating payment order: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def capture_payment_order(request):
    """Capture approved PayPal payment order"""
    try:
        order_id = request.data.get('order_id')
        
        if not order_id:
            return Response({
                'success': False,
                'error': 'Order ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Capture payment (with fallback for development)
        try:
            paypal_service = PayPalService()
            result = paypal_service.capture_payment_order(order_id)
        except Exception as e:
            # Fallback for development when PayPal is not configured
            if 'No active PayPal account configured' in str(e):
                result = {'success': False, 'error': str(e), 'demo_mode': True}
            else:
                raise

        # Handle demo mode or failed PayPal result
        if not result.get('success') and result.get('demo_mode'):
            # Mock capture for development
            from .models import PaymentTransaction

            # Find transaction by demo order ID
            if order_id.startswith('DEMO_ORDER_'):
                transaction_id = order_id.replace('DEMO_ORDER_', '')
                try:
                    payment_transaction = PaymentTransaction.objects.get(id=transaction_id)
                    payment_transaction.status = 'completed'
                    payment_transaction.completed_at = timezone.now()
                    payment_transaction.payer_email = request.user.email
                    payment_transaction.save()

                    result = {
                        'success': True,
                        'capture_id': f'DEMO_CAPTURE_{transaction_id}',
                        'transaction_id': payment_transaction.id,
                        'demo_mode': True
                    }
                except PaymentTransaction.DoesNotExist:
                    result = {
                        'success': False,
                        'error': 'Demo transaction not found'
                    }
            else:
                result = {
                    'success': False,
                    'error': 'Invalid demo order ID'
                }
        
        if result['success']:
            # Process the successful payment (activate premium, unlock tiers, etc.)
            transaction = PaymentTransaction.objects.get(id=result['transaction_id'])
            
            # Handle different payment purposes
            if transaction.payment_purpose == 'premium_subscription':
                MonetizationService.activate_premium_from_payment(transaction)
            elif transaction.payment_purpose == 'tier_unlock':
                MonetizationService.activate_tier_unlock_from_payment(transaction)
            elif transaction.payment_purpose == 'point_boost':
                MonetizationService.activate_point_boost_from_payment(transaction)
            
            return Response({
                'success': True,
                'capture_id': result['capture_id'],
                'transaction_id': result['transaction_id']
            })
        else:
            return Response({
                'success': False,
                'error': result['error']
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error capturing payment: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_paypal_profile(request):
    """Get user's existing PayPal profile"""
    try:
        user = request.user

        try:
            profile = UserPayPalProfile.objects.get(user=user)

            return Response({
                'success': True,
                'profile': {
                    'id': profile.id,
                    'paypal_email': profile.paypal_email,
                    'is_verified': profile.paypal_account_verified,
                    'verified_at': profile.verified_at.isoformat() if profile.verified_at else None,
                    'auto_accept_payments': profile.auto_accept_payments,
                    'created_at': profile.created_at.isoformat(),
                    'updated_at': profile.updated_at.isoformat(),
                }
            })

        except UserPayPalProfile.DoesNotExist:
            return Response({
                'success': True,
                'profile': None,
                'message': 'No PayPal profile found'
            })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def setup_paypal_profile(request):
    """Set up user's PayPal profile for receiving payments"""
    try:
        user = request.user
        paypal_email = request.data.get('paypal_email')
        
        if not paypal_email:
            return Response({
                'success': False,
                'error': 'PayPal email is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate email format
        from django.core.validators import validate_email
        from django.core.exceptions import ValidationError
        
        try:
            validate_email(paypal_email)
        except ValidationError:
            return Response({
                'success': False,
                'error': 'Invalid email format'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create or update PayPal profile
        profile, created = UserPayPalProfile.objects.get_or_create(
            user=user,
            defaults={'paypal_email': paypal_email}
        )
        
        if not created:
            profile.paypal_email = paypal_email
            profile.paypal_account_verified = False  # Re-verify if email changed
            profile.save()
        
        # Generate and send verification code
        from accounts.email_service import EmailService
        verification_code = EmailService.generate_verification_code(6)
        profile.verification_code = verification_code
        profile.verification_sent_at = timezone.now()
        profile.save()

        # Send verification email
        email_sent = EmailService.send_paypal_verification_email(
            user, paypal_email, verification_code
        )

        return Response({
            'success': True,
            'message': 'PayPal profile created. Verification email sent.' if email_sent else 'PayPal profile created. Please try resending verification email.',
            'verification_required': True,
            'email_sent': email_sent,
            'verification_code_length': 6
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error setting up PayPal profile: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_paypal_profile(request):
    """Verify user's PayPal profile with verification code"""
    try:
        user = request.user
        verification_code = request.data.get('verification_code')
        
        if not verification_code:
            return Response({
                'success': False,
                'error': 'Verification code is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            profile = UserPayPalProfile.objects.get(user=user)
        except UserPayPalProfile.DoesNotExist:
            return Response({
                'success': False,
                'error': 'PayPal profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        if profile.verification_code == verification_code:
            profile.paypal_account_verified = True
            profile.verified_at = timezone.now()
            profile.save()
            
            return Response({
                'success': True,
                'message': 'PayPal account verified successfully!'
            })
        else:
            return Response({
                'success': False,
                'error': 'Invalid verification code'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error verifying PayPal profile: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def resend_paypal_verification(request):
    """Resend PayPal verification email"""
    try:
        user = request.user

        try:
            profile = UserPayPalProfile.objects.get(user=user)
        except UserPayPalProfile.DoesNotExist:
            return Response({
                'success': False,
                'error': 'PayPal profile not found. Please set up your PayPal email first.'
            }, status=status.HTTP_404_NOT_FOUND)

        if profile.paypal_account_verified:
            return Response({
                'success': False,
                'error': 'PayPal account is already verified'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check rate limiting (max 3 emails per hour)
        if profile.verification_sent_at:
            time_since_last = timezone.now() - profile.verification_sent_at
            if time_since_last.total_seconds() < 1200:  # 20 minutes
                remaining_time = 20 - int(time_since_last.total_seconds() / 60)
                return Response({
                    'success': False,
                    'error': f'Please wait {remaining_time} minutes before requesting another verification email'
                }, status=status.HTTP_429_TOO_MANY_REQUESTS)

        # Generate new verification code
        from accounts.email_service import EmailService
        verification_code = EmailService.generate_verification_code(6)
        profile.verification_code = verification_code
        profile.verification_sent_at = timezone.now()
        profile.save()

        # Send verification email
        email_sent = EmailService.send_paypal_verification_email(
            user, profile.paypal_email, verification_code
        )

        return Response({
            'success': True,
            'message': 'Verification email sent successfully' if email_sent else 'Failed to send email. Please try again.',
            'email_sent': email_sent
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error resending verification email: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def payment_history(request):
    """Get user's payment history"""
    try:
        user = request.user
        
        # Get all transactions for user
        transactions = PaymentTransaction.objects.filter(user=user).order_by('-created_at')
        
        # Format transaction data
        transaction_data = []
        for transaction in transactions:
            transaction_data.append({
                'id': transaction.id,
                'type': transaction.transaction_type,
                'purpose': transaction.get_payment_purpose_display(),
                'amount': str(transaction.amount),
                'status': transaction.get_status_display(),
                'description': transaction.description,
                'created_at': transaction.created_at.isoformat(),
                'completed_at': transaction.completed_at.isoformat() if transaction.completed_at else None,
                'paypal_payment_id': transaction.paypal_payment_id,
                'reference_id': transaction.reference_id,
            })
        
        # Get PayPal profile status
        paypal_profile = None
        try:
            profile = UserPayPalProfile.objects.get(user=user)
            paypal_profile = {
                'email': profile.paypal_email,
                'verified': profile.is_verified,
                'total_received': str(profile.total_payments_received),
                'total_paid': str(profile.total_payments_made),
                'successful_transactions': profile.successful_transactions,
            }
        except UserPayPalProfile.DoesNotExist:
            pass
        
        return Response({
            'success': True,
            'data': {
                'transactions': transaction_data,
                'paypal_profile': paypal_profile,
                'total_transactions': len(transaction_data),
            }
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error fetching payment history: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def request_payout(request):
    """Request payout for earned rewards"""
    try:
        user = request.user
        amount = request.data.get('amount')
        
        if not amount:
            return Response({
                'success': False,
                'error': 'Amount is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate amount
        try:
            amount = float(amount)
            if amount <= 0:
                raise ValueError("Amount must be positive")
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'Invalid amount'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Check if user has verified PayPal profile
        try:
            profile = UserPayPalProfile.objects.get(user=user)
            if not profile.is_verified:
                return Response({
                    'success': False,
                    'error': 'PayPal account not verified'
                }, status=status.HTTP_400_BAD_REQUEST)
        except UserPayPalProfile.DoesNotExist:
            return Response({
                'success': False,
                'error': 'PayPal profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Check payout settings
        settings = PaymentSettings.get_settings()
        if not settings.payouts_enabled:
            return Response({
                'success': False,
                'error': 'Payouts are currently disabled'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        
        if amount < float(settings.minimum_payout):
            return Response({
                'success': False,
                'error': f'Minimum payout amount is ${settings.minimum_payout}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # TODO: Check if user has enough earned rewards to request this payout
        # This would integrate with the gamification system
        
        # Create payout request
        paypal_service = PayPalService()
        result = paypal_service.create_single_payout(
            user=user,
            amount=amount,
            purpose='reward_payout',
            description=f'Trendy App reward payout - ${amount}'
        )
        
        if result['success']:
            return Response({
                'success': True,
                'message': f'Payout request for ${amount} submitted successfully',
                'transaction_id': result['transaction_id'],
                'batch_id': result['batch_id']
            })
        else:
            return Response({
                'success': False,
                'error': result['error']
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error requesting payout: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
def paypal_webhook(request):
    """Handle PayPal webhook events"""
    try:
        # Get webhook data
        webhook_data = json.loads(request.body)
        
        # Process webhook
        paypal_service = PayPalService()
        result = paypal_service.process_webhook(webhook_data)
        
        if result['success']:
            return HttpResponse(status=200)
        else:
            return HttpResponse(status=400)
            
    except Exception as e:
        return HttpResponse(status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def payment_settings(request):
    """Get payment system settings for user"""
    try:
        settings = PaymentSettings.get_settings()
        
        return Response({
            'success': True,
            'data': {
                'payments_enabled': settings.payments_enabled,
                'payouts_enabled': settings.payouts_enabled,
                'minimum_payment': str(settings.minimum_payment),
                'maximum_payment': str(settings.maximum_payment),
                'minimum_payout': str(settings.minimum_payout),
                'payout_processing_fee': str(settings.payout_processing_fee),
                'require_email_verification': settings.require_email_verification,
            }
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error fetching payment settings: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
