"""
Django management command to setup the maintenance system
Usage: python manage.py setup_maintenance_system
"""
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.conf import settings
from core.models import FeatureToggle, PREDEFINED_FEATURES
from core.admin import setup_predefined_features


class Command(BaseCommand):
    help = 'Setup the maintenance and feature toggle system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force setup even if already configured'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔧 Setting up Maintenance System'))
        self.stdout.write('=' * 50)

        try:
            # 1. Create migrations
            self.stdout.write('\n1️⃣  Creating migrations...')
            call_command('makemigrations', 'core', verbosity=0)
            self.stdout.write('  ✅ Migrations created')

            # 2. Run migrations
            self.stdout.write('\n2️⃣  Running migrations...')
            call_command('migrate', verbosity=0)
            self.stdout.write('  ✅ Migrations applied')

            # 3. Setup predefined features
            self.stdout.write('\n3️⃣  Setting up predefined features...')
            created_count = setup_predefined_features()
            self.stdout.write(f'  ✅ Created {created_count} new feature toggles')

            # 4. Show current status
            self.stdout.write('\n4️⃣  Current system status:')
            self._show_system_status()

            # 5. Show usage instructions
            self.stdout.write('\n5️⃣  Usage Instructions:')
            self._show_usage_instructions()

            self.stdout.write(f'\n✅ Maintenance system setup completed!')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Setup failed: {str(e)}')
            )
            raise

    def _show_system_status(self):
        """Show current system status"""
        from core.models import SystemMaintenance, FeatureToggle
        
        # Maintenance status
        active_maintenance = SystemMaintenance.objects.filter(is_active=True).count()
        total_maintenance = SystemMaintenance.objects.count()
        self.stdout.write(f'  📊 Maintenance Records: {total_maintenance} total, {active_maintenance} active')
        
        # Feature toggles
        enabled_features = FeatureToggle.objects.filter(is_enabled=True).count()
        total_features = FeatureToggle.objects.count()
        self.stdout.write(f'  🎛️  Feature Toggles: {total_features} total, {enabled_features} enabled')

    def _show_usage_instructions(self):
        """Show usage instructions"""
        self.stdout.write('  📚 Available Commands:')
        self.stdout.write('    • python manage.py backup_database --compress')
        self.stdout.write('    • python manage.py restore_database <backup_name>')
        self.stdout.write('    • python manage.py clear_database --create-backup')
        self.stdout.write('')
        self.stdout.write('  🌐 Admin Interface:')
        self.stdout.write('    • Visit /admin/core/ to manage maintenance and features')
        self.stdout.write('    • System Maintenance: Schedule and control maintenance windows')
        self.stdout.write('    • Feature Toggles: Enable/disable system features')
        self.stdout.write('    • Maintenance Logs: View system activity logs')
        self.stdout.write('')
        self.stdout.write('  ⚙️  Settings Integration:')
        self.stdout.write('    • Add "core.middleware.MaintenanceMiddleware" to MIDDLEWARE')
        self.stdout.write('    • Add "core.middleware.FeatureToggleMiddleware" to MIDDLEWARE')
        self.stdout.write('    • Add "core" to INSTALLED_APPS')
