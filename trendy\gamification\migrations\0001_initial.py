# Generated by Django 5.2.3 on 2025-06-23 13:28

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PayPalSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rewards_enabled', models.BooleanField(default=True)),
                ('minimum_payout', models.DecimalField(decimal_places=2, default=5.0, max_digits=10)),
                ('maximum_monthly_payout_per_user', models.DecimalField(decimal_places=2, default=100.0, max_digits=10)),
                ('require_email_verification', models.BooleanField(default=True)),
                ('require_phone_verification', models.BooleanField(default=True)),
                ('minimum_account_age_days', models.PositiveIntegerField(default=30)),
                ('minimum_activity_score', models.PositiveIntegerField(default=100)),
                ('max_claims_per_day', models.PositiveIntegerField(default=10)),
                ('max_claims_per_month', models.PositiveIntegerField(default=50)),
                ('cooldown_period_days', models.PositiveIntegerField(default=7)),
                ('paypal_client_id', models.CharField(blank=True, max_length=200)),
                ('paypal_client_secret', models.CharField(blank=True, max_length=200)),
                ('paypal_mode', models.CharField(choices=[('sandbox', 'Sandbox'), ('live', 'Live')], default='sandbox', max_length=10)),
                ('admin_email_notifications', models.BooleanField(default=True)),
                ('user_email_notifications', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'PayPal Settings',
                'verbose_name_plural': 'PayPal Settings',
            },
        ),
        migrations.CreateModel(
            name='Badge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField()),
                ('badge_type', models.CharField(choices=[('reading', 'Reading Achievement'), ('writing', 'Writing Achievement'), ('engagement', 'Engagement Achievement'), ('community', 'Community Achievement'), ('special', 'Special Achievement'), ('milestone', 'Milestone Achievement')], max_length=20)),
                ('rarity', models.CharField(choices=[('common', 'Common'), ('uncommon', 'Uncommon'), ('rare', 'Rare'), ('epic', 'Epic'), ('legendary', 'Legendary')], default='common', max_length=20)),
                ('icon', models.CharField(default='star', max_length=50)),
                ('color', models.CharField(default='#FFD700', max_length=7)),
                ('image_url', models.URLField(blank=True)),
                ('requirements', models.JSONField(default=dict)),
                ('points_reward', models.PositiveIntegerField(default=10)),
                ('is_active', models.BooleanField(default=True)),
                ('is_secret', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['rarity', 'name'],
                'indexes': [models.Index(fields=['badge_type', 'is_active'], name='gamificatio_badge_t_6bd4f9_idx'), models.Index(fields=['rarity'], name='gamificatio_rarity_b6add4_idx')],
            },
        ),
        migrations.CreateModel(
            name='Challenge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('challenge_type', models.CharField(choices=[('reading', 'Reading Challenge'), ('writing', 'Writing Challenge'), ('engagement', 'Engagement Challenge'), ('community', 'Community Challenge')], max_length=20)),
                ('difficulty', models.CharField(choices=[('easy', 'Easy'), ('medium', 'Medium'), ('hard', 'Hard'), ('expert', 'Expert')], max_length=20)),
                ('requirements', models.JSONField(default=dict)),
                ('points_reward', models.PositiveIntegerField(default=50)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('duration_days', models.PositiveIntegerField(default=7)),
                ('max_participants', models.PositiveIntegerField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('badge_reward', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='gamification.badge')),
            ],
            options={
                'ordering': ['-is_featured', '-start_date'],
            },
        ),
        migrations.CreateModel(
            name='ChallengeParticipation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('progress', models.JSONField(default=dict)),
                ('completion_percentage', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('is_completed', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('challenge', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participants', to='gamification.challenge')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='challenge_participations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-joined_at'],
            },
        ),
        migrations.CreateModel(
            name='PayPalReward',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('reward_type', models.CharField(choices=[('milestone', 'Point Milestone'), ('level', 'Level Achievement'), ('challenge', 'Challenge Completion'), ('streak', 'Streak Achievement'), ('special', 'Special Event')], max_length=20)),
                ('points_required', models.PositiveIntegerField(blank=True, null=True)),
                ('level_required', models.PositiveIntegerField(blank=True, null=True)),
                ('streak_required', models.PositiveIntegerField(blank=True, null=True)),
                ('requirements', models.JSONField(default=dict)),
                ('usd_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('max_claims_per_user', models.PositiveIntegerField(default=1)),
                ('max_total_claims', models.PositiveIntegerField(blank=True, null=True)),
                ('current_claims', models.PositiveIntegerField(default=0)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('limited', 'Limited Time')], default='active', max_length=20)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('minimum_account_age_days', models.PositiveIntegerField(default=30)),
                ('requires_verification', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-usd_amount', '-points_required'],
                'indexes': [models.Index(fields=['status', 'reward_type'], name='gamificatio_status_d0a236_idx'), models.Index(fields=['points_required'], name='gamificatio_points__55143b_idx'), models.Index(fields=['level_required'], name='gamificatio_level_r_61bce5_idx')],
            },
        ),
        migrations.CreateModel(
            name='PointTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('reading', 'Reading Activity'), ('writing', 'Writing Activity'), ('engagement', 'Engagement Activity'), ('badge', 'Badge Earned'), ('challenge', 'Challenge Completed'), ('bonus', 'Bonus Points'), ('penalty', 'Point Penalty')], max_length=20)),
                ('points', models.IntegerField()),
                ('description', models.CharField(max_length=200)),
                ('related_object_type', models.CharField(blank=True, max_length=50)),
                ('related_object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='point_transactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserBadge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('earned_at', models.DateTimeField(auto_now_add=True)),
                ('progress_data', models.JSONField(default=dict)),
                ('badge', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='earned_by', to='gamification.badge')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='earned_badges', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-earned_at'],
            },
        ),
        migrations.CreateModel(
            name='UserLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_points', models.PositiveIntegerField(default=0)),
                ('current_level', models.PositiveIntegerField(default=1)),
                ('points_to_next_level', models.PositiveIntegerField(default=100)),
                ('reading_streak', models.PositiveIntegerField(default=0)),
                ('writing_streak', models.PositiveIntegerField(default=0)),
                ('engagement_streak', models.PositiveIntegerField(default=0)),
                ('last_reading_date', models.DateField(blank=True, null=True)),
                ('last_writing_date', models.DateField(blank=True, null=True)),
                ('last_engagement_date', models.DateField(blank=True, null=True)),
                ('total_posts_read', models.PositiveIntegerField(default=0)),
                ('total_posts_written', models.PositiveIntegerField(default=0)),
                ('total_comments_made', models.PositiveIntegerField(default=0)),
                ('total_likes_given', models.PositiveIntegerField(default=0)),
                ('total_voice_comments', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='level', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-current_level', '-total_points'],
            },
        ),
        migrations.CreateModel(
            name='UserPayPalReward',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('paypal_email', models.EmailField(max_length=254)),
                ('usd_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending Verification'), ('approved', 'Approved for Payment'), ('processing', 'Processing Payment'), ('paid', 'Payment Sent'), ('failed', 'Payment Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('claim_date', models.DateTimeField(auto_now_add=True)),
                ('approved_date', models.DateTimeField(blank=True, null=True)),
                ('paid_date', models.DateTimeField(blank=True, null=True)),
                ('paypal_transaction_id', models.CharField(blank=True, max_length=100)),
                ('paypal_batch_id', models.CharField(blank=True, max_length=100)),
                ('user_points_at_claim', models.PositiveIntegerField()),
                ('user_level_at_claim', models.PositiveIntegerField()),
                ('verification_notes', models.TextField(blank=True)),
                ('admin_notes', models.TextField(blank=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_paypal_rewards', to=settings.AUTH_USER_MODEL)),
                ('reward', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_claims', to='gamification.paypalreward')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='paypal_rewards', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-claim_date'],
            },
        ),
        migrations.AddIndex(
            model_name='challenge',
            index=models.Index(fields=['challenge_type', 'is_active'], name='gamificatio_challen_bbb520_idx'),
        ),
        migrations.AddIndex(
            model_name='challenge',
            index=models.Index(fields=['start_date', 'end_date'], name='gamificatio_start_d_ff8bb0_idx'),
        ),
        migrations.AddIndex(
            model_name='challenge',
            index=models.Index(fields=['is_featured'], name='gamificatio_is_feat_d54703_idx'),
        ),
        migrations.AddIndex(
            model_name='challengeparticipation',
            index=models.Index(fields=['user', 'is_active'], name='gamificatio_user_id_54a34e_idx'),
        ),
        migrations.AddIndex(
            model_name='challengeparticipation',
            index=models.Index(fields=['challenge', 'is_completed'], name='gamificatio_challen_455baa_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='challengeparticipation',
            unique_together={('user', 'challenge')},
        ),
        migrations.AddIndex(
            model_name='pointtransaction',
            index=models.Index(fields=['user', '-created_at'], name='gamificatio_user_id_b86e52_idx'),
        ),
        migrations.AddIndex(
            model_name='pointtransaction',
            index=models.Index(fields=['transaction_type'], name='gamificatio_transac_203c28_idx'),
        ),
        migrations.AddIndex(
            model_name='userbadge',
            index=models.Index(fields=['user', '-earned_at'], name='gamificatio_user_id_44649b_idx'),
        ),
        migrations.AddIndex(
            model_name='userbadge',
            index=models.Index(fields=['badge'], name='gamificatio_badge_i_20a089_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userbadge',
            unique_together={('user', 'badge')},
        ),
        migrations.AddIndex(
            model_name='userlevel',
            index=models.Index(fields=['current_level', '-total_points'], name='gamificatio_current_58db0f_idx'),
        ),
        migrations.AddIndex(
            model_name='userlevel',
            index=models.Index(fields=['-total_points'], name='gamificatio_total_p_0d6552_idx'),
        ),
        migrations.AddIndex(
            model_name='userpaypalreward',
            index=models.Index(fields=['user', 'status'], name='gamificatio_user_id_f5feb2_idx'),
        ),
        migrations.AddIndex(
            model_name='userpaypalreward',
            index=models.Index(fields=['status', 'claim_date'], name='gamificatio_status_6b7c48_idx'),
        ),
        migrations.AddIndex(
            model_name='userpaypalreward',
            index=models.Index(fields=['reward', 'status'], name='gamificatio_reward__433635_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userpaypalreward',
            unique_together={('user', 'reward')},
        ),
    ]
