from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from gamification.models import Badge, Challenge

class Command(BaseCommand):
    help = 'Create sample gamification content for testing'

    def handle(self, *args, **options):
        # Create sample badges
        badges_data = [
            {
                'name': 'First Steps',
                'description': 'Welcome to Trendy! You\'ve taken your first steps into our community.',
                'badge_type': 'milestone',
                'rarity': 'common',
                'icon': 'star',
                'color': '#FFD700',
                'requirements': {'min_level': 1},
                'points_reward': 10,
            },
            {
                'name': 'Bookworm',
                'description': 'Read 10 posts to earn this badge.',
                'badge_type': 'reading',
                'rarity': 'common',
                'icon': 'book',
                'color': '#4CAF50',
                'requirements': {'min_posts_read': 10},
                'points_reward': 25,
            },
            {
                'name': 'Prolific Reader',
                'description': 'Read 50 posts! You\'re a true reading enthusiast.',
                'badge_type': 'reading',
                'rarity': 'uncommon',
                'icon': 'library_books',
                'color': '#2196F3',
                'requirements': {'min_posts_read': 50},
                'points_reward': 50,
            },
            {
                'name': 'Reading Master',
                'description': 'Read 100 posts! You\'ve mastered the art of reading.',
                'badge_type': 'reading',
                'rarity': 'rare',
                'icon': 'school',
                'color': '#9C27B0',
                'requirements': {'min_posts_read': 100},
                'points_reward': 100,
            },
            {
                'name': 'First Post',
                'description': 'Published your first post! Welcome to the author community.',
                'badge_type': 'writing',
                'rarity': 'common',
                'icon': 'edit',
                'color': '#FF9800',
                'requirements': {'min_posts_written': 1},
                'points_reward': 50,
            },
            {
                'name': 'Prolific Writer',
                'description': 'Published 10 posts! You\'re becoming a regular author.',
                'badge_type': 'writing',
                'rarity': 'uncommon',
                'icon': 'create',
                'color': '#FF5722',
                'requirements': {'min_posts_written': 10},
                'points_reward': 100,
            },
            {
                'name': 'Conversationalist',
                'description': 'Made 25 comments! You love engaging with the community.',
                'badge_type': 'engagement',
                'rarity': 'common',
                'icon': 'chat',
                'color': '#00BCD4',
                'requirements': {'min_comments': 25},
                'points_reward': 30,
            },
            {
                'name': 'Community Champion',
                'description': 'Made 100 comments! You\'re a pillar of our community.',
                'badge_type': 'engagement',
                'rarity': 'uncommon',
                'icon': 'forum',
                'color': '#3F51B5',
                'requirements': {'min_comments': 100},
                'points_reward': 75,
            },
            {
                'name': 'Streak Master',
                'description': 'Maintained a 7-day reading streak! Consistency is key.',
                'badge_type': 'reading',
                'rarity': 'uncommon',
                'icon': 'local_fire_department',
                'color': '#F44336',
                'requirements': {'min_reading_streak': 7},
                'points_reward': 60,
            },
            {
                'name': 'Level Up!',
                'description': 'Reached level 5! You\'re making great progress.',
                'badge_type': 'milestone',
                'rarity': 'uncommon',
                'icon': 'trending_up',
                'color': '#8BC34A',
                'requirements': {'min_level': 5},
                'points_reward': 75,
            },
            {
                'name': 'Elite Member',
                'description': 'Reached level 10! You\'re among the elite members.',
                'badge_type': 'milestone',
                'rarity': 'rare',
                'icon': 'military_tech',
                'color': '#E91E63',
                'requirements': {'min_level': 10},
                'points_reward': 150,
            },
            {
                'name': 'Legend',
                'description': 'Reached level 20! You\'re a true legend of Trendy.',
                'badge_type': 'milestone',
                'rarity': 'legendary',
                'icon': 'emoji_events',
                'color': '#FFD700',
                'requirements': {'min_level': 20},
                'points_reward': 500,
                'is_secret': True,
            },
        ]

        for badge_data in badges_data:
            badge, created = Badge.objects.get_or_create(
                name=badge_data['name'],
                defaults=badge_data
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created badge: {badge.name}')
                )

        # Create sample challenges
        now = timezone.now()
        challenges_data = [
            {
                'title': '7-Day Reading Challenge',
                'description': 'Read at least one post every day for 7 consecutive days.',
                'challenge_type': 'reading',
                'difficulty': 'easy',
                'requirements': {'daily_reading': 7, 'consecutive_days': 7},
                'points_reward': 100,
                'start_date': now,
                'end_date': now + timedelta(days=7),
                'duration_days': 7,
                'is_featured': True,
            },
            {
                'title': 'Content Creator Sprint',
                'description': 'Publish 3 posts within 2 weeks.',
                'challenge_type': 'writing',
                'difficulty': 'medium',
                'requirements': {'posts_to_write': 3},
                'points_reward': 200,
                'start_date': now,
                'end_date': now + timedelta(days=14),
                'duration_days': 14,
                'is_featured': True,
            },
            {
                'title': 'Community Engagement',
                'description': 'Make 20 meaningful comments on different posts.',
                'challenge_type': 'engagement',
                'difficulty': 'easy',
                'requirements': {'comments_to_make': 20},
                'points_reward': 75,
                'start_date': now,
                'end_date': now + timedelta(days=10),
                'duration_days': 10,
            },
            {
                'title': 'Reading Marathon',
                'description': 'Read 25 posts in one month.',
                'challenge_type': 'reading',
                'difficulty': 'hard',
                'requirements': {'posts_to_read': 25},
                'points_reward': 300,
                'start_date': now,
                'end_date': now + timedelta(days=30),
                'duration_days': 30,
            },
            {
                'title': 'Voice Pioneer',
                'description': 'Leave 5 voice comments to explore this new feature.',
                'challenge_type': 'engagement',
                'difficulty': 'medium',
                'requirements': {'voice_comments': 5},
                'points_reward': 150,
                'start_date': now,
                'end_date': now + timedelta(days=14),
                'duration_days': 14,
            },
            {
                'title': 'Interactive Explorer',
                'description': 'Participate in 10 polls or quizzes.',
                'challenge_type': 'engagement',
                'difficulty': 'medium',
                'requirements': {'interactive_participation': 10},
                'points_reward': 125,
                'start_date': now,
                'end_date': now + timedelta(days=21),
                'duration_days': 21,
            },
        ]

        for challenge_data in challenges_data:
            challenge, created = Challenge.objects.get_or_create(
                title=challenge_data['title'],
                defaults=challenge_data
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created challenge: {challenge.title}')
                )

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample gamification content!')
        )
