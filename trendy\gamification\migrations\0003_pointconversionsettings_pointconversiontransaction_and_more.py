# Generated by Django 5.1.7 on 2025-06-25 09:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gamification', '0002_engagementsettings_engagementhistory_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PointConversionSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('base_conversion_rate', models.PositiveIntegerField(default=10)),
                ('level_bonus_enabled', models.BooleanField(default=True)),
                ('level_bonus_threshold', models.PositiveIntegerField(default=5)),
                ('level_bonus_reduction', models.FloatField(default=0.1)),
                ('max_level_bonus', models.FloatField(default=0.5)),
                ('premium_bonus_enabled', models.BooleanField(default=True)),
                ('premium_conversion_bonus', models.FloatField(default=0.2)),
                ('daily_conversion_limit', models.PositiveIntegerField(default=500)),
                ('minimum_conversion_amount', models.PositiveIntegerField(default=50)),
                ('maximum_conversion_amount', models.PositiveIntegerField(default=5000)),
                ('conversion_fee_percentage', models.FloatField(default=0.15)),
                ('conversion_fee_fixed', models.PositiveIntegerField(default=5)),
                ('conversion_enabled', models.BooleanField(default=True)),
                ('maintenance_mode', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Point Conversion Settings',
                'verbose_name_plural': 'Point Conversion Settings',
            },
        ),
        migrations.CreateModel(
            name='PointConversionTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gamification_points_spent', models.PositiveIntegerField()),
                ('store_points_received', models.PositiveIntegerField()),
                ('conversion_rate', models.PositiveIntegerField()),
                ('percentage_fee', models.PositiveIntegerField(default=0)),
                ('fixed_fee', models.PositiveIntegerField(default=0)),
                ('total_fee', models.PositiveIntegerField(default=0)),
                ('user_level_at_conversion', models.PositiveIntegerField()),
                ('user_total_points_at_conversion', models.PositiveIntegerField()),
                ('was_premium', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversion_transactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', '-created_at'], name='gamificatio_user_id_7234e7_idx'), models.Index(fields=['status'], name='gamificatio_status_9d2876_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserStorePoints',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('balance', models.PositiveIntegerField(default=0)),
                ('total_earned', models.PositiveIntegerField(default=0)),
                ('total_spent', models.PositiveIntegerField(default=0)),
                ('total_converted', models.PositiveIntegerField(default=0)),
                ('daily_conversions_today', models.PositiveIntegerField(default=0)),
                ('last_conversion_reset', models.DateField(auto_now_add=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='store_points', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['user', 'balance'], name='gamificatio_user_id_56c452_idx')],
            },
        ),
    ]
