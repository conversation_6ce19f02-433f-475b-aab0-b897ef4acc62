from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import <PERSON>Comment, VoiceCommentLike, AIWritingSession, TextToSpeechRequest

User = get_user_model()

class VoiceCommentSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.username', read_only=True)
    user_avatar = serializers.SerializerMethodField()
    audio_url = serializers.ReadOnlyField()
    is_liked = serializers.SerializerMethodField()
    
    class Meta:
        model = VoiceComment
        fields = [
            'id', 'post_id', 'user_id', 'user_name', 'user_avatar',
            'audio_url', 'transcription', 'duration_seconds', 'file_size',
            'is_transcribed', 'like_count', 'is_liked', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user_id', 'user_name', 'user_avatar', 'audio_url',
            'like_count', 'is_liked', 'created_at', 'updated_at'
        ]
    
    def get_user_avatar(self, obj):
        # Return user avatar URL if available
        # For now, return None (implement when user profiles are added)
        return None
    
    def get_is_liked(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return VoiceCommentLike.objects.filter(
                voice_comment=obj,
                user=request.user
            ).exists()
        return False

class CreateVoiceCommentSerializer(serializers.ModelSerializer):
    audio_file = serializers.FileField()
    
    class Meta:
        model = VoiceComment
        fields = [
            'post', 'audio_file', 'duration_seconds', 'transcription'
        ]
    
    def validate_audio_file(self, value):
        # Validate file size (max 10MB)
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("Audio file size cannot exceed 10MB")
        
        # Validate file extension
        allowed_extensions = ['mp3', 'm4a', 'wav', 'ogg']
        file_extension = value.name.split('.')[-1].lower()
        if file_extension not in allowed_extensions:
            raise serializers.ValidationError(
                f"Unsupported file format. Allowed formats: {', '.join(allowed_extensions)}"
            )
        
        return value
    
    def create(self, validated_data):
        # Set file size
        validated_data['file_size'] = validated_data['audio_file'].size
        
        # Set user from request
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            validated_data['user'] = request.user
        
        return super().create(validated_data)

class VoiceCommentLikeSerializer(serializers.ModelSerializer):
    class Meta:
        model = VoiceCommentLike
        fields = ['id', 'voice_comment', 'created_at']
        read_only_fields = ['id', 'created_at']

class AIWritingSessionSerializer(serializers.ModelSerializer):
    class Meta:
        model = AIWritingSession
        fields = [
            'id', 'session_id', 'content_before', 'content_after',
            'suggestions_count', 'suggestions_accepted', 'completion_requests',
            'improvement_requests', 'session_duration', 'words_added',
            'readability_improvement', 'started_at', 'ended_at'
        ]
        read_only_fields = ['id', 'started_at']

class CreateAIWritingSessionSerializer(serializers.ModelSerializer):
    class Meta:
        model = AIWritingSession
        fields = [
            'session_id', 'post', 'content_before'
        ]
    
    def create(self, validated_data):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            validated_data['user'] = request.user
        return super().create(validated_data)

class UpdateAIWritingSessionSerializer(serializers.ModelSerializer):
    class Meta:
        model = AIWritingSession
        fields = [
            'content_after', 'suggestions_count', 'suggestions_accepted',
            'completion_requests', 'improvement_requests', 'session_duration',
            'words_added', 'readability_improvement', 'ended_at'
        ]

class TextToSpeechRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = TextToSpeechRequest
        fields = [
            'id', 'post', 'text_length', 'language', 'voice_settings',
            'duration_seconds', 'completed', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
    
    def create(self, validated_data):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            validated_data['user'] = request.user
        return super().create(validated_data)

class VoiceCommentStatsSerializer(serializers.Serializer):
    """Serializer for voice comment statistics"""
    total_comments = serializers.IntegerField()
    total_duration = serializers.IntegerField()  # in seconds
    average_duration = serializers.FloatField()
    transcribed_count = serializers.IntegerField()
    transcription_rate = serializers.FloatField()  # percentage
    top_contributors = serializers.ListField(
        child=serializers.DictField()
    )

class AIWritingStatsSerializer(serializers.Serializer):
    """Serializer for AI writing assistance statistics"""
    total_sessions = serializers.IntegerField()
    total_suggestions = serializers.IntegerField()
    acceptance_rate = serializers.FloatField()  # percentage
    average_session_duration = serializers.FloatField()
    total_words_added = serializers.IntegerField()
    average_readability_improvement = serializers.FloatField()

# Simplified serializers for API responses
class VoiceCommentListSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.username', read_only=True)
    audio_url = serializers.ReadOnlyField()
    
    class Meta:
        model = VoiceComment
        fields = [
            'id', 'user_name', 'audio_url', 'duration_seconds',
            'transcription', 'like_count', 'created_at'
        ]

class VoiceCommentDetailSerializer(VoiceCommentSerializer):
    """Detailed serializer with additional fields"""
    user_email = serializers.CharField(source='user.email', read_only=True)
    post_title = serializers.CharField(source='post.title', read_only=True)
    
    class Meta(VoiceCommentSerializer.Meta):
        fields = VoiceCommentSerializer.Meta.fields + [
            'user_email', 'post_title', 'is_transcribed', 'transcription_confidence'
        ]
