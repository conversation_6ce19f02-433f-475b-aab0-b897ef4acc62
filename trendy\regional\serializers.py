"""
Regional content serializers
"""

from rest_framework import serializers
from .models import Country, Region, UserLocationHistory


class RegionSerializer(serializers.ModelSerializer):
    """Serializer for Region model"""
    country_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Region
        fields = ['id', 'name', 'code', 'description', 'country_count', 'is_active']
    
    def get_country_count(self, obj):
        return obj.countries.filter(is_active=True).count()


class CountrySerializer(serializers.ModelSerializer):
    """Serializer for Country model"""
    region = RegionSerializer(read_only=True)
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = Country
        fields = [
            'id', 'name', 'code', 'code_3', 'region', 'currency_code', 
            'currency_name', 'phone_code', 'flag_emoji', 'display_name',
            'is_active', 'allow_global_content', 'priority'
        ]


class CountrySimpleSerializer(serializers.ModelSerializer):
    """Simple serializer for Country model (for dropdowns)"""
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = Country
        fields = ['id', 'name', 'code', 'flag_emoji', 'display_name']


class UserLocationHistorySerializer(serializers.ModelSerializer):
    """Serializer for UserLocationHistory model"""
    country = CountrySimpleSerializer(source='detected_country', read_only=True)
    
    class Meta:
        model = UserLocationHistory
        fields = [
            'id', 'country', 'ip_address', 'detection_method', 
            'confidence_score', 'created_at'
        ]


class RegionalPreferencesSerializer(serializers.Serializer):
    """Serializer for user regional preferences"""
    preferred_country = CountrySimpleSerializer(read_only=True)
    detected_country = CountrySimpleSerializer(read_only=True)
    effective_country = CountrySimpleSerializer(read_only=True)
    show_global_content = serializers.BooleanField()
    auto_detect_location = serializers.BooleanField()
    
    def to_representation(self, instance):
        """Custom representation to include effective_country"""
        data = super().to_representation(instance)
        if instance.effective_country:
            data['effective_country'] = CountrySimpleSerializer(instance.effective_country).data
        else:
            data['effective_country'] = None
        return data


class SetPreferredCountrySerializer(serializers.Serializer):
    """Serializer for setting preferred country"""
    country_code = serializers.CharField(max_length=2, required=False, allow_null=True, allow_blank=True)

    def validate_country_code(self, value):
        """Validate that country exists and is active"""
        # Allow null/empty values to clear the preferred country
        if not value or value.strip() == '':
            return None

        try:
            Country.objects.get(code=value.upper(), is_active=True)
            return value.upper()
        except Country.DoesNotExist:
            raise serializers.ValidationError("Invalid or inactive country code")


class RegionalStatsSerializer(serializers.Serializer):
    """Serializer for regional content statistics"""
    user_country = serializers.CharField(allow_null=True)
    total_posts = serializers.IntegerField()
    global_posts = serializers.IntegerField()
    regional_posts = serializers.IntegerField()
    available_countries = serializers.IntegerField()
