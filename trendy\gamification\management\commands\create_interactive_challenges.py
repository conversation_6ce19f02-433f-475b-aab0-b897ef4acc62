from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from gamification.models import Challenge, Badge
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = 'Create interactive challenges for quiz and poll participation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing interactive challenges',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.reset_interactive_challenges()

        self.create_interactive_challenges()
        self.stdout.write(
            self.style.SUCCESS('✅ Successfully created interactive challenges!')
        )

    def reset_interactive_challenges(self):
        """Reset existing interactive challenges"""
        self.stdout.write('🔄 Resetting existing interactive challenges...')
        
        Challenge.objects.filter(
            challenge_type__in=['quiz', 'poll', 'interactive']
        ).delete()
        
        self.stdout.write('  ✅ Reset completed')

    def create_interactive_challenges(self):
        """Create comprehensive interactive challenges"""
        self.stdout.write('🎯 Creating interactive challenges...')
        
        now = timezone.now()
        
        # Quiz Challenges
        quiz_challenges = [
            {
                'title': 'Quiz Novice',
                'description': 'Complete your first 3 quizzes to get started!',
                'challenge_type': 'quiz',
                'difficulty': 'easy',
                'requirements': {'quizzes_completed': 3},
                'points_reward': 100,
                'start_date': now,
                'end_date': now + timedelta(days=30),
                'duration_days': 30,
                'is_featured': True,
            },
            {
                'title': 'Quiz Enthusiast',
                'description': 'Complete 10 quizzes this week and show your knowledge!',
                'challenge_type': 'quiz',
                'difficulty': 'medium',
                'requirements': {'quizzes_completed': 10},
                'points_reward': 250,
                'start_date': now,
                'end_date': now + timedelta(days=7),
                'duration_days': 7,
                'is_featured': True,
            },
            {
                'title': 'Quiz Master',
                'description': 'Complete 25 quizzes this month and become a true quiz master!',
                'challenge_type': 'quiz',
                'difficulty': 'hard',
                'requirements': {'quizzes_completed': 25},
                'points_reward': 500,
                'start_date': now,
                'end_date': now + timedelta(days=30),
                'duration_days': 30,
                'is_featured': False,
            },
            {
                'title': 'Quiz Champion',
                'description': 'Complete 50 quizzes and earn the ultimate quiz champion status!',
                'challenge_type': 'quiz',
                'difficulty': 'expert',
                'requirements': {'quizzes_completed': 50},
                'points_reward': 1000,
                'start_date': now,
                'end_date': now + timedelta(days=60),
                'duration_days': 60,
                'is_featured': False,
            },
        ]

        # Poll Challenges
        poll_challenges = [
            {
                'title': 'Voice Your Opinion',
                'description': 'Vote in your first 5 polls and make your voice heard!',
                'challenge_type': 'poll',
                'difficulty': 'easy',
                'requirements': {'polls_voted': 5},
                'points_reward': 75,
                'start_date': now,
                'end_date': now + timedelta(days=14),
                'duration_days': 14,
                'is_featured': True,
            },
            {
                'title': 'Poll Participant',
                'description': 'Participate in 15 polls this week and engage with the community!',
                'challenge_type': 'poll',
                'difficulty': 'medium',
                'requirements': {'polls_voted': 15},
                'points_reward': 200,
                'start_date': now,
                'end_date': now + timedelta(days=7),
                'duration_days': 7,
                'is_featured': True,
            },
            {
                'title': 'Poll Expert',
                'description': 'Vote in 40 polls this month and become a polling expert!',
                'challenge_type': 'poll',
                'difficulty': 'hard',
                'requirements': {'polls_voted': 40},
                'points_reward': 400,
                'start_date': now,
                'end_date': now + timedelta(days=30),
                'duration_days': 30,
                'is_featured': False,
            },
        ]

        # Interactive Challenges (Combined)
        interactive_challenges = [
            {
                'title': 'Interactive Explorer',
                'description': 'Complete 5 quizzes and vote in 10 polls to explore all interactive content!',
                'challenge_type': 'interactive',
                'difficulty': 'medium',
                'requirements': {
                    'quizzes_completed': 5,
                    'polls_voted': 10
                },
                'points_reward': 300,
                'start_date': now,
                'end_date': now + timedelta(days=14),
                'duration_days': 14,
                'is_featured': True,
            },
            {
                'title': 'Interactive Guru',
                'description': 'Complete 15 quizzes and vote in 25 polls to become an interactive content guru!',
                'challenge_type': 'interactive',
                'difficulty': 'hard',
                'requirements': {
                    'quizzes_completed': 15,
                    'polls_voted': 25
                },
                'points_reward': 750,
                'start_date': now,
                'end_date': now + timedelta(days=30),
                'duration_days': 30,
                'is_featured': True,
            },
            {
                'title': 'Interactive Legend',
                'description': 'Complete 30 quizzes and vote in 50 polls to achieve legendary status!',
                'challenge_type': 'interactive',
                'difficulty': 'expert',
                'requirements': {
                    'quizzes_completed': 30,
                    'polls_voted': 50
                },
                'points_reward': 1500,
                'start_date': now,
                'end_date': now + timedelta(days=60),
                'duration_days': 60,
                'is_featured': False,
            },
        ]

        # Weekly Recurring Challenges
        weekly_challenges = [
            {
                'title': 'Weekly Quiz Challenge',
                'description': 'Complete 5 quizzes this week for bonus points!',
                'challenge_type': 'quiz',
                'difficulty': 'easy',
                'requirements': {'quizzes_completed': 5},
                'points_reward': 150,
                'start_date': now,
                'end_date': now + timedelta(days=7),
                'duration_days': 7,
                'is_featured': True,
            },
            {
                'title': 'Weekly Poll Challenge',
                'description': 'Vote in 8 polls this week and earn bonus rewards!',
                'challenge_type': 'poll',
                'difficulty': 'easy',
                'requirements': {'polls_voted': 8},
                'points_reward': 120,
                'start_date': now,
                'end_date': now + timedelta(days=7),
                'duration_days': 7,
                'is_featured': True,
            },
        ]

        # Combine all challenges
        all_challenges = (
            quiz_challenges + 
            poll_challenges + 
            interactive_challenges + 
            weekly_challenges
        )

        # Create challenges
        for challenge_data in all_challenges:
            challenge, created = Challenge.objects.get_or_create(
                title=challenge_data['title'],
                defaults=challenge_data
            )
            
            if created:
                self.stdout.write(f'  ✅ Created challenge: {challenge.title}')
            else:
                self.stdout.write(f'  ⚠️  Challenge already exists: {challenge.title}')

        self.stdout.write(f'📊 Created {len(all_challenges)} interactive challenges')

    def create_interactive_badges(self):
        """Create badges for interactive achievements"""
        self.stdout.write('🏆 Creating interactive badges...')
        
        badges_data = [
            {
                'name': 'Quiz Starter',
                'description': 'Completed your first quiz',
                'badge_type': 'achievement',
                'rarity': 'common',
                'points_reward': 25,
                'requirements': {'quizzes_completed': 1},
                'icon': 'quiz',
            },
            {
                'name': 'Poll Participant',
                'description': 'Voted in your first poll',
                'badge_type': 'achievement',
                'rarity': 'common',
                'points_reward': 20,
                'requirements': {'polls_voted': 1},
                'icon': 'poll',
            },
            {
                'name': 'Quiz Expert',
                'description': 'Completed 20 quizzes',
                'badge_type': 'achievement',
                'rarity': 'rare',
                'points_reward': 100,
                'requirements': {'quizzes_completed': 20},
                'icon': 'quiz_expert',
            },
            {
                'name': 'Poll Master',
                'description': 'Voted in 30 polls',
                'badge_type': 'achievement',
                'rarity': 'rare',
                'points_reward': 80,
                'requirements': {'polls_voted': 30},
                'icon': 'poll_master',
            },
            {
                'name': 'Interactive Legend',
                'description': 'Master of both quizzes and polls',
                'badge_type': 'achievement',
                'rarity': 'legendary',
                'points_reward': 250,
                'requirements': {
                    'quizzes_completed': 25,
                    'polls_voted': 35
                },
                'icon': 'interactive_legend',
            },
        ]

        for badge_data in badges_data:
            badge, created = Badge.objects.get_or_create(
                name=badge_data['name'],
                defaults=badge_data
            )
            
            if created:
                self.stdout.write(f'  ✅ Created badge: {badge.name}')
            else:
                self.stdout.write(f'  ⚠️  Badge already exists: {badge.name}')
