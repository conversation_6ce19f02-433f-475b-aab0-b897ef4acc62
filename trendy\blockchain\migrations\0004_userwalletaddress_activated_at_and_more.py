# Generated by Django 5.1.7 on 2025-06-24 11:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('blockchain', '0003_alter_blockchainnetwork_name'),
    ]

    operations = [
        migrations.AddField(
            model_name='userwalletaddress',
            name='activated_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userwalletaddress',
            name='activation_code',
            field=models.CharField(blank=True, max_length=6, null=True),
        ),
        migrations.AddField(
            model_name='userwalletaddress',
            name='activation_code_expires',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userwalletaddress',
            name='is_active',
            field=models.BooleanField(default=False),
        ),
    ]
