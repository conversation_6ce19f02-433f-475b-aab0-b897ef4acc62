from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsA<PERSON><PERSON>icated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Count, Avg, Sum, F
from django.db import transaction
from blog.models import Post
from .models import VoiceComment, VoiceCommentLike, AIWritingSession, TextToSpeechRequest
from .serializers import (
    VoiceCommentSerializer, CreateVoiceCommentSerializer,
    AIWritingSessionSerializer, CreateAIWritingSessionSerializer, UpdateAIWritingSessionSerializer,
    TextToSpeechRequestSerializer, VoiceCommentStatsSerializer, AIWritingStatsSerializer
)

@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
def voice_comments_list(request, post_id):
    """Get voice comments for a post or create a new one"""
    try:
        post = get_object_or_404(Post, id=post_id)

        if request.method == 'GET':
            # Get voice comments for the post
            comments = VoiceComment.objects.filter(
                post=post,
                is_active=True,
                is_approved=True
            ).select_related('user').order_by('-created_at')

            serializer = VoiceCommentSerializer(
                comments,
                many=True,
                context={'request': request}
            )
            return Response(serializer.data)

        elif request.method == 'POST':
            # Create new voice comment
            if not request.user.is_authenticated:
                return Response({
                    'error': 'Authentication required to post voice comments'
                }, status=status.HTTP_401_UNAUTHORIZED)

            serializer = CreateVoiceCommentSerializer(
                data=request.data,
                context={'request': request}
            )

            if serializer.is_valid():
                voice_comment = serializer.save()

                # Return the created comment
                response_serializer = VoiceCommentSerializer(
                    voice_comment,
                    context={'request': request}
                )
                return Response(response_serializer.data, status=status.HTTP_201_CREATED)

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'DELETE'])
@permission_classes([IsAuthenticated])
def voice_comment_detail(request, comment_id):
    """Get or delete a specific voice comment"""
    try:
        comment = get_object_or_404(VoiceComment, id=comment_id)

        if request.method == 'GET':
            serializer = VoiceCommentSerializer(
                comment,
                context={'request': request}
            )
            return Response(serializer.data)

        elif request.method == 'DELETE':
            # Check if user owns the comment or is staff
            if comment.user != request.user and not request.user.is_staff:
                return Response({
                    'error': 'Permission denied'
                }, status=status.HTTP_403_FORBIDDEN)

            comment.delete()
            return Response({
                'message': 'Voice comment deleted successfully'
            })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def toggle_voice_comment_like(request, comment_id):
    """Like or unlike a voice comment"""
    try:
        comment = get_object_or_404(VoiceComment, id=comment_id)

        like, created = VoiceCommentLike.objects.get_or_create(
            voice_comment=comment,
            user=request.user
        )

        if not created:
            # Unlike
            like.delete()
            with transaction.atomic():
                comment.like_count = F('like_count') - 1
                comment.save(update_fields=['like_count'])

            return Response({
                'message': 'Voice comment unliked',
                'liked': False,
                'like_count': comment.like_count
            })
        else:
            # Like
            with transaction.atomic():
                comment.like_count = F('like_count') + 1
                comment.save(update_fields=['like_count'])

            return Response({
                'message': 'Voice comment liked',
                'liked': True,
                'like_count': comment.like_count
            })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_ai_writing_session(request):
    """Create a new AI writing session"""
    try:
        serializer = CreateAIWritingSessionSerializer(
            data=request.data,
            context={'request': request}
        )

        if serializer.is_valid():
            session = serializer.save()
            response_serializer = AIWritingSessionSerializer(session)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_ai_writing_session(request, session_id):
    """Update an AI writing session"""
    try:
        session = get_object_or_404(
            AIWritingSession,
            session_id=session_id,
            user=request.user
        )

        serializer = UpdateAIWritingSessionSerializer(
            session,
            data=request.data,
            partial=True
        )

        if serializer.is_valid():
            session = serializer.save()
            response_serializer = AIWritingSessionSerializer(session)
            return Response(response_serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([AllowAny])
def track_tts_request(request, post_id):
    """Track text-to-speech usage"""
    try:
        post = get_object_or_404(Post, id=post_id)

        serializer = TextToSpeechRequestSerializer(
            data=request.data,
            context={'request': request}
        )

        if serializer.is_valid():
            tts_request = serializer.save(post=post)
            return Response({
                'message': 'TTS request tracked',
                'id': tts_request.id
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def voice_comment_stats(request, post_id=None):
    """Get voice comment statistics"""
    try:
        if post_id:
            # Stats for specific post
            post = get_object_or_404(Post, id=post_id)
            comments = VoiceComment.objects.filter(post=post, is_active=True)
        else:
            # Global stats (staff only)
            if not request.user.is_staff:
                return Response({
                    'error': 'Permission denied'
                }, status=status.HTTP_403_FORBIDDEN)
            comments = VoiceComment.objects.filter(is_active=True)

        total_comments = comments.count()
        total_duration = comments.aggregate(Sum('duration_seconds'))['duration_seconds__sum'] or 0
        average_duration = comments.aggregate(Avg('duration_seconds'))['duration_seconds__avg'] or 0
        transcribed_count = comments.filter(is_transcribed=True).count()
        transcription_rate = (transcribed_count / total_comments * 100) if total_comments > 0 else 0

        # Top contributors
        top_contributors = comments.values('user__username').annotate(
            comment_count=Count('id'),
            total_duration=Sum('duration_seconds')
        ).order_by('-comment_count')[:5]

        stats = {
            'total_comments': total_comments,
            'total_duration': total_duration,
            'average_duration': round(average_duration, 2),
            'transcribed_count': transcribed_count,
            'transcription_rate': round(transcription_rate, 2),
            'top_contributors': list(top_contributors)
        }

        serializer = VoiceCommentStatsSerializer(stats)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def ai_writing_stats(request):
    """Get AI writing assistance statistics"""
    try:
        if not request.user.is_staff:
            # User's personal stats
            sessions = AIWritingSession.objects.filter(user=request.user)
        else:
            # Global stats for staff
            sessions = AIWritingSession.objects.all()

        total_sessions = sessions.count()
        total_suggestions = sessions.aggregate(Sum('suggestions_count'))['suggestions_count__sum'] or 0
        total_accepted = sessions.aggregate(Sum('suggestions_accepted'))['suggestions_accepted__sum'] or 0
        acceptance_rate = (total_accepted / total_suggestions * 100) if total_suggestions > 0 else 0

        avg_duration = sessions.aggregate(Avg('session_duration'))['session_duration__avg'] or 0
        total_words = sessions.aggregate(Sum('words_added'))['words_added__sum'] or 0
        avg_readability = sessions.aggregate(Avg('readability_improvement'))['readability_improvement__avg'] or 0

        stats = {
            'total_sessions': total_sessions,
            'total_suggestions': total_suggestions,
            'acceptance_rate': round(acceptance_rate, 2),
            'average_session_duration': round(avg_duration, 2),
            'total_words_added': total_words,
            'average_readability_improvement': round(avg_readability, 2)
        }

        serializer = AIWritingStatsSerializer(stats)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
