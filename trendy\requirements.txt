aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
asgiref==3.8.1
async-timeout==5.0.1
attrs==25.3.0
bitarray==3.4.3
certifi==2025.6.15
cffi==1.17.1
charset-normalizer==3.4.2
cmudict==1.0.32
colorama==0.4.6
crispy-bootstrap5==2024.10
cryptography==41.0.7
cytoolz==1.0.1
distro==1.9.0
dj-database-url==2.1.0
Django==4.2.23
django-cors-headers==4.7.0
django-crispy-forms==2.3
django-environ==0.12.0
django-ratelimit==4.1.0
djangorestframework==3.15.2
drf-spectacular==0.28.0
eth-account==0.9.0
eth-hash==0.7.1
eth-keyfile==0.9.1
eth-keys==0.7.0
eth-rlp==1.0.1
eth-typing==5.2.1
eth-utils==2.2.2
eth_abi==5.2.0
exceptiongroup==1.3.0
frozenlist==1.7.0
gunicorn==21.2.0
h11==0.16.0
hexbytes==0.3.1
httpcore==1.0.9
httpx==0.28.1
idna==3.10
importlib_metadata==8.7.0
importlib_resources==6.5.2
inflection==0.5.1
jiter==0.10.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
lru-dict==1.2.0
multidict==6.5.0
numpy==2.0.2
openai==1.98.0
opencv-python==4.11.0.86
packaging==25.0
parsimonious==0.10.0
pillow==11.1.0
propcache==0.3.2
protobuf==6.31.1
psycopg2-binary==2.9.10
py-ecc==8.0.0
py-solc-x==2.0.4
pycparser==2.22
pycryptodome==3.23.0
pydantic==2.11.7
pydantic_core==2.33.2
pyphen==0.17.2
python-dotenv==1.1.0
pyunormalize==16.0.0
pywin32==306; sys_platform == "win32"
PyYAML==6.0.2
redis==5.2.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
rlp==4.1.0
rpds-py==0.23.1
sniffio==1.3.1
sqlparse==0.5.3
textstat==0.7.7
toolz==1.0.0
tqdm==4.67.1
typing-inspection==0.4.1
typing_extensions==4.12.2
tzdata==2025.1
uritemplate==4.1.1
urllib3==2.5.0
web3==6.11.3
websockets==15.0.1
whitenoise==6.6.0
yarl==1.20.1
zipp==3.23.0
